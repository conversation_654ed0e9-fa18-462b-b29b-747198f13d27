# SNOMED CT to OMOP Standard Concepts Mapping

## Overview

SNOMED CT (Systematized Nomenclature of Medicine - Clinical Terms) is a comprehensive clinical terminology that provides clinical content and expressivity for clinical documentation and reporting. In the OMOP Common Data Model, SNOMED CT is the primary standard vocabulary for the Condition domain and is also used in other domains. This document outlines the strategies and approaches for mapping SNOMED CT codes from FHIR resources to OMOP standard concepts.

## SNOMED CT in OMOP CDM

In the OMOP Common Data Model:
- SNOMED CT is the preferred standard vocabulary for conditions (diagnoses)
- SNOMED CT concepts are stored in the CONCEPT table with vocabulary_id = 'SNOMED'
- Most standard concepts in the Condition domain are sourced from SNOMED CT
- SNOMED CT is also used for standard concepts in other domains like Observation, Procedure, and Measurement

## Mapping Process

The process of mapping SNOMED CT codes from FHIR resources to OMOP standard concepts involves several steps:

1. **Extract SNOMED CT Code from FHIR Resource**: 
   - In FHIR, SNOMED CT codes are typically found in resources like Condition, Procedure, or Observation in the `code.coding` element where `system = "http://snomed.info/sct"`.

2. **Look Up SNOMED CT Code in OMOP Vocabulary**:
   - Use the CONCEPT table to find the corresponding concept_id for the SNOMED CT code.
   - Query the CONCEPT table where concept_code = [SNOMED CT code] and vocabulary_id = 'SNOMED'.

3. **Determine Standard Concept Status**:
   - Check if the found concept is a standard concept (standard_concept = 'S').
   - Most SNOMED CT concepts used in OMOP are already standard concepts, but some may not be.
   - If it is not a standard concept, use the CONCEPT_RELATIONSHIP table to find the standard concept it maps to.

4. **Assign to Appropriate Domain**:
   - Based on the domain_id of the standard concept, determine which OMOP table the data should be stored in (e.g., CONDITION_OCCURRENCE, PROCEDURE_OCCURRENCE, OBSERVATION).

5. **Populate OMOP Fields**:
   - Set the appropriate concept_id field (e.g., condition_concept_id) to the standard concept_id.
   - Set the source_value field (e.g., condition_source_value) to the original SNOMED CT code.
   - Set the source_concept_id field (e.g., condition_source_concept_id) to the source concept_id (if different from the standard concept_id).

## SQL Code Examples

### Example 1: Finding OMOP Concept ID for a SNOMED CT Code

```sql
-- Find the OMOP concept_id for SNOMED CT code '44054006' (Type 2 diabetes mellitus)
SELECT 
    concept_id,
    concept_name,
    domain_id,
    vocabulary_id,
    concept_code,
    standard_concept
FROM 
    concept
WHERE 
    concept_code = '44054006'
    AND vocabulary_id = 'SNOMED';
```

### Example 2: Finding Standard Concept for Non-Standard SNOMED CT Concept

```sql
-- If the SNOMED CT concept is not standard, find its standard mapping
SELECT 
    c1.concept_id AS source_concept_id,
    c1.concept_name AS source_concept_name,
    c2.concept_id AS standard_concept_id,
    c2.concept_name AS standard_concept_name,
    c2.domain_id,
    c2.vocabulary_id
FROM 
    concept c1
JOIN 
    concept_relationship cr ON c1.concept_id = cr.concept_id_1
JOIN 
    concept c2 ON cr.concept_id_2 = c2.concept_id
WHERE 
    c1.concept_code = '44054006'
    AND c1.vocabulary_id = 'SNOMED'
    AND cr.relationship_id = 'Maps to'
    AND c2.standard_concept = 'S';
```

### Example 3: Complete FHIR Condition to OMOP Condition_Occurrence Mapping

```sql
-- Pseudocode for mapping FHIR Condition with SNOMED CT code to OMOP Condition_Occurrence
INSERT INTO condition_occurrence (
    person_id,
    condition_concept_id,
    condition_start_date,
    condition_start_datetime,
    condition_type_concept_id,
    condition_source_value,
    condition_source_concept_id
)
SELECT
    person_id, -- Mapped from FHIR Condition.subject reference
    standard_concept_id, -- From the mapping query above
    condition_date, -- From FHIR Condition.onsetDateTime
    condition_datetime, -- From FHIR Condition.onsetDateTime
    type_concept_id, -- Based on provenance of the condition
    snomed_code, -- Original SNOMED CT code from FHIR Condition.code.coding.code
    source_concept_id -- From the initial concept lookup
FROM
    fhir_condition_staging;
```

## Mapping from Other Vocabularies to SNOMED CT

One of the key advantages of using SNOMED CT as the standard vocabulary in OMOP is that many other vocabularies (like ICD-9-CM, ICD-10-CM, etc.) have established mappings to SNOMED CT. This allows for standardization across different source data.

### ICD-9-CM and ICD-10-CM to SNOMED CT Mapping

OHDSI provides mappings from ICD-9-CM and ICD-10-CM to SNOMED CT, which are used to standardize condition data in the OMOP CDM. These mappings are based on:

1. National Library of Medicine's Unified Medical Language System Metathesaurus Mapping Project
2. United Kingdom National Health Service Terminology Service mappings
3. Additional mappings created by OHDSI's vocabulary team

Example of ICD-9-CM to SNOMED CT mapping:
- ICD-9-CM 410.00 "Acute myocardial infarction of anterolateral wall, episode of care unspecified" maps to SNOMED CT 70211005 "Acute myocardial infarction of anterolateral wall"

Example of ICD-10-CM to SNOMED CT mapping:
- ICD-10-CM E11.9 "Type 2 diabetes mellitus without complications" maps to SNOMED CT 44054006 "Type 2 diabetes mellitus"

## Python Code Example for FHIR to OMOP SNOMED CT Mapping

```python
import pandas as pd
import sqlalchemy as sa

# Connect to the OMOP database
engine = sa.create_engine('postgresql://username:password@localhost:5432/omop_cdm')

def map_snomed_to_omop(snomed_code):
    """
    Map a SNOMED CT code to its corresponding OMOP standard concept
    
    Args:
        snomed_code (str): The SNOMED CT code to map
        
    Returns:
        dict: A dictionary containing the mapping information
    """
    # Query to find the concept and its standard mapping if needed
    query = """
    WITH source_concept AS (
        SELECT 
            concept_id,
            concept_name,
            domain_id,
            standard_concept
        FROM 
            concept
        WHERE 
            concept_code = :snomed_code
            AND vocabulary_id = 'SNOMED'
    )
    SELECT 
        sc.concept_id AS source_concept_id,
        sc.concept_name AS source_concept_name,
        CASE 
            WHEN sc.standard_concept = 'S' THEN sc.concept_id
            ELSE c2.concept_id 
        END AS standard_concept_id,
        CASE 
            WHEN sc.standard_concept = 'S' THEN sc.concept_name
            ELSE c2.concept_name 
        END AS standard_concept_name,
        CASE 
            WHEN sc.standard_concept = 'S' THEN sc.domain_id
            ELSE c2.domain_id 
        END AS domain_id
    FROM 
        source_concept sc
    LEFT JOIN 
        concept_relationship cr ON sc.concept_id = cr.concept_id_1 AND cr.relationship_id = 'Maps to'
    LEFT JOIN 
        concept c2 ON cr.concept_id_2 = c2.concept_id AND c2.standard_concept = 'S'
    """
    
    with engine.connect() as conn:
        result = conn.execute(sa.text(query), {"snomed_code": snomed_code}).fetchone()
    
    if result is None:
        return {"error": f"SNOMED CT code {snomed_code} not found in OMOP vocabulary"}
    
    return {
        "source_concept_id": result.source_concept_id,
        "source_concept_name": result.source_concept_name,
        "standard_concept_id": result.standard_concept_id,
        "standard_concept_name": result.standard_concept_name,
        "domain_id": result.domain_id
    }

def process_fhir_condition(fhir_condition_json):
    """
    Process a FHIR Condition resource and map its SNOMED CT code to OMOP
    
    Args:
        fhir_condition_json (dict): The FHIR Condition resource as a JSON object
        
    Returns:
        dict: The data ready to be inserted into the appropriate OMOP table
    """
    # Extract the SNOMED CT code from the FHIR Condition
    snomed_code = None
    for coding in fhir_condition_json.get("code", {}).get("coding", []):
        if coding.get("system") == "http://snomed.info/sct":
            snomed_code = coding.get("code")
            break
    
    if not snomed_code:
        return {"error": "No SNOMED CT code found in the FHIR Condition"}
    
    # Map the SNOMED CT code to OMOP
    mapping = map_snomed_to_omop(snomed_code)
    if "error" in mapping:
        return mapping
    
    # Determine the appropriate OMOP table based on the domain
    omop_table = "condition_occurrence" if mapping["domain_id"] == "Condition" else "observation"
    
    # Extract other relevant data from the FHIR Condition
    # (This is simplified and would need to be expanded for a real implementation)
    person_id = extract_person_id(fhir_condition_json.get("subject", {}).get("reference"))
    condition_datetime = fhir_condition_json.get("onsetDateTime")
    
    # Prepare the data for insertion into the appropriate OMOP table
    if omop_table == "condition_occurrence":
        omop_data = {
            "person_id": person_id,
            "condition_concept_id": mapping["standard_concept_id"],
            "condition_start_date": condition_datetime.split("T")[0] if condition_datetime else None,
            "condition_start_datetime": condition_datetime,
            "condition_type_concept_id": 32879,  # EHR
            "condition_source_value": snomed_code,
            "condition_source_concept_id": mapping["source_concept_id"]
        }
    else:
        omop_data = {
            "person_id": person_id,
            "observation_concept_id": mapping["standard_concept_id"],
            "observation_date": condition_datetime.split("T")[0] if condition_datetime else None,
            "observation_datetime": condition_datetime,
            "observation_type_concept_id": 32879,  # EHR
            "observation_source_value": snomed_code,
            "observation_source_concept_id": mapping["source_concept_id"]
        }
    
    return {
        "table": omop_table,
        "data": omop_data
    }

# Helper function (would need to be implemented)
def extract_person_id(subject_reference):
    # Extract person_id from FHIR reference (e.g., "Patient/123")
    if subject_reference and subject_reference.startswith("Patient/"):
        return subject_reference.split("/")[1]
    return None
```

## Mapping Strategies and Considerations

### 1. Direct Mapping

Most SNOMED CT codes used in FHIR resources can be directly mapped to OMOP standard concepts. This is the simplest case and works well for most clinical concepts.

### 2. Domain Assignment

SNOMED CT concepts in OMOP are assigned to specific domains (Condition, Procedure, Observation, etc.). When mapping FHIR resources to OMOP, it's important to check the domain of the mapped concept to ensure it's placed in the correct OMOP table.

### 3. Handling Non-Standard SNOMED CT Concepts

Some SNOMED CT concepts may not be standard concepts in OMOP. In these cases:
- Use the CONCEPT_RELATIONSHIP table to find the standard concept that the SNOMED CT code maps to
- If no standard mapping exists, consider using the closest hierarchical ancestor that is a standard concept

### 4. Mapping from Other Vocabularies to SNOMED CT

When dealing with FHIR resources that use vocabularies other than SNOMED CT:
1. First map the source code to its corresponding concept in the source vocabulary
2. Then use the established mappings to find the equivalent SNOMED CT standard concept

### 5. Handling Post-Coordinated Expressions

SNOMED CT allows for post-coordinated expressions (combining multiple concepts). These are challenging to map to OMOP:
- Consider breaking down post-coordinated expressions into their component concepts
- Map each component separately and use the most specific applicable concept as the primary mapping

## Research Findings on SNOMED CT to OMOP Mapping

Research by Hripcsak et al. (2018) on the effect of vocabulary mapping for conditions on phenotype cohorts found:

1. The translation of data from source vocabularies (ICD-9-CM, ICD-10-CM) to SNOMED CT resulted in very small error rates (0.15-0.26%)
2. These error rates were an order of magnitude smaller than other error sources in observational research
3. It is possible to map diagnoses from disparate vocabularies to SNOMED CT and carry out research using a single set of definitions

This research supports the OHDSI approach of using SNOMED CT as the standard vocabulary for conditions in the OMOP CDM.

## Best Practices for SNOMED CT to OMOP Mapping

1. **Use Athena for Vocabulary Lookup**:
   - Athena (https://athena.ohdsi.org) is the official OHDSI tool for browsing and downloading the standardized vocabularies
   - Use it to look up SNOMED CT codes and understand their mappings

2. **Preserve Source Values**:
   - Always preserve the original SNOMED CT code in the source_value field for traceability

3. **Handle Domain Crossover**:
   - Some SNOMED CT concepts may map to domains different from what might be expected based on the FHIR resource type
   - Always check the domain_id of the mapped concept and route the data to the appropriate OMOP table

4. **Validate Mappings**:
   - Regularly validate that your mappings are correct by comparing the meaning of the SNOMED CT term with the mapped OMOP concept

5. **Stay Updated with Vocabulary Releases**:
   - OMOP vocabularies are updated regularly. Ensure your system uses the latest version to have the most comprehensive SNOMED CT coverage

6. **Mapping Strategy Selection**:
   - When mapping a new vocabulary to OMOP, consider these approaches (in order of preference):
     a) Map code to the closest SNOMED CT concept even if losing some information
     b) Try to map to SNOMED CT unless there is a more specific match in other standard vocabularies
     c) Map to the best matching concept regardless of vocabulary

## References

1. Hripcsak, G., Levine, M. E., Shang, N., & Ryan, P. B. (2018). Effect of vocabulary mapping for conditions on phenotype cohorts. Journal of the American Medical Informatics Association, 25(12), 1618-1625. https://doi.org/10.1093/jamia/ocy124
2. OHDSI. "The Book of OHDSI: Chapter 5 Standardized Vocabularies." https://ohdsi.github.io/TheBookOfOhdsi/StandardizedVocabularies.html
3. OHDSI Forums. "Mapping a new vocabulary: is better to map to snomed or to other standard vocabularies?" https://forums.ohdsi.org/t/mapping-a-new-vocabulary-is-better-to-map-to-snomed-or-to-other-standard-vocabularies/11113
4. HL7. "FHIR Coded Source Data to OMOP Patterns." https://confluence.hl7.org/pages/viewpage.action?pageId=256517831
