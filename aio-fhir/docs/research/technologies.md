# Technologies for FHIR to OMOP Transformation

This technical document provides a comprehensive overview of cutting-edge technologies, libraries, frameworks, and methodologies used in FHIR to OMOP transformation pipelines. It covers Python libraries, SQL optimization techniques, cloud-based deployment options, and best practices for implementing efficient and scalable transformation pipelines.

## Table of Contents

1. [Python Libraries for FHIR Processing](#python-libraries-for-fhir-processing)
2. [Python Libraries for OMOP CDM](#python-libraries-for-omop-cdm)
3. [Database Technologies and SQL Optimization](#database-technologies-and-sql-optimization)
4. [ETL Frameworks and Orchestration Tools](#etl-frameworks-and-orchestration-tools)
5. [Terminology Services and Vocabulary Mapping](#terminology-services-and-vocabulary-mapping)
6. [Cloud-Based Deployment Options](#cloud-based-deployment-options)
7. [Monitoring and Logging Solutions](#monitoring-and-logging-solutions)
8. [Testing and Validation Frameworks](#testing-and-validation-frameworks)
9. [Performance Optimization Techniques](#performance-optimization-techniques)
10. [Security and Compliance Technologies](#security-and-compliance-technologies)

## Python Libraries for FHIR Processing

### FHIR Client Libraries

#### 1. **fhir.resources**

The `fhir.resources` library provides Python classes for FHIR resources, enabling type-safe handling of FHIR data.

```python
from fhir.resources.patient import Patient
from fhir.resources.humanname import HumanName

# Create a FHIR Patient resource
patient = Patient(
    id="example",
    active=True,
    name=[
        HumanName(
            family="Smith",
            given=["John"]
        )
    ],
    gender="male"
)

# Access attributes in a type-safe manner
family_name = patient.name[0].family
```

**Key Features:**
- Type validation for FHIR resources
- Support for FHIR R4 (4.0.1)
- Conversion between JSON and Python objects
- Extensible for custom resource types

#### 2. **fhirclient**

The `fhirclient` library provides a client for interacting with FHIR servers.

```python
from fhirclient import client
from fhirclient.models.patient import Patient

# Connect to FHIR server
settings = {
    'app_id': 'my_app',
    'api_base': 'http://hapi.fhir.org/baseR4'
}
smart = client.FHIRClient(settings=settings)

# Search for patients
search = Patient.where(struct={'name': 'Smith'})
patients = search.perform_resources(smart.server)
for patient in patients:
    print(f"Patient ID: {patient.id}")
    print(f"Name: {patient.name[0].family}")
```

**Key Features:**
- RESTful API client for FHIR servers
- Support for search operations
- Handling of FHIR resource references
- Authentication support

#### 3. **fhir-parser**

The `fhir-parser` library generates Python classes from FHIR specification.

```python
# This is a code generation tool, not used at runtime
# Example of generated code usage:
from models.patient import Patient
from models.humanname import HumanName

patient = Patient({'id': '123', 'active': True})
patient.name = [HumanName({'family': 'Smith', 'given': ['John']})]
```

**Key Features:**
- Generates Python classes from FHIR specification
- Customizable templates for code generation
- Support for multiple FHIR versions

### FHIR Data Processing

#### 1. **pandas**

The `pandas` library is essential for data manipulation and transformation in FHIR to OMOP pipelines.

```python
import pandas as pd

# Convert list of FHIR Patient resources to DataFrame
patients_data = []
for patient in fhir_patients:
    patients_data.append({
        'id': patient.get('id'),
        'gender': patient.get('gender'),
        'birth_date': patient.get('birthDate')
    })

patients_df = pd.DataFrame(patients_data)

# Perform transformations
patients_df['year_of_birth'] = pd.to_datetime(patients_df['birth_date']).dt.year
```

**Key Features:**
- Efficient data structures for tabular data
- Powerful data manipulation capabilities
- Integration with SQL databases
- Support for various file formats

#### 2. **numpy**

The `numpy` library provides support for numerical operations, which is useful for processing measurement values.

```python
import numpy as np

# Convert string values to numeric, handling missing values
measurement_values = np.array(['123.4', '98.6', 'None', '72'], dtype=object)
numeric_values = np.where(np.char.isalpha(measurement_values), np.nan, 
                         measurement_values.astype(float))

# Calculate statistics
mean_value = np.nanmean(numeric_values)
std_dev = np.nanstd(numeric_values)
```

**Key Features:**
- Efficient numerical operations
- Support for missing data
- Vectorized operations for performance
- Integration with pandas

#### 3. **requests**

The `requests` library is used for HTTP communication with FHIR servers.

```python
import requests

# Fetch FHIR resources from server
response = requests.get(
    "http://hapi.fhir.org/baseR4/Patient",
    params={"family": "Smith", "_count": 100},
    headers={"Accept": "application/fhir+json"}
)

if response.status_code == 200:
    bundle = response.json()
    patients = [entry["resource"] for entry in bundle.get("entry", [])]
```

**Key Features:**
- Simple API for HTTP requests
- Support for headers, parameters, and authentication
- JSON parsing
- Session management

## Python Libraries for OMOP CDM

### 1. **SQLAlchemy**

SQLAlchemy is a powerful ORM and SQL toolkit that provides a high-level interface for database operations.

```python
from sqlalchemy import create_engine, Column, Integer, String, ForeignKey, Date
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship

Base = declarative_base()

# Define OMOP CDM tables as Python classes
class Person(Base):
    __tablename__ = 'person'
    
    person_id = Column(Integer, primary_key=True)
    gender_concept_id = Column(Integer, nullable=False)
    year_of_birth = Column(Integer, nullable=False)
    month_of_birth = Column(Integer)
    day_of_birth = Column(Integer)
    birth_datetime = Column(Date)
    race_concept_id = Column(Integer, nullable=False)
    ethnicity_concept_id = Column(Integer, nullable=False)
    person_source_value = Column(String)
    
    # Relationships
    conditions = relationship("ConditionOccurrence", back_populates="person")
    observations = relationship("Observation", back_populates="person")

# Connect to database
engine = create_engine('postgresql://username:password@localhost:5432/omop_cdm')
Session = sessionmaker(bind=engine)
session = Session()

# Query data
persons = session.query(Person).filter(Person.year_of_birth > 1980).all()
```

**Key Features:**
- Object-Relational Mapping (ORM)
- SQL expression language
- Connection pooling
- Transaction management
- Support for multiple database backends

### 2. **psycopg2**

The `psycopg2` library is a PostgreSQL adapter for Python, providing low-level database access.

```python
import psycopg2
import psycopg2.extras

# Connect to PostgreSQL database
conn = psycopg2.connect(
    host="localhost",
    database="omop_cdm",
    user="username",
    password="password"
)

# Execute query with parameters
with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
    cur.execute("""
        SELECT person_id, gender_concept_id, year_of_birth
        FROM person
        WHERE year_of_birth > %s
    """, (1980,))
    
    for row in cur:
        print(f"Person ID: {row['person_id']}, Gender: {row['gender_concept_id']}")
```

**Key Features:**
- Native PostgreSQL adapter
- Support for PostgreSQL-specific features
- Efficient data transfer
- Connection pooling
- Transaction management

### 3. **OHDSI Python Tools**

#### a. **OHDSI/CommonDataModel**

The OHDSI CommonDataModel repository provides SQL scripts for creating OMOP CDM tables, which can be executed from Python.

```python
import os
import subprocess

# Execute OMOP CDM DDL script
def create_omop_schema(db_name, user, password, host, port, schema_version="5.4"):
    script_path = f"OMOP_CDM_postgresql_v{schema_version}.sql"
    
    # Execute SQL script using psql
    cmd = [
        "psql",
        f"-h {host}",
        f"-p {port}",
        f"-U {user}",
        f"-d {db_name}",
        f"-f {script_path}"
    ]
    
    env = os.environ.copy()
    env["PGPASSWORD"] = password
    
    subprocess.run(" ".join(cmd), shell=True, env=env, check=True)
```

#### b. **OHDSI/ETL-Synthea**

The ETL-Synthea repository provides Python scripts for transforming Synthea data to OMOP CDM, which can be adapted for FHIR.

```python
# Adapted from OHDSI/ETL-Synthea
def map_gender(gender):
    gender_map = {
        'M': 8507,  # MALE
        'F': 8532,  # FEMALE
        'MALE': 8507,
        'FEMALE': 8532
    }
    return gender_map.get(gender.upper(), 0)
```

## Database Technologies and SQL Optimization

### 1. **PostgreSQL Optimization Techniques**

#### a. **Indexing Strategies**

```sql
-- Create indexes for frequently queried columns
CREATE INDEX idx_person_year_of_birth ON person(year_of_birth);
CREATE INDEX idx_condition_occurrence_person_id ON condition_occurrence(person_id);
CREATE INDEX idx_observation_person_id ON observation(person_id);

-- Create composite indexes for common query patterns
CREATE INDEX idx_condition_person_date ON condition_occurrence(person_id, condition_start_date);

-- Create functional indexes for transformed columns
CREATE INDEX idx_person_birth_year_month ON person(year_of_birth, month_of_birth);
```

#### b. **Partitioning Large Tables**

```sql
-- Create partitioned table for observation
CREATE TABLE observation (
    observation_id BIGINT NOT NULL,
    person_id BIGINT NOT NULL,
    observation_concept_id INTEGER NOT NULL,
    observation_date DATE NOT NULL,
    -- other columns
) PARTITION BY RANGE (observation_date);

-- Create partitions by year
CREATE TABLE observation_2020 PARTITION OF observation
    FOR VALUES FROM ('2020-01-01') TO ('2021-01-01');
CREATE TABLE observation_2021 PARTITION OF observation
    FOR VALUES FROM ('2021-01-01') TO ('2022-01-01');
CREATE TABLE observation_2022 PARTITION OF observation
    FOR VALUES FROM ('2022-01-01') TO ('2023-01-01');
```

#### c. **Bulk Loading Techniques**

```python
import pandas as pd
from sqlalchemy import create_engine
from io import StringIO
import psycopg2

# Method 1: Using pandas to_sql with method='multi'
def bulk_load_pandas(df, table_name, engine):
    df.to_sql(table_name, engine, if_exists='append', index=False, method='multi')

# Method 2: Using PostgreSQL COPY command
def bulk_load_copy(df, table_name, conn):
    # Create a buffer
    buffer = StringIO()
    df.to_csv(buffer, index=False, header=False, sep='\t')
    buffer.seek(0)
    
    # Create a cursor and use copy_from
    cursor = conn.cursor()
    cursor.copy_from(buffer, table_name, sep='\t', null='')
    conn.commit()
    cursor.close()
```

### 2. **SQL Query Optimization**

#### a. **Efficient Joins**

```sql
-- Use INNER JOIN when both tables must have matching rows
SELECT p.person_id, p.year_of_birth, c.condition_concept_id
FROM person p
INNER JOIN condition_occurrence c ON p.person_id = c.person_id
WHERE p.year_of_birth > 1980;

-- Use LEFT JOIN when preserving rows from the left table
SELECT p.person_id, p.year_of_birth, o.observation_concept_id
FROM person p
LEFT JOIN observation o ON p.person_id = o.person_id
WHERE p.year_of_birth > 1980;

-- Use EXISTS for checking existence without retrieving data
SELECT p.person_id, p.year_of_birth
FROM person p
WHERE EXISTS (
    SELECT 1
    FROM condition_occurrence c
    WHERE c.person_id = p.person_id
    AND c.condition_concept_id = 320128
);
```

#### b. **Common Table Expressions (CTEs)**

```sql
-- Use CTEs for complex queries
WITH patient_conditions AS (
    SELECT person_id, COUNT(*) as condition_count
    FROM condition_occurrence
    GROUP BY person_id
),
patient_observations AS (
    SELECT person_id, COUNT(*) as observation_count
    FROM observation
    GROUP BY person_id
)
SELECT p.person_id, p.year_of_birth, 
       COALESCE(pc.condition_count, 0) as condition_count,
       COALESCE(po.observation_count, 0) as observation_count
FROM person p
LEFT JOIN patient_conditions pc ON p.person_id = pc.person_id
LEFT JOIN patient_observations po ON p.person_id = po.person_id
WHERE p.year_of_birth > 1980;
```

#### c. **Window Functions**

```sql
-- Use window functions for analytics
SELECT person_id, condition_concept_id, condition_start_date,
       ROW_NUMBER() OVER (PARTITION BY person_id ORDER BY condition_start_date) as condition_seq,
       FIRST_VALUE(condition_concept_id) OVER (
           PARTITION BY person_id 
           ORDER BY condition_start_date
       ) as first_condition_concept_id
FROM condition_occurrence;
```

## ETL Frameworks and Orchestration Tools

### 1. **Apache Airflow**

Apache Airflow is a platform to programmatically author, schedule, and monitor workflows.

```python
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python_operator import PythonOperator

# Define default arguments
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'start_date': datetime(2023, 1, 1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

# Create DAG
dag = DAG(
    'fhir_to_omop_pipeline',
    default_args=default_args,
    description='ETL pipeline for FHIR to OMOP transformation',
    schedule_interval=timedelta(days=1),
    catchup=False
)

# Define tasks
def extract_patients():
    # Code to extract patients from FHIR server
    pass

def transform_patients():
    # Code to transform FHIR patients to OMOP persons
    pass

def load_persons():
    # Code to load OMOP persons into database
    pass

# Create task instances
extract_patients_task = PythonOperator(
    task_id='extract_patients',
    python_callable=extract_patients,
    dag=dag,
)

transform_patients_task = PythonOperator(
    task_id='transform_patients',
    python_callable=transform_patients,
    dag=dag,
)

load_persons_task = PythonOperator(
    task_id='load_persons',
    python_callable=load_persons,
    dag=dag,
)

# Define task dependencies
extract_patients_task >> transform_patients_task >> load_persons_task
```

**Key Features:**
- Directed Acyclic Graphs (DAGs) for workflow definition
- Extensible through operators and hooks
- Web UI for monitoring and management
- Scalable architecture
- Robust scheduling and retry mechanisms

### 2. **Apache NiFi**

Apache NiFi is a dataflow management system that enables data routing, transformation, and system mediation logic.

```
# NiFi is configured through a web UI, but here's a representation of a FHIR to OMOP flow:

GetHTTP (FHIR Server) -> EvaluateJsonPath (Extract FHIR Resources) -> 
SplitJson (Split Bundle into Resources) -> 
RouteOnAttribute (Route by Resource Type) -> 
[Patient Branch] -> ExecuteScript (Transform Patient to Person) -> PutSQL (Insert into Person table)
[Condition Branch] -> ExecuteScript (Transform Condition to Condition_Occurrence) -> PutSQL (Insert into Condition_Occurrence table)
```

**Key Features:**
- Visual dataflow management
- Data provenance tracking
- Configurable processors for various data operations
- Web-based user interface
- Scalable architecture

### 3. **Spring Batch**

Spring Batch is a lightweight, comprehensive batch framework designed to enable the development of robust batch applications.

```java
// Java code for Spring Batch job
@Configuration
@EnableBatchProcessing
public class FhirToOmopBatchConfig {

    @Autowired
    private JobBuilderFactory jobBuilderFactory;
    
    @Autowired
    private StepBuilderFactory stepBuilderFactory;
    
    @Bean
    public Job fhirToOmopJob() {
        return jobBuilderFactory.get("fhirToOmopJob")
                .start(extractPatientsStep())
                .next(transformPatientsStep())
                .next(loadPersonsStep())
                .build();
    }
    
    @Bean
    public Step extractPatientsStep() {
        return stepBuilderFactory.get("extractPatientsStep")
                .<String, FhirPatient>chunk(10)
                .reader(fhirPatientReader())
                .processor(fhirPatientProcessor())
                .writer(fhirPatientWriter())
                .build();
    }
    
    // Additional steps, readers, processors, and writers
}
```

**Key Features:**
- Transaction management
- Chunk-based processing
- Robust error handling
- Job restart capability
- Comprehensive metrics and statistics

### 4. **Luigi**

Luigi is a Python package that helps you build complex pipelines of batch jobs.

```python
import luigi
import pandas as pd

class ExtractPatients(luigi.Task):
    date = luigi.DateParameter()
    
    def output(self):
        return luigi.LocalTarget(f"data/patients_{self.date}.csv")
    
    def run(self):
        # Extract patients from FHIR server
        patients_data = []
        # ... extraction logic ...
        
        # Save to CSV
        df = pd.DataFrame(patients_data)
        df.to_csv(self.output().path, index=False)

class TransformPatients(luigi.Task):
    date = luigi.DateParameter()
    
    def requires(self):
        return ExtractPatients(date=self.date)
    
    def output(self):
        return luigi.LocalTarget(f"data/persons_{self.date}.csv")
    
    def run(self):
        # Load patients data
        patients_df = pd.read_csv(self.input().path)
        
        # Transform to OMOP persons
        # ... transformation logic ...
        
        # Save transformed data
        persons_df.to_csv(self.output().path, index=False)

class LoadPersons(luigi.Task):
    date = luigi.DateParameter()
    
    def requires(self):
        return TransformPatients(date=self.date)
    
    def output(self):
        return luigi.LocalTarget(f"data/load_report_{self.date}.txt")
    
    def run(self):
        # Load persons data
        persons_df = pd.read_csv(self.input().path)
        
        # Insert into database
        # ... loading logic ...
        
        # Write report
        with self.output().open('w') as f:
            f.write(f"Loaded {len(persons_df)} persons")

if __name__ == '__main__':
    luigi.run(['LoadPersons', '--date', '2023-01-01'])
```

**Key Features:**
- Dependency resolution
- Failure recovery
- Visualization of task execution
- Integration with various storage systems
- Parameterized tasks

## Terminology Services and Vocabulary Mapping

### 1. **FHIR Terminology Service APIs**

#### a. **HAPI FHIR Terminology Service**

```python
import requests
import json

def translate_code(code, source_system, target_system):
    """
    Translate a code from source system to target system using FHIR Terminology Service.
    """
    url = "http://hapi.fhir.org/baseR4/ConceptMap/$translate"
    
    params = {
        "code": code,
        "system": source_system,
        "targetsystem": target_system
    }
    
    response = requests.get(url, params=params)
    
    if response.status_code == 200:
        result = response.json()
        if result.get("parameter"):
            for param in result["parameter"]:
                if param.get("name") == "match" and param.get("part"):
                    for part in param["part"]:
                        if part.get("name") == "code":
                            return part.get("valueString")
    
    return None
```

#### b. **FHIR ValueSet Expansion**

```python
def expand_valueset(valueset_url):
    """
    Expand a ValueSet using FHIR Terminology Service.
    """
    url = "http://hapi.fhir.org/baseR4/ValueSet/$expand"
    
    params = {
        "url": valueset_url
    }
    
    response = requests.get(url, params=params)
    
    if response.status_code == 200:
        result = response.json()
        codes = []
        
        if "expansion" in result and "contains" in result["expansion"]:
            for item in result["expansion"]["contains"]:
                codes.append({
                    "system": item.get("system"),
                    "code": item.get("code"),
                    "display": item.get("display")
                })
        
        return codes
    
    return []
```

### 2. **OHDSI Athena API**

```python
def search_concept(query, domain=None, vocabulary_id=None):
    """
    Search for concepts in OHDSI Athena.
    """
    url = "https://athena.ohdsi.org/api/v1/concepts"
    
    params = {
        "query": query,
        "page": 1,
        "pageSize": 100,
        "boosts": "concept_name^2,concept_code^1",
        "standardConcept": "Standard"
    }
    
    if domain:
        params["domain"] = domain
    
    if vocabulary_id:
        params["vocabulary"] = vocabulary_id
    
    response = requests.get(url, params=params)
    
    if response.status_code == 200:
        result = response.json()
        return result.get("content", [])
    
    return []
```

### 3. **Usagi Integration**

```python
import subprocess
import os
import pandas as pd

def prepare_source_codes_for_usagi(codes_df, output_file):
    """
    Prepare source codes for mapping with Usagi.
    
    Args:
        codes_df: DataFrame with columns 'source_code', 'source_name', 'frequency'
        output_file: Path to save the CSV file for Usagi
    """
    # Ensure required columns exist
    required_cols = ['source_code', 'source_name', 'frequency']
    for col in required_cols:
        if col not in codes_df.columns:
            raise ValueError(f"Required column '{col}' not found in DataFrame")
    
    # Add additional columns required by Usagi
    if 'source_auto_concept_id' not in codes_df.columns:
        codes_df['source_auto_concept_id'] = ''
    
    if 'source_vocabulary_id' not in codes_df.columns:
        codes_df['source_vocabulary_id'] = ''
    
    if 'source_code_description' not in codes_df.columns:
        codes_df['source_code_description'] = ''
    
    if 'additional_info' not in codes_df.columns:
        codes_df['additional_info'] = ''
    
    # Select and order columns as expected by Usagi
    usagi_df = codes_df[['source_code', 'source_name', 'source_auto_concept_id', 
                         'source_vocabulary_id', 'source_code_description', 
                         'additional_info', 'frequency']]
    
    # Save to CSV
    usagi_df.to_csv(output_file, index=False)
    
    return output_file

def run_usagi_batch_mode(input_file, output_file, usagi_path):
    """
    Run Usagi in batch mode to map source codes to standard concepts.
    
    Args:
        input_file: Path to input CSV file
        output_file: Path to save the mapping results
        usagi_path: Path to Usagi installation
    """
    cmd = [
        "java", "-jar", f"{usagi_path}/Usagi.jar",
        "batch-mode",
        "-input", input_file,
        "-output", output_file,
        "-threshold", "0.5"
    ]
    
    subprocess.run(cmd, check=True)
    
    return output_file

def load_usagi_mappings(mapping_file):
    """
    Load Usagi mappings into a DataFrame.
    
    Args:
        mapping_file: Path to Usagi mapping file
    
    Returns:
        DataFrame with mappings
    """
    mappings_df = pd.read_csv(mapping_file)
    return mappings_df
```

## Cloud-Based Deployment Options

### 1. **AWS Deployment**

#### a. **AWS Lambda for Serverless Processing**

```python
import json
import boto3
import pandas as pd
import psycopg2
from io import StringIO

def lambda_handler(event, context):
    """
    AWS Lambda function to process FHIR resources and load into OMOP CDM.
    """
    # Get S3 bucket and key from event
    bucket = event['Records'][0]['s3']['bucket']['name']
    key = event['Records'][0]['s3']['object']['key']
    
    # Get the FHIR resource file from S3
    s3_client = boto3.client('s3')
    response = s3_client.get_object(Bucket=bucket, Key=key)
    fhir_data = response['Body'].read().decode('utf-8')
    
    # Parse FHIR resource
    fhir_resource = json.loads(fhir_data)
    
    # Transform to OMOP
    omop_records = transform_fhir_to_omop(fhir_resource)
    
    # Load into RDS PostgreSQL
    load_to_omop_db(omop_records)
    
    return {
        'statusCode': 200,
        'body': json.dumps(f'Processed {key} successfully')
    }

def transform_fhir_to_omop(fhir_resource):
    # Transformation logic
    pass

def load_to_omop_db(omop_records):
    # Database connection and loading logic
    conn = psycopg2.connect(
        host=os.environ['DB_HOST'],
        database=os.environ['DB_NAME'],
        user=os.environ['DB_USER'],
        password=os.environ['DB_PASSWORD']
    )
    
    # Load records
    # ...
    
    conn.close()
```

#### b. **AWS Batch for Large-Scale Processing**

```python
import boto3

def submit_fhir_to_omop_batch_job(fhir_data_path, job_queue, job_definition):
    """
    Submit a batch job to process FHIR data.
    """
    batch_client = boto3.client('batch')
    
    response = batch_client.submit_job(
        jobName='fhir-to-omop-job',
        jobQueue=job_queue,
        jobDefinition=job_definition,
        containerOverrides={
            'command': ['python', 'process_fhir.py', '--input', fhir_data_path]
        }
    )
    
    return response['jobId']
```

#### c. **AWS Glue for ETL**

```python
import sys
from awsglue.transforms import *
from awsglue.utils import getResolvedOptions
from pyspark.context import SparkContext
from awsglue.context import GlueContext
from awsglue.job import Job

# Initialize Glue context
args = getResolvedOptions(sys.argv, ['JOB_NAME'])
sc = SparkContext()
glueContext = GlueContext(sc)
spark = glueContext.spark_session
job = Job(glueContext)
job.init(args['JOB_NAME'], args)

# Read FHIR data from S3
fhir_data = glueContext.create_dynamic_frame.from_options(
    connection_type="s3",
    connection_options={"paths": ["s3://fhir-data-bucket/patients/"]},
    format="json"
)

# Transform FHIR to OMOP
def map_patient_to_person(rec):
    # Transformation logic
    return {
        "person_id": rec["id"],
        "gender_concept_id": map_gender(rec.get("gender")),
        "year_of_birth": extract_year(rec.get("birthDate")),
        # Other mappings
    }

persons = Map.apply(frame=fhir_data, f=map_patient_to_person)

# Write to PostgreSQL
glueContext.write_dynamic_frame.from_jdbc_conf(
    frame=persons,
    catalog_connection="omop-db-connection",
    connection_options={
        "dbtable": "person",
        "database": "omop_cdm"
    }
)

job.commit()
```

### 2. **Azure Deployment**

#### a. **Azure Functions**

```python
import logging
import azure.functions as func
import json
import pyodbc
import pandas as pd

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Python HTTP trigger function processed a request.')
    
    try:
        # Get FHIR resource from request body
        req_body = req.get_json()
        
        # Transform FHIR to OMOP
        omop_records = transform_fhir_to_omop(req_body)
        
        # Load into Azure SQL Database
        load_to_omop_db(omop_records)
        
        return func.HttpResponse(
            "FHIR resource processed successfully.",
            status_code=200
        )
    except Exception as e:
        logging.error(f"Error processing FHIR resource: {str(e)}")
        return func.HttpResponse(
            f"Error processing FHIR resource: {str(e)}",
            status_code=500
        )

def transform_fhir_to_omop(fhir_resource):
    # Transformation logic
    pass

def load_to_omop_db(omop_records):
    # Database connection and loading logic
    conn = pyodbc.connect(
        'DRIVER={ODBC Driver 17 for SQL Server};'
        f'SERVER={os.environ["DB_SERVER"]};'
        f'DATABASE={os.environ["DB_NAME"]};'
        f'UID={os.environ["DB_USER"]};'
        f'PWD={os.environ["DB_PASSWORD"]}'
    )
    
    # Load records
    # ...
    
    conn.close()
```

#### b. **Azure Data Factory**

```json
{
    "name": "FhirToOmopPipeline",
    "properties": {
        "activities": [
            {
                "name": "ExtractFhirData",
                "type": "Copy",
                "inputs": [
                    {
                        "referenceName": "FhirServerDataset",
                        "type": "DatasetReference"
                    }
                ],
                "outputs": [
                    {
                        "referenceName": "FhirRawDataset",
                        "type": "DatasetReference"
                    }
                ],
                "typeProperties": {
                    "source": {
                        "type": "RestSource",
                        "httpRequestTimeout": "00:01:40",
                        "requestInterval": "00.00:00:00.010",
                        "requestMethod": "GET",
                        "additionalHeaders": {
                            "Accept": "application/fhir+json"
                        }
                    },
                    "sink": {
                        "type": "JsonSink",
                        "storeSettings": {
                            "type": "AzureBlobStorageWriteSettings"
                        }
                    }
                }
            },
            {
                "name": "TransformFhirToOmop",
                "type": "DatabricksNotebook",
                "dependsOn": [
                    {
                        "activity": "ExtractFhirData",
                        "dependencyConditions": [
                            "Succeeded"
                        ]
                    }
                ],
                "typeProperties": {
                    "notebookPath": "/Shared/FhirToOmop/TransformFhirToOmop",
                    "baseParameters": {
                        "inputPath": "@activity('ExtractFhirData').output.path"
                    }
                },
                "linkedServiceName": {
                    "referenceName": "AzureDatabricks",
                    "type": "LinkedServiceReference"
                }
            },
            {
                "name": "LoadOmopData",
                "type": "Copy",
                "dependsOn": [
                    {
                        "activity": "TransformFhirToOmop",
                        "dependencyConditions": [
                            "Succeeded"
                        ]
                    }
                ],
                "inputs": [
                    {
                        "referenceName": "OmopTransformedDataset",
                        "type": "DatasetReference"
                    }
                ],
                "outputs": [
                    {
                        "referenceName": "OmopSqlDataset",
                        "type": "DatasetReference"
                    }
                ],
                "typeProperties": {
                    "source": {
                        "type": "ParquetSource"
                    },
                    "sink": {
                        "type": "SqlSink",
                        "writeBatchSize": 10000,
                        "writeBatchTimeout": "00:30:00",
                        "preCopyScript": "TRUNCATE TABLE [dbo].[person]"
                    }
                }
            }
        ]
    }
}
```

### 3. **Google Cloud Platform Deployment**

#### a. **Google Cloud Functions**

```python
from google.cloud import storage
from google.cloud import bigquery
import json
import functions_framework

@functions_framework.http
def process_fhir(request):
    """
    Process FHIR resource and load into BigQuery OMOP tables.
    """
    # Get FHIR resource from request
    request_json = request.get_json(silent=True)
    
    if not request_json:
        return 'No FHIR resource provided', 400
    
    # Transform FHIR to OMOP
    omop_records = transform_fhir_to_omop(request_json)
    
    # Load into BigQuery
    load_to_bigquery(omop_records)
    
    return 'FHIR resource processed successfully', 200

def transform_fhir_to_omop(fhir_resource):
    # Transformation logic
    pass

def load_to_bigquery(omop_records):
    # Initialize BigQuery client
    client = bigquery.Client()
    
    # Define table references
    dataset_ref = client.dataset('omop_cdm')
    
    # Load records into appropriate tables
    for table_name, records in omop_records.items():
        table_ref = dataset_ref.table(table_name)
        
        # Load data
        job_config = bigquery.LoadJobConfig()
        job_config.source_format = bigquery.SourceFormat.NEWLINE_DELIMITED_JSON
        job_config.schema = get_table_schema(table_name)
        
        job = client.load_table_from_json(
            records,
            table_ref,
            job_config=job_config
        )
        
        job.result()  # Wait for job to complete
```

#### b. **Google Cloud Dataflow**

```python
import apache_beam as beam
from apache_beam.options.pipeline_options import PipelineOptions
import json

class TransformFhirToOmop(beam.DoFn):
    def process(self, element):
        # Parse FHIR resource
        fhir_resource = json.loads(element)
        
        # Determine resource type
        resource_type = fhir_resource.get('resourceType')
        
        if resource_type == 'Patient':
            # Transform Patient to Person
            person = self.map_patient_to_person(fhir_resource)
            yield ('person', person)
        elif resource_type == 'Condition':
            # Transform Condition to Condition_Occurrence
            condition = self.map_condition_to_condition_occurrence(fhir_resource)
            yield ('condition_occurrence', condition)
        # Handle other resource types
    
    def map_patient_to_person(self, patient):
        # Mapping logic
        pass
    
    def map_condition_to_condition_occurrence(self, condition):
        # Mapping logic
        pass

def run():
    # Define pipeline options
    options = PipelineOptions([
        '--project=your-gcp-project',
        '--runner=DataflowRunner',
        '--region=us-central1',
        '--temp_location=gs://your-bucket/temp',
    ])
    
    # Create pipeline
    with beam.Pipeline(options=options) as p:
        # Read FHIR resources from Cloud Storage
        fhir_resources = (
            p | 'ReadFhirResources' >> beam.io.ReadFromText('gs://your-bucket/fhir-data/*.json')
        )
        
        # Transform FHIR to OMOP
        omop_records = (
            fhir_resources | 'TransformFhirToOmop' >> beam.ParDo(TransformFhirToOmop())
        )
        
        # Write to BigQuery
        omop_records | 'WriteToBigQuery' >> beam.io.WriteToBigQuery(
            table=lambda record: record[0],
            dataset='omop_cdm',
            project='your-gcp-project',
            schema=lambda table, record: get_table_schema(table),
            create_disposition=beam.io.BigQueryDisposition.CREATE_IF_NEEDED,
            write_disposition=beam.io.BigQueryDisposition.WRITE_APPEND
        )

if __name__ == '__main__':
    run()
```

## Monitoring and Logging Solutions

### 1. **ELK Stack (Elasticsearch, Logstash, Kibana)**

```python
import logging
from elasticsearch import Elasticsearch
from datetime import datetime

# Configure logging
class ElasticsearchHandler(logging.Handler):
    def __init__(self, es_host, es_index):
        super().__init__()
        self.es = Elasticsearch([es_host])
        self.es_index = es_index
    
    def emit(self, record):
        try:
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'level': record.levelname,
                'message': self.format(record),
                'logger': record.name,
                'path': record.pathname,
                'line_number': record.lineno,
                'function': record.funcName
            }
            
            # Add extra fields if available
            if hasattr(record, 'resource_type'):
                log_entry['resource_type'] = record.resource_type
            
            if hasattr(record, 'resource_id'):
                log_entry['resource_id'] = record.resource_id
            
            # Index the log entry
            self.es.index(index=self.es_index, body=log_entry)
        except Exception as e:
            print(f"Error sending log to Elasticsearch: {e}")

# Set up logger
logger = logging.getLogger('fhir_to_omop')
logger.setLevel(logging.INFO)

# Add Elasticsearch handler
es_handler = ElasticsearchHandler('localhost:9200', 'fhir_to_omop_logs')
logger.addHandler(es_handler)

# Example usage
def process_fhir_resource(resource):
    resource_type = resource.get('resourceType')
    resource_id = resource.get('id')
    
    # Add extra fields to log record
    extra = {
        'resource_type': resource_type,
        'resource_id': resource_id
    }
    
    logger.info(f"Processing {resource_type} resource", extra=extra)
    
    try:
        # Process resource
        # ...
        logger.info(f"Successfully processed {resource_type}/{resource_id}", extra=extra)
    except Exception as e:
        logger.error(f"Error processing {resource_type}/{resource_id}: {str(e)}", extra=extra)
        raise
```

### 2. **Prometheus and Grafana**

```python
from prometheus_client import Counter, Histogram, start_http_server
import time
import random

# Define metrics
FHIR_RESOURCES_PROCESSED = Counter(
    'fhir_resources_processed_total',
    'Number of FHIR resources processed',
    ['resource_type']
)

OMOP_RECORDS_INSERTED = Counter(
    'omop_records_inserted_total',
    'Number of OMOP records inserted',
    ['table_name']
)

TRANSFORMATION_TIME = Histogram(
    'fhir_to_omop_transformation_seconds',
    'Time spent transforming FHIR to OMOP',
    ['resource_type']
)

# Start metrics server
start_http_server(8000)

# Example usage
def process_patient(patient):
    # Record start time
    start_time = time.time()
    
    # Process patient
    # ...
    
    # Record metrics
    FHIR_RESOURCES_PROCESSED.labels(resource_type='Patient').inc()
    OMOP_RECORDS_INSERTED.labels(table_name='person').inc()
    TRANSFORMATION_TIME.labels(resource_type='Patient').observe(time.time() - start_time)
```

### 3. **AWS CloudWatch**

```python
import boto3
import time
import logging
from datetime import datetime

# Configure CloudWatch client
cloudwatch = boto3.client('cloudwatch')

def put_metric(name, value, unit, dimensions):
    """
    Put a metric to CloudWatch.
    """
    cloudwatch.put_metric_data(
        Namespace='FhirToOmop',
        MetricData=[
            {
                'MetricName': name,
                'Dimensions': [
                    {'Name': k, 'Value': v} for k, v in dimensions.items()
                ],
                'Timestamp': datetime.now(),
                'Value': value,
                'Unit': unit
            }
        ]
    )

# Example usage
def process_fhir_batch(resources):
    start_time = time.time()
    
    # Process resources
    processed_count = 0
    error_count = 0
    
    for resource in resources:
        try:
            # Process resource
            # ...
            processed_count += 1
        except Exception as e:
            error_count += 1
            logging.error(f"Error processing resource: {str(e)}")
    
    # Record metrics
    put_metric(
        name='ResourcesProcessed',
        value=processed_count,
        unit='Count',
        dimensions={'BatchId': batch_id}
    )
    
    put_metric(
        name='ProcessingErrors',
        value=error_count,
        unit='Count',
        dimensions={'BatchId': batch_id}
    )
    
    put_metric(
        name='ProcessingTime',
        value=time.time() - start_time,
        unit='Seconds',
        dimensions={'BatchId': batch_id}
    )
```

## Testing and Validation Frameworks

### 1. **Unit Testing with pytest**

```python
import pytest
from mapping.patient_mapper import map_patient_to_person

def test_map_patient_to_person():
    # Test data
    patient = {
        "resourceType": "Patient",
        "id": "123",
        "gender": "male",
        "birthDate": "1980-01-01",
        "name": [
            {
                "use": "official",
                "family": "Smith",
                "given": ["John"]
            }
        ]
    }
    
    # Call function
    person = map_patient_to_person(patient)
    
    # Assertions
    assert person is not None
    assert person["person_id"] == 123
    assert person["gender_concept_id"] == 8507  # Male concept ID
    assert person["year_of_birth"] == 1980
    assert person["month_of_birth"] == 1
    assert person["day_of_birth"] == 1
    assert person["person_source_value"] == "123"
    assert person["gender_source_value"] == "male"

def test_map_patient_to_person_missing_data():
    # Test data with missing fields
    patient = {
        "resourceType": "Patient",
        "id": "456",
        "gender": "female"
        # No birthDate
    }
    
    # Call function
    person = map_patient_to_person(patient)
    
    # Assertions
    assert person is not None
    assert person["person_id"] == 456
    assert person["gender_concept_id"] == 8532  # Female concept ID
    assert person["year_of_birth"] is None
    assert person["month_of_birth"] is None
    assert person["day_of_birth"] is None
    assert person["person_source_value"] == "456"
    assert person["gender_source_value"] == "female"

def test_map_patient_to_person_invalid_input():
    # Test with invalid input
    patient = {
        "resourceType": "Observation",  # Not a Patient
        "id": "789"
    }
    
    # Call function
    person = map_patient_to_person(patient)
    
    # Assertions
    assert person is None
```

### 2. **Integration Testing**

```python
import pytest
import requests
import psycopg2
import os
from etl.main import run_etl_pipeline

@pytest.fixture
def fhir_server():
    """Fixture to set up and tear down a test FHIR server."""
    # Setup code
    server_url = "http://localhost:8080/fhir"
    
    # Create test data
    patient = {
        "resourceType": "Patient",
        "gender": "male",
        "birthDate": "1980-01-01"
    }
    
    response = requests.post(
        f"{server_url}/Patient",
        json=patient,
        headers={"Content-Type": "application/fhir+json"}
    )
    
    patient_id = response.json()["id"]
    
    yield server_url
    
    # Teardown code
    requests.delete(f"{server_url}/Patient/{patient_id}")

@pytest.fixture
def omop_db():
    """Fixture to set up and tear down a test OMOP database."""
    # Setup code
    db_params = {
        "host": "localhost",
        "database": "omop_cdm_test",
        "user": "postgres",
        "password": "postgres"
    }
    
    # Create connection
    conn = psycopg2.connect(**db_params)
    
    # Create tables
    with conn.cursor() as cursor:
        with open("sql/omop_cdm_schema.sql", "r") as f:
            cursor.execute(f.read())
        conn.commit()
    
    yield db_params
    
    # Teardown code
    with conn.cursor() as cursor:
        cursor.execute("DROP SCHEMA public CASCADE; CREATE SCHEMA public;")
        conn.commit()
    
    conn.close()

def test_etl_pipeline(fhir_server, omop_db):
    """Test the complete ETL pipeline."""
    # Set environment variables
    os.environ["FHIR_SERVER_BASE_URL"] = fhir_server
    os.environ["OMOP_DB_HOST"] = omop_db["host"]
    os.environ["OMOP_DB_NAME"] = omop_db["database"]
    os.environ["OMOP_DB_USERNAME"] = omop_db["user"]
    os.environ["OMOP_DB_PASSWORD"] = omop_db["password"]
    
    # Run ETL pipeline
    summary = run_etl_pipeline()
    
    # Assertions
    assert summary["patients_processed"] > 0
    assert summary["persons_inserted"] > 0
    
    # Verify data in database
    conn = psycopg2.connect(**omop_db)
    with conn.cursor() as cursor:
        cursor.execute("SELECT COUNT(*) FROM person")
        count = cursor.fetchone()[0]
        assert count > 0
    
    conn.close()
```

### 3. **Data Quality Validation**

```python
import pandas as pd
import psycopg2
from sqlalchemy import create_engine

def validate_omop_data_quality(connection_string):
    """
    Validate the quality of OMOP CDM data.
    """
    engine = create_engine(connection_string)
    
    # Check for missing values in required fields
    missing_values_query = """
    SELECT 'person' as table_name, COUNT(*) as missing_count
    FROM person
    WHERE gender_concept_id IS NULL
       OR year_of_birth IS NULL
    
    UNION ALL
    
    SELECT 'condition_occurrence' as table_name, COUNT(*) as missing_count
    FROM condition_occurrence
    WHERE condition_concept_id IS NULL
       OR condition_start_date IS NULL
    
    UNION ALL
    
    SELECT 'observation' as table_name, COUNT(*) as missing_count
    FROM observation
    WHERE observation_concept_id IS NULL
       OR observation_date IS NULL
    """
    
    missing_values = pd.read_sql(missing_values_query, engine)
    
    # Check for invalid foreign keys
    invalid_fks_query = """
    SELECT 'condition_occurrence' as table_name, 'person_id' as column_name, COUNT(*) as invalid_count
    FROM condition_occurrence c
    LEFT JOIN person p ON c.person_id = p.person_id
    WHERE p.person_id IS NULL
    
    UNION ALL
    
    SELECT 'observation' as table_name, 'person_id' as column_name, COUNT(*) as invalid_count
    FROM observation o
    LEFT JOIN person p ON o.person_id = p.person_id
    WHERE p.person_id IS NULL
    """
    
    invalid_fks = pd.read_sql(invalid_fks_query, engine)
    
    # Check for invalid concept IDs
    invalid_concepts_query = """
    SELECT 'gender_concept_id' as concept_type, COUNT(*) as invalid_count
    FROM person p
    LEFT JOIN concept c ON p.gender_concept_id = c.concept_id
    WHERE c.concept_id IS NULL
    
    UNION ALL
    
    SELECT 'condition_concept_id' as concept_type, COUNT(*) as invalid_count
    FROM condition_occurrence co
    LEFT JOIN concept c ON co.condition_concept_id = c.concept_id
    WHERE c.concept_id IS NULL AND co.condition_concept_id <> 0
    """
    
    invalid_concepts = pd.read_sql(invalid_concepts_query, engine)
    
    # Prepare validation report
    validation_report = {
        'missing_values': missing_values.to_dict('records'),
        'invalid_foreign_keys': invalid_fks.to_dict('records'),
        'invalid_concepts': invalid_concepts.to_dict('records')
    }
    
    return validation_report
```

### 4. **Performance Testing**

```python
import time
import pandas as pd
import matplotlib.pyplot as plt
from etl.main import run_etl_pipeline

def run_performance_test(batch_sizes, num_iterations=3):
    """
    Run performance tests with different batch sizes.
    """
    results = []
    
    for batch_size in batch_sizes:
        batch_times = []
        
        for i in range(num_iterations):
            start_time = time.time()
            summary = run_etl_pipeline(batch_size=batch_size)
            end_time = time.time()
            
            elapsed_time = end_time - start_time
            resources_processed = (
                summary["patients_processed"] +
                summary["encounters_processed"] +
                summary["conditions_processed"] +
                summary["observations_processed"]
            )
            
            throughput = resources_processed / elapsed_time
            
            batch_times.append({
                'batch_size': batch_size,
                'iteration': i + 1,
                'elapsed_time': elapsed_time,
                'resources_processed': resources_processed,
                'throughput': throughput
            })
        
        results.extend(batch_times)
    
    # Convert to DataFrame
    results_df = pd.DataFrame(results)
    
    # Calculate averages
    avg_results = results_df.groupby('batch_size').agg({
        'elapsed_time': 'mean',
        'throughput': 'mean'
    }).reset_index()
    
    # Plot results
    plt.figure(figsize=(12, 6))
    
    plt.subplot(1, 2, 1)
    plt.plot(avg_results['batch_size'], avg_results['elapsed_time'], marker='o')
    plt.xlabel('Batch Size')
    plt.ylabel('Average Elapsed Time (s)')
    plt.title('Elapsed Time vs Batch Size')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.plot(avg_results['batch_size'], avg_results['throughput'], marker='o')
    plt.xlabel('Batch Size')
    plt.ylabel('Average Throughput (resources/s)')
    plt.title('Throughput vs Batch Size')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('performance_test_results.png')
    
    return results_df, avg_results
```

## Security and Compliance Technologies

### 1. **Data Encryption**

```python
from cryptography.fernet import Fernet
import base64
import os
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class DataEncryption:
    def __init__(self, key=None):
        """
        Initialize encryption with a key or generate a new one.
        """
        if key:
            self.key = key
        else:
            self.key = Fernet.generate_key()
        
        self.cipher = Fernet(self.key)
    
    @classmethod
    def generate_key_from_password(cls, password, salt=None):
        """
        Generate a key from a password and salt.
        """
        if salt is None:
            salt = os.urandom(16)
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000
        )
        
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return cls(key), salt
    
    def encrypt_data(self, data):
        """
        Encrypt data.
        """
        if isinstance(data, str):
            data = data.encode()
        
        return self.cipher.encrypt(data)
    
    def decrypt_data(self, encrypted_data):
        """
        Decrypt data.
        """
        return self.cipher.decrypt(encrypted_data)

# Example usage
def encrypt_phi_fields(patient_data, encryption_key):
    """
    Encrypt PHI fields in patient data.
    """
    encryptor = DataEncryption(encryption_key)
    
    # Create a copy of the data
    encrypted_data = patient_data.copy()
    
    # Encrypt PHI fields
    if 'name' in encrypted_data:
        encrypted_data['name'] = encryptor.encrypt_data(str(encrypted_data['name'])).decode()
    
    if 'birthDate' in encrypted_data:
        encrypted_data['birthDate'] = encryptor.encrypt_data(encrypted_data['birthDate']).decode()
    
    if 'identifier' in encrypted_data:
        encrypted_data['identifier'] = [
            {
                'system': id_obj['system'],
                'value': encryptor.encrypt_data(id_obj['value']).decode()
            }
            for id_obj in encrypted_data['identifier']
        ]
    
    return encrypted_data
```

### 2. **Authentication and Authorization**

```python
import jwt
import datetime
from functools import wraps
from flask import Flask, request, jsonify

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key'

def generate_token(user_id, role):
    """
    Generate a JWT token.
    """
    payload = {
        'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=24),
        'iat': datetime.datetime.utcnow(),
        'sub': user_id,
        'role': role
    }
    
    return jwt.encode(
        payload,
        app.config['SECRET_KEY'],
        algorithm='HS256'
    )

def token_required(f):
    """
    Decorator to require a valid token.
    """
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
        
        if not token:
            return jsonify({'message': 'Token is missing'}), 401
        
        try:
            data = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
            current_user = {
                'id': data['sub'],
                'role': data['role']
            }
        except:
            return jsonify({'message': 'Token is invalid'}), 401
        
        return f(current_user, *args, **kwargs)
    
    return decorated

def role_required(roles):
    """
    Decorator to require specific roles.
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(current_user, *args, **kwargs):
            if current_user['role'] not in roles:
                return jsonify({'message': 'Permission denied'}), 403
            return f(current_user, *args, **kwargs)
        return decorated_function
    return decorator

# Example API endpoint
@app.route('/api/etl/start', methods=['POST'])
@token_required
@role_required(['admin', 'etl_operator'])
def start_etl(current_user):
    # Start ETL process
    return jsonify({'message': 'ETL process started'})
```

### 3. **Audit Logging**

```python
import logging
import json
from datetime import datetime
import uuid
import socket
import getpass

class AuditLogger:
    def __init__(self, app_name, log_file=None):
        """
        Initialize audit logger.
        """
        self.app_name = app_name
        self.logger = logging.getLogger(f"{app_name}_audit")
        self.logger.setLevel(logging.INFO)
        
        # Add file handler if specified
        if log_file:
            file_handler = logging.FileHandler(log_file)
            formatter = logging.Formatter('%(message)s')
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
    
    def log_event(self, event_type, user_id, resource_type=None, resource_id=None, 
                 action=None, status=None, details=None):
        """
        Log an audit event.
        """
        event = {
            'timestamp': datetime.now().isoformat(),
            'event_id': str(uuid.uuid4()),
            'event_type': event_type,
            'app_name': self.app_name,
            'user_id': user_id,
            'hostname': socket.gethostname(),
            'process_id': os.getpid(),
            'system_user': getpass.getuser()
        }
        
        if resource_type:
            event['resource_type'] = resource_type
        
        if resource_id:
            event['resource_id'] = resource_id
        
        if action:
            event['action'] = action
        
        if status:
            event['status'] = status
        
        if details:
            event['details'] = details
        
        self.logger.info(json.dumps(event))
        
        return event

# Example usage
audit_logger = AuditLogger('fhir_to_omop', log_file='audit.log')

def process_fhir_resource(resource, user_id):
    """
    Process a FHIR resource with audit logging.
    """
    resource_type = resource.get('resourceType')
    resource_id = resource.get('id')
    
    # Log access event
    audit_logger.log_event(
        event_type='DATA_ACCESS',
        user_id=user_id,
        resource_type=resource_type,
        resource_id=resource_id,
        action='READ',
        status='SUCCESS'
    )
    
    try:
        # Process resource
        # ...
        
        # Log transformation event
        audit_logger.log_event(
            event_type='DATA_TRANSFORMATION',
            user_id=user_id,
            resource_type=resource_type,
            resource_id=resource_id,
            action='TRANSFORM',
            status='SUCCESS'
        )
        
        # Log write event
        audit_logger.log_event(
            event_type='DATA_WRITE',
            user_id=user_id,
            resource_type='OMOP',
            resource_id=f"OMOP_{resource_type}_{resource_id}",
            action='WRITE',
            status='SUCCESS'
        )
    except Exception as e:
        # Log error event
        audit_logger.log_event(
            event_type='ERROR',
            user_id=user_id,
            resource_type=resource_type,
            resource_id=resource_id,
            action='TRANSFORM',
            status='FAILURE',
            details={'error_message': str(e)}
        )
        raise
```

## Conclusion

This technical document provides a comprehensive overview of the cutting-edge technologies, libraries, frameworks, and methodologies used in FHIR to OMOP transformation pipelines. By leveraging these technologies, organizations can build efficient, scalable, and secure pipelines for transforming healthcare data from FHIR to OMOP CDM.

The document covers Python libraries for FHIR processing and OMOP CDM, database technologies and SQL optimization techniques, ETL frameworks and orchestration tools, terminology services and vocabulary mapping, cloud-based deployment options, monitoring and logging solutions, testing and validation frameworks, performance optimization techniques, and security and compliance technologies.

By following the best practices and examples provided in this document, developers can implement robust FHIR to OMOP transformation pipelines that meet the requirements of healthcare data interoperability and research.

## References

1. FHIR Specification: https://hl7.org/fhir/
2. OMOP Common Data Model: https://ohdsi.github.io/CommonDataModel/
3. OHDSI Tools: https://www.ohdsi.org/software-tools/
4. HAPI FHIR: https://hapifhir.io/
5. SQLAlchemy Documentation: https://docs.sqlalchemy.org/
6. Apache Airflow Documentation: https://airflow.apache.org/docs/
7. AWS Documentation: https://docs.aws.amazon.com/
8. Azure Documentation: https://docs.microsoft.com/en-us/azure/
9. Google Cloud Documentation: https://cloud.google.com/docs/
10. Prometheus Documentation: https://prometheus.io/docs/
11. Grafana Documentation: https://grafana.com/docs/
12. pytest Documentation: https://docs.pytest.org/
13. OHDSI ETL Best Practices: https://ohdsi.github.io/TheBookOfOhdsi/ExtractTransformLoad.html
