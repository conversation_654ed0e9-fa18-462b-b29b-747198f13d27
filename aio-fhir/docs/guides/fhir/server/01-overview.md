# Local FHIR-OMOP Deployment Architecture: Modular Design for Clinical Research

## Overview

This setup creates a local dev environment with a FHIR (Fast Healthcare Interoperability Resources) server linked to a database. It's the foundation for transforming FHIR into OMOP CDM (Common Data Model). Designed to be modular and scalable, the architecture supports clinical research in a reproducible, isolated setup.

### Architectural Diagram

The following diagram illustrates the layered architecture of our FHIR server environment:

```mermaid
graph TD
    subgraph "Infrastructure Layer"
        Docker["Docker & Docker Compose"]
    end

    subgraph "Application Layer"
        HAPI["HAPI FHIR Server"]
    end

    subgraph "Persistence Layer"
        PG["PostgreSQL"]
    end

    subgraph "Client Layer"
        Postman["Postman"]
        Scripts["Test Scripts"]
        Browser["Web Browser"]
    end

    subgraph "Configuration"
        ENV[".env File"]
        DC["docker-compose-postgres.yml"]
    end

    Docker --> HAPI
    HAPI --> PG
    ENV --> Docker
    DC --> Docker
    Postman --> <PERSON><PERSON><PERSON>
    Scripts --> HAPI
    Browser --> HAPI

    classDef current fill:#6ab7ff,stroke:#333,stroke-width:2px,color:#000;
    classDef config fill:#ffcc00,stroke:#333,stroke-width:2px,color:#000;
    classDef client fill:#99e699,stroke:#333,stroke-width:2px,color:#000;

    class HAPI,PG,Docker current;
    class ENV,DC config;
    class Postman,Scripts,Browser client;
```

This architecture is organized in logical layers:
1. **Infrastructure Layer**: Docker and Docker Compose for container orchestration
2. **Application Layer**: HAPI FHIR server for FHIR resource management
3. **Persistence Layer**: PostgreSQL database for robust data storage
4. **Client Layer**: Tools that interact with the FHIR server (Postman, test scripts, web browsers)
5. **Configuration**: Environment variables and Docker Compose configuration

**Legend**:
- 🔵 **Blue**: Current components in use
- 🟣 **Purple**: Future components (planned but not yet implemented)
- 🟡 **Yellow**: Configuration files
- 🟢 **Green**: Client tools

## Architectural Justification

The proposed architecture is based on solid principles supported by our research:

1. **Docker containers for isolation and reproducibility**: Following recommendations from the [Vulcan FHIR-OMOP project](https://build.fhir.org/ig/HL7/vulcan-fhir-omop/), we use Docker to ensure consistent environments and eliminate "works on my machine" issues. As noted in the [FHIR to OMOP Cookbook](docs/research/pdfs/FHIR%20to%20OMOP%20Cookbook_v04.pdf), "containerization significantly facilitates deployment and reduces barriers to entry for new users."

2. **HAPI FHIR as reference FHIR server**: We selected [HAPI FHIR](https://hapifhir.io/) as it is the open-source reference implementation for FHIR R4, widely adopted in the research community. According to Microsoft's FHIR-to-OMOP Transformation Service documentation, "HAPI FHIR offers the most complete and standards-compliant implementation of the FHIR model."

3. **PostgreSQL for persistent storage**: We chose PostgreSQL for its robustness, performance, and compatibility with OMOP CDM. As [OHDSI documentation](https://ohdsi.github.io/TheBookOfOhdsi/CommonDataModel.html) indicates, "PostgreSQL is one of the recommended databases for OMOP CDM implementations due to its ability to handle large volumes of data and support for complex queries."

4. **FHIR R4 as standard version**: We use [FHIR R4](https://hl7.org/fhir/R4/) as it contains normative (stable) content and is the most widely implemented version. The [OHDSI Common Data Model](https://github.com/OHDSI/CommonDataModel) documentation notes that "most FHIR-to-OMOP mapping tools are optimized for FHIR R4."

5. **OMOP CDM v5.4.2 as target model**: Although not implemented in this phase, we selected [OMOP CDM v5.4.2](https://github.com/OHDSI/CommonDataModel/releases/tag/v5.4.2) as the ultimate target due to its wide adoption and tool support. According to [OHDSI ETL-CDMBuilder](https://github.com/OHDSI/ETL-CDMBuilder) documentation, "OMOP CDM v5.4.2 offers the best balance between stability and features for clinical research projects."

## Key Features

- **Container-based architecture**: The entire environment runs in Docker containers, facilitating portability and reproducibility.
- **Complete parameterization**: All configurations are managed through environment variables, allowing adaptations without modifying code.
- **Automated testing**: Includes validation scripts to verify proper functioning of the FHIR server.
- **Comprehensive documentation**: Provides detailed guides for installation, configuration, and usage.
- **Basic security incorporated**: Includes options for authentication and credential protection.
- **FHIR interaction examples**: Contains Postman collections to facilitate learning and testing.

## Structure and Components

The deployment is structured in logical layers:

1. **Infrastructure layer**: Docker and Docker Compose for container orchestration.
2. **Persistence layer**: PostgreSQL for FHIR data storage.
3. **Application layer**: HAPI FHIR server for FHIR resource management.
4. **Utilities layer**: Configuration scripts, tests, and examples.

## Upgrade Capability

The design facilitates future upgrades through:

1. **Explicit versioning**: All dependencies have specific documented versions.
2. **Persistent volumes**: Data is stored in Docker volumes separate from code.
3. **Externalized configuration**: Parameters in `.env` files to facilitate changes.
4. **Modular design**: Each component can be upgraded independently.
5. **Migration path to OMOP**: Prepared for phase 2 which will implement OMOP CDM and ETL.

In future iterations, this environment can be expanded to include:
- Complete OMOP CDM implementation
- Automated FHIR-to-OMOP ETL processes
- Visualization and analysis tools
- Integration with standard vocabularies
- Data federation capabilities

---

This document outlines a comprehensive, step-by-step plan for setting up a local FHIR server environment as part of Phase 1 of our FHIR to OMOP transformation project.

## Next Steps

Continue to the [Prerequisites and Environment Setup](02-prerequisites.md) section to prepare your environment for the FHIR server installation.
