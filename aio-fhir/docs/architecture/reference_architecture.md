# Reference Architecture for FHIR to OMOP Transformation Pipeline

This document outlines a comprehensive reference architecture for implementing a FHIR to OMOP CDM transformation pipeline, based on research of existing implementations and best practices.

## 1. Architecture Overview

### 1.1 Architecture Diagram

```mermaid
graph TD
    subgraph "Data Sources"
        A1[FHIR Server] -->|Bulk Export| B1[FHIR Resources]
        A2[FHIR Files] --> B1
    end

    subgraph "ETL Pipeline"
        B1 -->|Extract| C1[Resource Extraction]
        C1 -->|Transform| D1[Mappers]

        subgraph "Mappers"
            D1 --> D2[Patient Mapper]
            D1 --> D3[Encounter Mapper]
            D1 --> D4[Condition Mapper]
            D1 --> D5[Observation Mapper]
            D1 --> D6[Other Mappers...]
        end

        D1 -->|Load| E1[OMOP CDM Database]
    end

    subgraph "Support Components"
        F1[Vocabulary Service] -->|Concept Mapping| D1
        F2[Configuration] --> C1
        F2 --> D1
        F2 --> E1
        F3[Utilities] --> C1
        F3 --> D1
        F3 --> E1
    end

    subgraph "OMOP CDM"
        E1 --> E2[Person]
        E1 --> E3[Visit Occurrence]
        E1 --> E4[Condition Occurrence]
        E1 --> E5[Measurement]
        E1 --> E6[Other Tables...]
    end

    %% Data Sources
    style A1 fill:#d770ad,stroke:#333,stroke-width:2px,color:#fff
    style A2 fill:#d770ad,stroke:#333,stroke-width:2px,color:#fff
    style B1 fill:#d770ad,stroke:#333,stroke-width:2px,color:#fff

    %% ETL Pipeline
    style C1 fill:#5d8aa8,stroke:#333,stroke-width:2px,color:#fff
    style D1 fill:#5d8aa8,stroke:#333,stroke-width:2px,color:#fff
    style D2 fill:#5d8aa8,stroke:#333,stroke-width:2px,color:#fff
    style D3 fill:#5d8aa8,stroke:#333,stroke-width:2px,color:#fff
    style D4 fill:#5d8aa8,stroke:#333,stroke-width:2px,color:#fff
    style D5 fill:#5d8aa8,stroke:#333,stroke-width:2px,color:#fff
    style D6 fill:#5d8aa8,stroke:#333,stroke-width:2px,color:#fff

    %% Support Components
    style F1 fill:#6b8e23,stroke:#333,stroke-width:2px,color:#fff
    style F2 fill:#6b8e23,stroke:#333,stroke-width:2px,color:#fff
    style F3 fill:#6b8e23,stroke:#333,stroke-width:2px,color:#fff

    %% OMOP CDM
    style E1 fill:#b87333,stroke:#333,stroke-width:2px,color:#fff
    style E2 fill:#b87333,stroke:#333,stroke-width:2px,color:#fff
    style E3 fill:#b87333,stroke:#333,stroke-width:2px,color:#fff
    style E4 fill:#b87333,stroke:#333,stroke-width:2px,color:#fff
    style E5 fill:#b87333,stroke:#333,stroke-width:2px,color:#fff
    style E6 fill:#b87333,stroke:#333,stroke-width:2px,color:#fff
```

### 1.2 Project Structure

```mermaid
graph LR
    subgraph "Repository Root"
        A[fhir-omop] --> B[src]
        A --> C[docs]
        A --> D[data]
        A --> E[tests]
    end

    subgraph "Source Code"
        B --> B1[fhir_omop]
        B1 --> B2[mappers]
        B1 --> B3[utils]
        B1 --> B4[etl]
        B1 --> B5[validation]
    end

    subgraph "Documentation"
        C --> C1[architecture]
        C --> C2[guides]
        C --> C3[mappings]
        C --> C4[research]
    end

    subgraph "Data Files"
        D --> D1[sample_fhir]
        D --> D2[vocabulary]
    end

    subgraph "Test Suite"
        E --> E1[test_mappers]
        E --> E2[test_utils]
        E --> E3[test_etl]
    end

    %% Repository Root
    style A fill:#4b0082,stroke:#333,stroke-width:2px,color:#fff

    %% Source Code
    style B fill:#1e90ff,stroke:#333,stroke-width:2px,color:#fff
    style B1 fill:#1e90ff,stroke:#333,stroke-width:2px,color:#fff
    style B2 fill:#1e90ff,stroke:#333,stroke-width:2px,color:#fff
    style B3 fill:#1e90ff,stroke:#333,stroke-width:2px,color:#fff
    style B4 fill:#1e90ff,stroke:#333,stroke-width:2px,color:#fff
    style B5 fill:#1e90ff,stroke:#333,stroke-width:2px,color:#fff

    %% Documentation
    style C fill:#228b22,stroke:#333,stroke-width:2px,color:#fff
    style C1 fill:#228b22,stroke:#333,stroke-width:2px,color:#fff
    style C2 fill:#228b22,stroke:#333,stroke-width:2px,color:#fff
    style C3 fill:#228b22,stroke:#333,stroke-width:2px,color:#fff
    style C4 fill:#228b22,stroke:#333,stroke-width:2px,color:#fff

    %% Data Files
    style D fill:#b8860b,stroke:#333,stroke-width:2px,color:#fff
    style D1 fill:#b8860b,stroke:#333,stroke-width:2px,color:#fff
    style D2 fill:#b8860b,stroke:#333,stroke-width:2px,color:#fff

    %% Test Suite
    style E fill:#8b0000,stroke:#333,stroke-width:2px,color:#fff
    style E1 fill:#8b0000,stroke:#333,stroke-width:2px,color:#fff
    style E2 fill:#8b0000,stroke:#333,stroke-width:2px,color:#fff
    style E3 fill:#8b0000,stroke:#333,stroke-width:2px,color:#fff
```

### 1.3 Data Flow

```mermaid
sequenceDiagram
    participant FHIR as FHIR Source
    participant Extract as Extraction Layer
    participant Map as Mappers
    participant Vocab as Vocabulary Service
    participant Load as Loading Layer
    participant OMOP as OMOP CDM Database

    FHIR->>Extract: FHIR Resources (JSON)
    Extract->>Map: Parsed Resources

    Map->>Vocab: Request Concept Mappings
    Vocab-->>Map: Standard Concepts

    Map->>Load: OMOP-Compatible Records
    Load->>OMOP: Insert into CDM Tables
    OMOP-->>Load: Confirmation

    Load-->>Map: Success/Failure
    Map-->>Extract: Processing Status
    Extract-->>FHIR: Completion Report
```

The architecture follows a modular design with clear separation of concerns, enabling flexibility, scalability, and maintainability. It consists of the following core components:

### 1.4 Data Ingestion Layer
- **FHIR Server**: Acts as the source of FHIR resources (R4)
- **Bulk FHIR API Client**: Extracts resources in bulk for efficient processing
- **FHIR Resource Validator**: Validates incoming resources against FHIR profiles

### 1.5 Transformation Layer
- **ETL Orchestrator**: Manages the transformation workflow and tracks progress
- **Mapping Engine**: Applies transformation rules to convert FHIR to OMOP
- **Terminology Service**: Handles code system mappings and concept translations
- **Validation Engine**: Ensures transformed data meets OMOP CDM specifications

### 1.6 Data Storage Layer
- **OMOP CDM Database**: PostgreSQL database with OMOP CDM schema
- **Vocabulary Database**: Contains standard vocabularies and concept mappings
- **Transformation Metadata Store**: Tracks lineage, mappings, and transformation metrics

### 1.7 Monitoring and Management Layer
- **Logging Service**: Captures detailed logs of the transformation process
- **Metrics Dashboard**: Visualizes transformation statistics and data quality metrics
- **Job Scheduler**: Manages periodic or event-triggered transformations

## 2. Component Details

### 2.1 FHIR Server
- **Purpose**: Source of FHIR resources for transformation
- **Options**:
  - HAPI FHIR Server (open-source Java implementation)
  - IBM FHIR Server (cloud-native implementation)
  - Azure API for FHIR (managed service)
  - Google Cloud Healthcare API (managed service)
- **Key Features**:
  - Support for FHIR R4 resources
  - Bulk export capabilities
  - Search and filtering capabilities
  - Authentication and authorization

### 2.2 ETL Orchestrator
- **Purpose**: Coordinates the transformation workflow
- **Options**:
  - Apache Airflow (Python-based workflow management)
  - Apache NiFi (data flow automation)
  - Spring Batch (Java-based batch processing)
  - Custom orchestration service
- **Key Features**:
  - Workflow definition and execution
  - Error handling and retry mechanisms
  - Dependency management between tasks
  - Monitoring and alerting

### 2.3 Mapping Engine
- **Purpose**: Transforms FHIR resources to OMOP CDM tables
- **Options**:
  - Custom Python transformation scripts
  - Whistle (JSON-to-JSON transformation language)
  - FHIR Mapping Language with Matchbox
  - SQL-based transformations
- **Key Features**:
  - Declarative mapping definitions
  - Support for complex transformations
  - Extensibility for custom mapping logic
  - Performance optimization for large datasets

### 2.4 Terminology Service
- **Purpose**: Handles code system mappings and concept translations
- **Options**:
  - HAPI FHIR Terminology Service
  - Ontoserver
  - OHDSI Athena (for vocabulary download)
  - Custom terminology mapping service
- **Key Features**:
  - Code system translation (LOINC, SNOMED CT, RxNorm)
  - Concept relationship navigation
  - Vocabulary version management
  - Caching for performance optimization

### 2.5 OMOP CDM Database
- **Purpose**: Stores transformed data in OMOP CDM format
- **Options**:
  - PostgreSQL (recommended)
  - SQL Server
  - Oracle
  - BigQuery (for very large datasets)
- **Key Features**:
  - OMOP CDM v5.3/v5.4 schema
  - Optimized indexing for OMOP queries
  - Partitioning for large tables
  - Backup and recovery mechanisms

## 3. Data Flow

The transformation pipeline follows these key data flow steps:

1. **Resource Extraction**:
   - FHIR resources are extracted from the FHIR server using the Bulk FHIR API
   - Resources are validated against FHIR profiles
   - Resources are grouped by type for efficient processing

2. **Pre-processing**:
   - Resources are converted to a common intermediate format (JSON)
   - Data quality checks are performed
   - Resources are enriched with additional metadata

3. **Transformation**:
   - Mapping rules are applied to convert FHIR resources to OMOP CDM format
   - Terminology mappings are applied to standardize codes
   - Relationships between resources are preserved in OMOP CDM tables

4. **Post-processing**:
   - Data quality checks are performed on transformed data
   - Derived fields are calculated
   - Indexes are updated for optimal query performance

5. **Loading**:
   - Transformed data is loaded into OMOP CDM tables
   - Constraints and referential integrity are enforced
   - Transformation metadata is recorded

## 4. Deployment Options

### 4.1 On-Premises Deployment

**Components**:
- Docker containers for each service
- Kubernetes for orchestration
- Local PostgreSQL database
- Local FHIR server

**Considerations**:
- Requires significant hardware resources
- Provides maximum control over data and security
- Requires in-house expertise for maintenance
- Suitable for organizations with strict data governance requirements

### 4.2 Cloud Deployment (AWS)

**Components**:
- Amazon ECS/EKS for container orchestration
- Amazon RDS for PostgreSQL
- Amazon S3 for storage
- AWS Lambda for serverless functions
- Amazon CloudWatch for monitoring

**Considerations**:
- Scalable and elastic resources
- Managed services reduce operational overhead
- Pay-as-you-go pricing model
- Requires cloud expertise and security considerations

### 4.3 Cloud Deployment (Azure)

**Components**:
- Azure Kubernetes Service (AKS)
- Azure Database for PostgreSQL
- Azure API for FHIR
- Azure Functions
- Azure Monitor

**Considerations**:
- Integrated with Azure healthcare services
- Managed FHIR service simplifies setup
- Compliance with healthcare regulations
- Microsoft's healthcare-focused tools and services

### 4.4 Cloud Deployment (GCP)

**Components**:
- Google Kubernetes Engine (GKE)
- Cloud SQL for PostgreSQL
- Google Cloud Healthcare API
- Cloud Functions
- Cloud Monitoring

**Considerations**:
- Native support for FHIR through Healthcare API
- Integration with BigQuery for analytics
- AI/ML capabilities for advanced use cases
- Google's data processing capabilities

### 4.5 Hybrid Deployment

**Components**:
- Mix of on-premises and cloud services
- Data processing in cloud, storage on-premises
- VPN/Direct Connect for secure communication

**Considerations**:
- Balances control and scalability
- Addresses specific regulatory requirements
- More complex to set up and maintain
- Provides flexibility for different workloads

## 5. Scaling Considerations

### 5.1 Horizontal Scaling
- Distribute transformation workloads across multiple nodes
- Partition data by resource type or time period
- Use message queues for workload distribution
- Implement stateless components for easy scaling

### 5.2 Vertical Scaling
- Increase resources (CPU, memory) for database servers
- Optimize database configurations for larger datasets
- Use in-memory processing for performance-critical components
- Implement efficient caching strategies

### 5.3 Batch vs. Streaming
- **Batch Processing**:
  - Suitable for initial data loads and periodic updates
  - Efficient for large volumes of historical data
  - Simpler to implement and debug
  - Lower real-time requirements

- **Streaming Processing**:
  - Suitable for near real-time data synchronization
  - Processes data as it arrives
  - More complex to implement and maintain
  - Requires additional components (e.g., Kafka, Spark Streaming)

## 6. Security and Compliance

### 6.1 Data Protection
- Encryption at rest and in transit
- Role-based access control
- Audit logging of all data access
- Data masking and de-identification

### 6.2 Regulatory Compliance
- HIPAA compliance for US healthcare data
- GDPR compliance for European data
- Audit trails for all transformations
- Data lineage tracking

### 6.3 Authentication and Authorization
- OAuth 2.0 / OpenID Connect for authentication
- Fine-grained authorization policies
- Service-to-service authentication
- API security best practices

## 7. Monitoring and Maintenance

### 7.1 Operational Monitoring
- Resource utilization metrics
- Pipeline performance metrics
- Error rates and types
- Processing times and throughput

### 7.2 Data Quality Monitoring
- Completeness of transformed data
- Accuracy of code mappings
- Consistency of relationships
- Conformance to OMOP CDM specifications

### 7.3 Alerting and Notification
- Threshold-based alerts for critical metrics
- Notification channels (email, SMS, Slack)
- Escalation policies for critical issues
- On-call rotation for support

### 7.4 Maintenance Procedures
- Regular vocabulary updates
- Schema migrations for OMOP CDM updates
- Backup and recovery procedures
- Performance tuning and optimization

## 8. Implementation Recommendations

Based on the research of real-world implementations, we recommend the following approach:

1. **Start with Core Resources**: Begin with the most common FHIR resources (Patient, Encounter, Condition, Observation, Medication)
2. **Adopt Modular Architecture**: Implement components that can be developed and tested independently
3. **Implement Robust Validation**: Validate data at multiple stages of the pipeline
4. **Use Standard Tools**: Leverage existing open-source tools where possible
5. **Design for Incremental Processing**: Support both full and incremental data loads
6. **Prioritize Terminology Mapping**: Invest in robust terminology services early
7. **Implement Comprehensive Logging**: Track all transformations for debugging and auditing
8. **Develop with Scalability in Mind**: Design components that can scale horizontally
9. **Automate Testing**: Implement automated tests for mapping logic and data quality
10. **Document Everything**: Maintain detailed documentation of all mappings and configurations

## 9. References

1. MENDS-on-FHIR Implementation
2. German ETL-Process for FHIR to OMOP Transformation
3. Modular FHIR-Driven Transformation Pipeline
4. OHDSI ETL Best Practices
5. HL7 FHIR-to-OMOP Implementation Guide (Vulcan Project)
