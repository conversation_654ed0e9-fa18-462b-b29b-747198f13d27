# FHIR Selective Loading Guide

This guide explains the selective approach to loading FHIR data, which focuses on loading only resources with complete references to ensure data integrity and avoid errors.

## Table of Contents

- [Overview](#overview)
- [The Dependency Challenge](#the-dependency-challenge)
- [Selective Loading Approach](#selective-loading-approach)
- [Implementation](#implementation)
- [Usage](#usage)
- [Practical Examples](#practical-examples)
- [Troubleshooting](#troubleshooting)
- [References](#references)

## Overview

Loading FHIR data into a server can be challenging due to the complex web of references between resources. The selective loading approach addresses this challenge by analyzing references between resources and loading only those resources whose references can be satisfied.

### When to Use Selective Loading

Selective loading is most appropriate when:

- You're using the **default server configuration** with validation and referential integrity checks enabled
- Data integrity is more important than loading all resources
- You want to avoid creating placeholder resources for missing references
- You're incrementally building up a dataset

For bulk loading with all resources regardless of references, see the [Direct Transaction Tutorial](./direct-transaction-tutorial.md).

### Key Benefits

- **Data Integrity**: Ensures all loaded resources have valid references
- **Error Prevention**: Avoids errors due to missing referenced resources
- **Incremental Loading**: Enables loading data in multiple passes as dependencies are satisfied
- **Dependency Analysis**: Provides insights into the reference structure of your data
- **Works with Default Configuration**: No need to modify server settings

## The Dependency Challenge

FHIR resources often reference other resources, creating a complex dependency graph. For example:

- `Observation` resources reference `Patient` and `Encounter` resources
- `Encounter` resources reference `Patient`, `Practitioner`, and `Location` resources
- `Condition` resources reference `Patient` and `Encounter` resources

When loading data, these dependencies must be satisfied to maintain data integrity. The challenge is determining the correct order to load resources to ensure all references are valid.

### Types of References

FHIR references can be categorized as:

1. **Direct References**: References to resources that must exist (e.g., `Patient` reference in an `Observation`)
2. **Circular References**: Resources that reference each other (e.g., `Encounter` references `Patient` and `Patient` references `Encounter`)
3. **Optional References**: References that are not required (e.g., `Practitioner` reference in an `Observation`)

## Selective Loading Approach

The selective loading approach follows these principles:

1. **Dependency Satisfaction**: A resource can only be loaded if all its references point to resources that are already available
2. **Incremental Loading**: Resources are loaded in multiple passes, with each pass loading resources whose dependencies are now satisfied
3. **Reference Analysis**: Before loading, all references between resources are analyzed to determine the optimal loading order
4. **Selective Inclusion**: Only resources with complete references are included in the loading process

### The Algorithm

1. **Scan Resources**: Identify all resources and their IDs in the dataset
2. **Extract References**: Analyze all references between resources
3. **Determine Loading Order**: Use topological sorting to determine an initial loading order
4. **Incremental Loading**:
   - Start with resources that have no external dependencies
   - After loading each batch, update the set of available resources
   - Check which additional resources can now be loaded
   - Continue until no more resources can be loaded

## Implementation

Our implementation consists of two main components:

1. **Reference Analyzer**: Analyzes references between resources and determines the optimal loading order
2. **Selective Loader**: Loads resources incrementally based on reference completeness

### Reference Analyzer

The reference analyzer:

- Scans all NDJSON files to identify resources and their IDs
- Extracts all references between resources
- Builds a dependency graph based on these references
- Determines which resources have complete references
- Generates a loading plan that prioritizes resources with complete references

### Selective Loader

The selective loader:

- Uses the reference analyzer to determine the initial loading order
- Loads resources incrementally, starting with those that have no dependencies
- After each loading round, checks which additional resources can now be loaded
- Continues until no more resources can be loaded or all resources are loaded

## Usage

### Prerequisites

- Python 3.6+
- HAPI FHIR Server
- FHIR data in NDJSON format

### Basic Usage

```bash
# Reset the FHIR server database (optional)
./manage-fhir-server.sh stop postgres
docker volume rm fhir-server_postgres-data
./manage-fhir-server.sh start postgres

# Note: No need to modify server configuration - selective loader works with default settings

# Run the selective loader
python servers/fhir-server/scripts/transaction_bundles/selective_loader.py \
  --data-dir data/sample_fhir/bulk-export \
  --server-url http://localhost:8080/fhir \
  --verify
```

### Expected Results

With default server configuration, the selective loader will typically:

1. Load Patient resources first (no external dependencies)
2. Load AllergyIntolerance and Device resources next (depend only on Patient)
3. Skip resources with unresolved references (like Encounter, Condition, Observation)

This is normal and expected behavior. If you need to load all resources regardless of references, use the [Direct Transaction Bundle method](./direct-transaction-tutorial.md) instead.

### Command Line Options

- `--data-dir`: Directory containing NDJSON files (default: `data/sample_fhir/bulk-export`)
- `--output-dir`: Directory to save generated bundles (default: `data/generated_bundles`)
- `--server-url`: URL of the FHIR server (default: `http://localhost:8080/fhir`)
- `--batch-size`: Maximum number of resources per bundle (default: 500)
- `--verify`: Verify that resources were loaded correctly

## Practical Examples

### Example 1: Loading Patient and Related Resources

In this example, we'll load Patient resources and resources that directly reference Patients:

```bash
python scripts/transaction_bundles/selective_loader.py \
  --data-dir data/sample_fhir/bulk-export \
  --server-url http://localhost:8080/fhir \
  --verify
```

Expected outcome:
- Patient resources are loaded first (no dependencies)
- AllergyIntolerance and Device resources are loaded next (depend only on Patient)
- Resources with unsatisfied dependencies are not loaded

### Example 2: Analyzing References Without Loading

To analyze references without loading data:

```bash
python scripts/transaction_bundles/reference_analyzer.py \
  --data-dir data/sample_fhir/bulk-export \
  --output-file reference_analysis.json
```

This will generate a JSON file with the reference analysis, including:
- Resources with complete vs. incomplete references
- Dependency graph between resource types
- Optimal loading order

## Troubleshooting

### Common Issues

1. **Circular Dependencies**:
   - **Symptom**: Warning about circular dependencies in resource types
   - **Solution**: This is normal in FHIR data. The algorithm handles circular dependencies by breaking the cycle.

2. **No Resources Loaded**:
   - **Symptom**: "No loadable resources found at this time" for all resource types
   - **Solution**: Check if the dataset contains the necessary resources to satisfy dependencies.

3. **Only Some Resources Loaded**:
   - **Symptom**: Only a small percentage of resources are loaded (e.g., only Patient, AllergyIntolerance, Device)
   - **Solution**: This is expected with default server configuration. The selective loader only loads resources with satisfied references.
   - **Alternative**: If you need to load all resources, use the [Direct Transaction Bundle method](./direct-transaction-tutorial.md) with permissive server configuration.

4. **Resources Rejected Despite Dependencies Being Satisfied**:
   - **Symptom**: Resources are rejected even though their referenced resources exist
   - **Solution**: Check for other validation issues or use the `--verbose` flag for more detailed error messages

### Debugging Tips

- Use the `--verify` option to check if resources were loaded correctly
- Examine the loading summary to see which resource types were loaded
- Check the server logs for any errors during loading
- If you need to load all resources regardless of references, consider switching to the Direct Transaction Bundle method

## References

- [FHIR References](https://www.hl7.org/fhir/references.html)
- [FHIR RESTful API](https://www.hl7.org/fhir/http.html)
- [FHIR Transactions](https://www.hl7.org/fhir/http.html#transaction)
- [HAPI FHIR Server](https://hapifhir.io/)
