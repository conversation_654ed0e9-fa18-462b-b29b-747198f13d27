// Extract from ConditionMapper.java in OHDSI/ETL-German-FHIR-Core repository
// Source: https://github.com/OHDSI/ETL-German-FHIR-Core/blob/main/src/main/java/org/miracum/etl/fhirtoomop/mapper/ConditionMapper.java

package org.miracum.etl.fhirtoomop.mapper;

import static org.miracum.etl.fhirtoomop.Constants.CONCEPT_EHR;
import static org.miracum.etl.fhirtoomop.Constants.CONCEPT_FINDING_SITE;
import static org.miracum.etl.fhirtoomop.Constants.CONCEPT_SEVERITY;
import static org.miracum.etl.fhirtoomop.Constants.CONCEPT_STAGE;
import static org.miracum.etl.fhirtoomop.Constants.FHIR_RESOURCE_CONDITION_ACCEPTABLE_STATUS_LIST;
import static org.miracum.etl.fhirtoomop.Constants.OMOP_DOMAIN_CONDITION;
import static org.miracum.etl.fhirtoomop.Constants.OMOP_DOMAIN_MEASUREMENT;
import static org.miracum.etl.fhirtoomop.Constants.OMOP_DOMAIN_OBSERVATION;
import static org.miracum.etl.fhirtoomop.Constants.OMOP_DOMAIN_PROCEDURE;
import static org.miracum.etl.fhirtoomop.Constants.SOURCE_VOCABULARY_ID_DIAGNOSTIC_CONFIDENCE;
import static org.miracum.etl.fhirtoomop.Constants.SOURCE_VOCABULARY_ID_ICD_LOCALIZATION;
import static org.miracum.etl.fhirtoomop.Constants.VOCABULARY_ICD10GM;

import com.google.common.base.Strings;
import io.micrometer.core.instrument.Counter;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.hl7.fhir.r4.model.Coding;
import org.hl7.fhir.r4.model.Condition;
import org.hl7.fhir.r4.model.Enumerations.ResourceType;
import org.hl7.fhir.r4.model.StringType;
import org.miracum.etl.fhirtoomop.DbMappings;
import org.miracum.etl.fhirtoomop.config.FhirSystems;
import org.miracum.etl.fhirtoomop.mapper.helpers.FindOmopConcepts;
import org.miracum.etl.fhirtoomop.mapper.helpers.MapperMetrics;
import org.miracum.etl.fhirtoomop.mapper.helpers.ResourceCheckDataAbsentReason;
import org.miracum.etl.fhirtoomop.mapper.helpers.ResourceFhirReferenceUtils;
import org.miracum.etl.fhirtoomop.mapper.helpers.ResourceOmopReferenceUtils;
import org.miracum.etl.fhirtoomop.mapper.helpers.ResourceOnset;
import org.miracum.etl.fhirtoomop.model.IcdSnomedDomainLookup;
import org.miracum.etl.fhirtoomop.model.OmopModelWrapper;
import org.miracum.etl.fhirtoomop.model.OrphaSnomedMapping;
import org.miracum.etl.fhirtoomop.model.PostProcessMap;
import org.miracum.etl.fhirtoomop.model.omop.Concept;
import org.miracum.etl.fhirtoomop.model.omop.ConditionOccurrence;
import org.miracum.etl.fhirtoomop.model.omop.Measurement;
import org.miracum.etl.fhirtoomop.model.omop.OmopObservation;
import org.miracum.etl.fhirtoomop.model.omop.ProcedureOccurrence;
import org.miracum.etl.fhirtoomop.model.omop.SourceToConceptMap;
import org.miracum.etl.fhirtoomop.repository.service.ConditionMapperServiceImpl;
import org.miracum.etl.fhirtoomop.repository.service.OmopConceptServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Nullable;
import org.springframework.stereotype.Component;

/**
 * The ConditionMapper class describes the business logic of transforming a FHIR Condition resource
 * to OMOP CDM.
 *
 * <AUTHOR> Henke
 * <AUTHOR> Peng
 */
@Slf4j
@Component
public class ConditionMapper implements FhirMapper<Condition> {

  private static final FhirSystems fhirSystems = new FhirSystems();
  private final DbMappings dbMappings;
  private final Boolean bulkload;

  @Autowired OmopConceptServiceImpl omopConceptService;

  @Autowired ConditionMapperServiceImpl conditionMapperService;

  @Autowired ResourceFhirReferenceUtils resourceFhirReferenceUtils;

  @Autowired ResourceOmopReferenceUtils resourceOmopReferenceUtils;

  @Autowired ResourceCheckDataAbsentReason resourceCheckDataAbsentReason;

  @Autowired ResourceOnset resourceOnset;

  @Autowired FindOmopConcepts findOmopConcepts;

  @Autowired MapperMetrics mapperMetrics;

  private Counter noStartDateCounter;
  private Counter noPersonIdCounter;
  private Counter invalidCodeCounter;
  private Counter noCodeCounter;
  private Counter noMatchingOmopConceptCounter;
  private Counter noFhirReferenceCounter;
  private Counter invalidStatusCounter;
  private Counter totalCounter;
  private Counter successCounter;

  /**
   * Constructor for objects of the class ConditionMapper.
   *
   * @param bulkload parameter which indicates whether the Job should be run as bulk load or
   *     incremental load
   * @param dbMappings collections for the intermediate storage of data from OMOP CDM in RAM
   */
  @Autowired
  public ConditionMapper(Boolean bulkload, DbMappings dbMappings) {
    this.bulkload = bulkload;
    this.dbMappings = dbMappings;
    initCounters();
  }

  private void initCounters() {
    noStartDateCounter = mapperMetrics.registerCounter("noStartDate", "condition");
    noPersonIdCounter = mapperMetrics.registerCounter("noPersonId", "condition");
    invalidCodeCounter = mapperMetrics.registerCounter("invalidCode", "condition");
    noCodeCounter = mapperMetrics.registerCounter("noCode", "condition");
    noMatchingOmopConceptCounter = mapperMetrics.registerCounter("noMatchingOmopConcept", "condition");
    noFhirReferenceCounter = mapperMetrics.registerCounter("noFhirReference", "condition");
    invalidStatusCounter = mapperMetrics.registerCounter("invalidStatus", "condition");
    totalCounter = mapperMetrics.registerCounter("total", "condition");
    successCounter = mapperMetrics.registerCounter("success", "condition");
  }

  /**
   * Maps a FHIR Condition resource to several OMOP CDM tables.
   *
   * @param srcCondition FHIR Condition resource
   * @param isDeleted a flag, whether the FHIR resource is deleted in the source
   * @return OmopModelWrapper cache of newly created OMOP CDM records from the FHIR Condition
   *     resource
   */
  @Override
  public OmopModelWrapper map(Condition srcCondition, boolean isDeleted) {

    totalCounter.increment();

    if (isDeleted) {
      return null;
    }

    var wrapper = new OmopModelWrapper();

    if (srcCondition.getCode() == null
        || srcCondition.getCode().isEmpty()
        || srcCondition.getCode().getCoding().isEmpty()) {
      log.warn("No code found. Condition resource is invalid. Skip resource.");
      noCodeCounter.increment();
      return null;
    }

    if (srcCondition.getSubject() == null || srcCondition.getSubject().isEmpty()) {
      log.warn("No subject found. Condition resource is invalid. Skip resource.");
      noFhirReferenceCounter.increment();
      return null;
    }

    if (!FHIR_RESOURCE_CONDITION_ACCEPTABLE_STATUS_LIST.contains(
        srcCondition.getClinicalStatus().getCodingFirstRep().getCode())) {
      log.warn(
          "The status of the condition is not in the acceptable list. Skip resource. Status: {}",
          srcCondition.getClinicalStatus().getCodingFirstRep().getCode());
      invalidStatusCounter.increment();
      return null;
    }

    var personId =
        resourceOmopReferenceUtils.getPersonId(
            srcCondition.getSubject().getReference(), bulkload);

    if (personId == null) {
      log.warn("No matching person id found. Skip resource.");
      noPersonIdCounter.increment();
      return null;
    }

    var visitOccId =
        resourceOmopReferenceUtils.getVisitOccId(
            srcCondition.getEncounter() != null ? srcCondition.getEncounter().getReference() : null,
            bulkload);

    var visitDetailId =
        resourceOmopReferenceUtils.getVisitDetailId(
            srcCondition.getEncounter() != null ? srcCondition.getEncounter().getReference() : null,
            bulkload);

    var startDate = resourceOnset.getStartDate(srcCondition);
    var endDate = resourceOnset.getEndDate(srcCondition);

    if (startDate == null) {
      log.warn("No start date found. Skip resource.");
      noStartDateCounter.increment();
      return null;
    }

    var conditionCoding = srcCondition.getCode().getCodingFirstRep();

    if (Strings.isNullOrEmpty(conditionCoding.getCode())) {
      log.warn("No code found. Condition resource is invalid. Skip resource.");
      invalidCodeCounter.increment();
      return null;
    }

    var conditionSourceConceptId = getSourceConceptId(conditionCoding);

    if (conditionSourceConceptId == null) {
      log.warn("No matching concept found. Skip resource.");
      noMatchingOmopConceptCounter.increment();
      return null;
    }

    var conditionSourceConcept = findOmopConcepts.getConceptFromId(conditionSourceConceptId);

    if (conditionSourceConcept == null) {
      log.warn("No matching concept found. Skip resource.");
      noMatchingOmopConceptCounter.increment();
      return null;
    }

    var conditionConceptId = conditionSourceConcept.getConceptId();
    var conditionConcept = conditionSourceConcept;

    if (!conditionSourceConcept.getStandardConcept().equals("S")) {
      var mappedConcept =
          findOmopConcepts.getMappedStandardConcept(
              conditionSourceConceptId, Collections.singletonList(OMOP_DOMAIN_CONDITION));

      if (mappedConcept != null) {
        conditionConceptId = mappedConcept.getConceptId();
        conditionConcept = mappedConcept;
      }
    }

    var conditionTypeConceptId = CONCEPT_EHR;

    var conditionStatusConceptId = getConditionStatusConceptId(srcCondition);

    var stopReason = getStopReason(srcCondition);

    var providerIdRef = srcCondition.getRecorder();
    var providerId =
        resourceOmopReferenceUtils.getProviderId(
            providerIdRef != null ? providerIdRef.getReference() : null, bulkload);

    var conditionSourceValue = conditionCoding.getCode();

    var conditionSourceVocabularyId = getSourceVocabularyId(conditionCoding);

    var conditionStatusSourceValue =
        srcCondition.getClinicalStatus() != null
                && !srcCondition.getClinicalStatus().isEmpty()
                && !srcCondition.getClinicalStatus().getCoding().isEmpty()
            ? srcCondition.getClinicalStatus().getCodingFirstRep().getCode()
            : null;

    var conditionId = getConditionOccurrenceId(srcCondition.getId());

    var domain = conditionConcept.getDomainId();

    if (domain.equals(OMOP_DOMAIN_CONDITION)) {
      var conditionOccurrence =
          createConditionOccurrence(
              conditionId,
              personId,
              conditionConceptId,
              startDate,
              endDate,
              conditionTypeConceptId,
              conditionStatusConceptId,
              stopReason,
              visitOccId,
              visitDetailId,
              conditionSourceValue,
              conditionSourceConceptId,
              conditionStatusSourceValue,
              providerId);

      wrapper.setConditionOccurrence(conditionOccurrence);

      // create additional entries for body site
      createBodySiteEntries(srcCondition, wrapper, conditionOccurrence);

      // create additional entries for severity
      createSeverityEntries(srcCondition, wrapper, conditionOccurrence);

      // create additional entries for stage
      createStageEntries(srcCondition, wrapper, conditionOccurrence);

      // create additional entries for diagnostic confidence
      createDiagnosticConfidenceEntries(srcCondition, wrapper, conditionOccurrence);

      // create additional entries for ICD localization
      createIcdLocalizationEntries(srcCondition, wrapper, conditionOccurrence);

    } else if (domain.equals(OMOP_DOMAIN_OBSERVATION)) {
      var observation =
          createObservation(
              conditionId,
              personId,
              conditionConceptId,
              startDate,
              conditionTypeConceptId,
              conditionSourceValue,
              conditionSourceConceptId,
              visitOccId,
              visitDetailId,
              providerId);

      wrapper.setObservation(observation);

    } else if (domain.equals(OMOP_DOMAIN_MEASUREMENT)) {
      var measurement =
          createMeasurement(
              conditionId,
              personId,
              conditionConceptId,
              startDate,
              conditionTypeConceptId,
              conditionSourceValue,
              conditionSourceConceptId,
              visitOccId,
              visitDetailId,
              providerId);

      wrapper.setMeasurement(measurement);

    } else if (domain.equals(OMOP_DOMAIN_PROCEDURE)) {
      var procedureOccurrence =
          createProcedureOccurrence(
              conditionId,
              personId,
              conditionConceptId,
              startDate,
              conditionTypeConceptId,
              conditionSourceValue,
              conditionSourceConceptId,
              visitOccId,
              visitDetailId,
              providerId);

      wrapper.setProcedureOccurrence(procedureOccurrence);
    }

    successCounter.increment();
    return wrapper;
  }

  // ... [Additional methods for creating OMOP records and handling specific mappings]
