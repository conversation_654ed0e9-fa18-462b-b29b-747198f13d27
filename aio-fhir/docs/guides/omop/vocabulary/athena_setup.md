# OHDSI Athena Configuration Guide

## Overview

OHDSI Athena is a vocabulary browsing and download tool that provides access to the standardized vocabularies used in the OMOP Common Data Model (CDM). This guide documents the process of configuring and using Athena to obtain vocabularies for a FHIR to OMOP transformation pipeline.

## Athena Features

Athena provides several key features:

1. **Vocabulary Browsing**: Search and explore standardized vocabularies by domain, concept class, and vocabulary type
2. **Concept Relationships**: View hierarchical relationships between concepts
3. **Vocabulary Download**: Download selected vocabularies for use in OMOP CDM implementations
4. **User Management**: Register and manage user accounts for accessing licensed vocabularies

## Account Setup and Registration

### Creating an Account

1. Navigate to [Athena](https://athena.ohdsi.org/)
2. Click on the "Login" button in the top navigation bar
3. On the login page, look for a registration link (typically "Register" or "Create Account")
4. Complete the registration form with your information
5. Verify your email address through the confirmation email

### Logging In

1. Navigate to [<PERSON>](https://athena.ohdsi.org/)
2. Click on the "Login" button in the top navigation bar
3. Enter your email address and password
4. Click "Login"

## Browsing Vocabularies

### Search Interface

The Athena search interface allows you to:

1. **Search by Keyword**: Enter terms in the search box (e.g., "diabetes", "aspirin")
2. **Filter by Domain**: Narrow results to specific domains (Condition, Drug, Procedure, etc.)
3. **Filter by Vocabulary**: Limit results to specific vocabularies (SNOMED CT, LOINC, RxNorm, etc.)
4. **Filter by Concept Class**: Filter by concept types (Ingredient, Clinical Drug, etc.)
5. **Filter by Standard Concept**: Show only standard concepts used in OMOP analyses

### Exploring Domains

Athena organizes concepts into domains that correspond to OMOP CDM tables:

- **Condition**: Diagnoses and conditions (maps to CONDITION_OCCURRENCE)
- **Drug**: Medications and drugs (maps to DRUG_EXPOSURE)
- **Procedure**: Medical procedures (maps to PROCEDURE_OCCURRENCE)
- **Measurement**: Lab tests and measurements (maps to MEASUREMENT)
- **Observation**: Clinical observations (maps to OBSERVATION)
- **Device**: Medical devices (maps to DEVICE_EXPOSURE)

## Downloading Vocabularies

### Selecting Vocabularies

1. Navigate to the Download section by clicking "Download" in the top navigation
2. Select the vocabularies you need for your OMOP CDM implementation
   - Standard vocabularies (SNOMED CT, LOINC, RxNorm) are pre-selected
   - Add source vocabularies used in your FHIR data (ICD-10, CPT4, etc.)
3. For licensed vocabularies (e.g., SNOMED CT), you'll need to provide license information

### License Requirements

Some vocabularies require licenses:

- **SNOMED CT**: Requires a SNOMED International license
- **CPT4**: Requires an AMA license
- **ICD-10-CM/PCS**: Free for use in the US, may require license internationally

### Download Process

1. After selecting vocabularies, click "Download" button
2. Accept the terms of service
3. For licensed vocabularies, you'll be prompted to provide license information
4. The system will generate a ZIP file containing CSV files for each vocabulary table
5. Download the ZIP file when ready (this may take some time depending on selected vocabularies)

## Vocabulary Tables

The downloaded ZIP file contains CSV files for the following OMOP vocabulary tables:

1. **CONCEPT**: Contains all concepts across all vocabularies
2. **CONCEPT_RELATIONSHIP**: Defines relationships between concepts
3. **CONCEPT_ANCESTOR**: Contains hierarchical relationships
4. **CONCEPT_SYNONYM**: Contains alternative names for concepts
5. **VOCABULARY**: Metadata about included vocabularies
6. **DOMAIN**: Defines the domains used in the OMOP CDM
7. **CONCEPT_CLASS**: Defines the concept classes
8. **RELATIONSHIP**: Defines the types of relationships between concepts

## Loading Vocabularies into OMOP CDM

### Database Setup

1. Create the vocabulary tables in your OMOP CDM database using the [OMOP CDM DDL scripts](https://github.com/OHDSI/CommonDataModel/tree/v5.4/inst/ddl/5.4/postgresql)
2. Ensure your database has sufficient space (vocabularies can be several GB)

### Loading Process

#### Option 1: Using SQL Bulk Load

```sql
-- Example for PostgreSQL
COPY concept FROM '/path/to/extracted/CONCEPT.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
COPY concept_relationship FROM '/path/to/extracted/CONCEPT_RELATIONSHIP.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
COPY concept_ancestor FROM '/path/to/extracted/CONCEPT_ANCESTOR.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
COPY concept_synonym FROM '/path/to/extracted/CONCEPT_SYNONYM.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
COPY vocabulary FROM '/path/to/extracted/VOCABULARY.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
COPY domain FROM '/path/to/extracted/DOMAIN.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
COPY concept_class FROM '/path/to/extracted/CONCEPT_CLASS.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
COPY relationship FROM '/path/to/extracted/RELATIONSHIP.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
```

#### Option 2: Using ETL Tools

Many ETL tools can be used to load the vocabulary files:

```python
# Example using pandas in Python
import pandas as pd
import sqlalchemy

# Create database connection
engine = sqlalchemy.create_engine('postgresql://username:password@localhost:5432/omop_cdm')

# Load CONCEPT table
concept_df = pd.read_csv('/path/to/extracted/CONCEPT.csv', sep='\t')
concept_df.to_sql('concept', engine, if_exists='append', index=False, schema='vocabulary')

# Repeat for other vocabulary tables
```

## Integration with FHIR to OMOP Pipeline

### Vocabulary Configuration

When setting up a FHIR to OMOP transformation pipeline, configure the following:

1. **Database Connection**: Point your ETL process to the OMOP CDM database with loaded vocabularies
2. **Vocabulary Version**: Document the version of vocabularies used in your pipeline
3. **Mapping Configuration**: Configure source-to-standard concept mappings based on your FHIR resources

### Example Configuration File

```json
{
  "vocabulary": {
    "database": {
      "connection_string": "postgresql://username:password@localhost:5432/omop_cdm",
      "schema": "vocabulary"
    },
    "version": "v5.0 23-JUN-24",
    "mappings": {
      "condition": {
        "source_vocabulary": "ICD10CM",
        "target_vocabulary": "SNOMED"
      },
      "medication": {
        "source_vocabulary": "RxNorm",
        "target_vocabulary": "RxNorm"
      },
      "observation": {
        "source_vocabulary": "LOINC",
        "target_vocabulary": "LOINC"
      }
    }
  }
}
```

## Updating Vocabularies

OHDSI releases updated vocabularies regularly. To update:

1. Download the latest vocabularies from Athena
2. Back up your existing vocabulary tables
3. Truncate the existing vocabulary tables
4. Load the new vocabulary files
5. Update your ETL process to use the new vocabulary version

## Best Practices

1. **Document Vocabulary Versions**: Always document which vocabulary version is used in your ETL process
2. **Include All Necessary Vocabularies**: Ensure you download all vocabularies used in your source data
3. **Verify Concept Mappings**: After loading vocabularies, verify that your source codes map to appropriate standard concepts
4. **Create Indexes**: Add appropriate indexes to vocabulary tables for better query performance
5. **Regular Updates**: Update vocabularies periodically to ensure you have the latest concept definitions and relationships

## Troubleshooting

### Common Issues

1. **Missing Concepts**: If source codes don't map to standard concepts, check if you've downloaded all necessary vocabularies
2. **Performance Issues**: Add indexes to the CONCEPT and CONCEPT_RELATIONSHIP tables for better query performance
3. **License Issues**: Ensure you have proper licenses for restricted vocabularies
4. **Space Issues**: Vocabulary tables can be large; ensure your database has sufficient space

### SQL Queries for Verification

```sql
-- Check if vocabularies are loaded correctly
SELECT vocabulary_id, vocabulary_name, vocabulary_version 
FROM vocabulary;

-- Check concept counts by domain
SELECT domain_id, COUNT(*) 
FROM concept 
WHERE standard_concept = 'S' 
GROUP BY domain_id;

-- Verify mapping from a source code to standard concept
SELECT c1.concept_code AS source_code, 
       c1.concept_name AS source_name,
       c2.concept_id AS standard_concept_id,
       c2.concept_name AS standard_name
FROM concept c1
JOIN concept_relationship cr ON c1.concept_id = cr.concept_id_1
JOIN concept c2 ON cr.concept_id_2 = c2.concept_id
WHERE c1.vocabulary_id = 'ICD10CM' 
  AND c1.concept_code = 'E11.9'
  AND cr.relationship_id = 'Maps to'
  AND c2.standard_concept = 'S';
```

## References

1. [OHDSI Athena](https://athena.ohdsi.org/)
2. [The Book of OHDSI: Standardized Vocabularies](https://ohdsi.github.io/TheBookOfOhdsi/StandardizedVocabularies.html)
3. [OMOP Common Data Model](https://github.com/OHDSI/CommonDataModel)
4. [OHDSI Vocabulary ETL Documentation](https://www.ohdsi.org/web/wiki/doku.php?id=documentation:vocabulary_etl)
