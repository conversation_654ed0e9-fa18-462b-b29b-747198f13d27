// ConditionParser.java from NACHC-CAD/fhir-to-omop repository
// Source: https://github.com/NACHC-CAD/fhir-to-omop/blob/main/src/main/java/org/nachc/tools/fhirtoomop/fhir/parser/condition/ConditionParser.java

package org.nachc.tools.fhirtoomop.fhir.parser.condition;

import java.util.Date;

import org.hl7.fhir.dstu3.model.Coding;
import org.hl7.fhir.dstu3.model.Condition;
import org.hl7.fhir.dstu3.model.Condition.ConditionClinicalStatus;
import org.hl7.fhir.dstu3.model.Condition.ConditionVerificationStatus;
import org.nachc.tools.fhirtoomop.fhir.util.id.FhirUtil;

public class ConditionParser {

    private Condition con;
    
    public ConditionParser(Condition con) {
        this.con = con;
    }
    
    public String getConditionId() {
        return FhirUtil.getIdUnqualified(this.con.getId());
    }
    
    public Coding getCoding() {
        // TODO: (JEG) code can have multiple codings
        // (were just taking the first one for now)
        try {
            return this.con.getCode().getCodingFirstRep();
        } catch (NullPointerException exp) {
            return null;
        }
    }
    
    public String getPatientId() {
        try {
            String rtn = this.con.getSubject().getReference();
            rtn = FhirUtil.getIdFromReference(rtn);
            return rtn;
        } catch (Exception exp) {
            return null;
        }
    }
    
    public String getEncounterId() {
        try {
            String rtn = this.con.getContext().getReference();
            rtn = FhirUtil.getIdFromReference(rtn);
            return rtn;
        } catch (Exception exp) {
            return null;
        }
    }
    
    public Date getOnsetDate() {
        try {
            Date rtn = this.con.getOnsetDateTimeType().getValue();
            return rtn;
        } catch (Exception exp) {
            return null;
        }
    }
    
    public Date getAbatementDate() {
        try {
            Date rtn = this.con.getAbatementDateTimeType().getValue();
            return rtn;
        } catch (Exception exp) {
            return null;
        }
    }
    
    public ConditionClinicalStatus getClinicalStatus() {
        try {
            ConditionClinicalStatus rtn = this.con.getClinicalStatus();
            return rtn;
        } catch (Exception exp) {
            return null;
        }
    }
    
    public ConditionVerificationStatus getVerificationStatus() {
        try {
            ConditionVerificationStatus rtn = this.con.getVerificationStatus();
            return rtn;
        } catch (Exception exp) {
            return null;
        }
    }
    
}
