# Manus Research prompt

# You are a specialized assistant in healthcare informatics and clinical data interoperability. Your mission is to create a comprehensive and organized repository of resources for building a data transformation pipeline from HL7 FHIR to OMOP CDM, with special emphasis on code examples, practical configurations, and an initial pipeline to start our project.

## Main Objective

Collect, evaluate, and organize all technical resources needed to develop a functional pipeline that transforms data from FHIR (R4) format to OMOP CDM (v5.3/v5.4), with particular attention to open-source tools, proven mapping strategies, technical documentation, and implementation examples.

## Technology Preferences

- **Prioritize solutions based on Python and SQL** for the main pipeline, as these are the technologies I'm most familiar with
- **Provide detailed and well-commented code examples** in Python for each pipeline component
- **Include optimized SQL scripts** for transformation and loading operations in OMOP
- **Don't limit yourself exclusively to these technologies** if others offer significant advantages or are standard in the field
- **When recommending tools in other languages** (Java, JavaScript, etc.), include:
    - Clear justification of why they are superior for that specific component
    - Learning resources to quickly master that technology
    - If possible, alternatives in Python/SQL even if suboptimal

## Important Note on Resources

While I've provided specific resources below as starting points, **you should independently research and identify additional current projects, tools, and resources** that may be valuable or superior to those suggested. The field evolves rapidly, and newer solutions may have emerged. **Do not limit yourself to only the resources listed** - verify their current status and complement them with your own findings of relevant up-to-date resources.

## Key Elements to Research

### 1. Code Repositories and Open Source Tools

Research in depth and provide documentation, direct links, and code examples for:

- **NACHC-CAD/fhir-to-omop** ([https://github.com/NACHC-CAD/fhir-to-omop](https://github.com/NACHC-CAD/fhir-to-omop)):
    - Examine its architecture, main components, and project structure
    - Identify specific methods for processing each type of FHIR resource
    - Extract Java code examples for mapping at least Patient, Observation, and Condition
- **OHDSIETL-German-FHIR-Core** ([https://github.com/OHDSI/OHDSIETL-German-FHIR-Core](https://github.com/OHDSI/OHDSIETL-German-FHIR-Core)):
    - Analyze its capabilities for processing specific FHIR resources
    - Document how it configures SQL queries for transformation
    - Extract SQL scripts for mapping at least 3 key FHIR resources
- **Microsoft FHIR-to-OMOP** ([https://github.com/microsoft/FHIR-Tools-for-Anonymization](https://github.com/microsoft/FHIR-Tools-for-Anonymization)):
    - Analyze its components in Azure Data Factory
    - Provide JSON configuration examples for pipelines
    - Document the reference architecture in Azure
- **Google Healthcare Data Harmonization** ([https://github.com/GoogleCloudPlatform/healthcare-data-harmonization](https://github.com/GoogleCloudPlatform/healthcare-data-harmonization)):
    - Extract Whistle DSL examples for FHIR to OMOP mappings
    - Document the configuration for Google Cloud deployment
    - Analyze the advantages of its domain-specific language approach
- **WhiteRabbit and Rabbit-in-a-Hat** ([https://github.com/OHDSI/WhiteRabbit](https://github.com/OHDSI/WhiteRabbit)):
    - Provide a step-by-step guide with screenshots for scanning FHIR data
    - Extract examples of generated mapping specification files
- **Synthea** ([https://github.com/synthetichealth/synthea](https://github.com/synthetichealth/synthea)):
    - Document configuration to generate data simultaneously in FHIR and OMOP
    - Extract modules and templates that define mappings in Synthea

### 2. Mapping Strategies and Reference Resources

Detail with direct links and code snippets:

- **Vulcan Project (HL7-OHDSI)**:
    - Provide direct access to the FHIR-to-OMOP Implementation Guide ([https://build.fhir.org/ig/HL7/fhir-omop-ig/](https://build.fhir.org/ig/HL7/fhir-omop-ig/))
    - Extract mapping matrices from FHIR resources to OMOP tables
    - Document clinical use cases validated in the project
- **Vocabulary mapping strategies**:
    - Provide concrete code examples (SQL, Python, Java) for mapping:
        - LOINC → OMOP Standard Concepts
        - SNOMED CT → OMOP Standard Concepts
        - RxNorm → OMOP Standard Concepts
    - Include examples of using the FHIR Terminology Server API ([https://terminology.hl7.org/](https://terminology.hl7.org/))
    - Document configuration for OHDSI Athena ([https://athena.ohdsi.org/](https://athena.ohdsi.org/)) as a source of mappings
- **Specific documentation for FHIR resources**:
    - For each mapping pair, provide pseudocode or real transformation code:
        - Patient → Person (with handling of demographics, race, ethnicity)
        - Encounter → Visit_Occurrence (with mapping of visit types)
        - Condition → Condition_Occurrence (with code normalization strategies)
        - Observation → Measurement/Observation (with handling of quantitative vs qualitative values)
        - MedicationRequest/MedicationAdministration → Drug_Exposure (with strategies for dosing and duration)
        - Procedure → Procedure_Occurrence (with mapping of procedure types)

### 3. Real Implementation Cases

Analyze with links to technical documentation:

- **All of Us Research Program**:
    - Document the technical architecture ([https://www.researchallofus.org/data-tools/data-access/](https://www.researchallofus.org/data-tools/data-access/))
    - Extract key components of their ETL infrastructure
    - Identify lessons learned in their transformation process
- **National COVID Cohort Collaborative (N3C)**:
    - Analyze the harmonization scripts used ([https://github.com/National-COVID-Cohort-Collaborative](https://github.com/National-COVID-Cohort-Collaborative))
    - Document the specific mappings for COVID data
    - Extract lessons on large-scale processing
- **MENDS-on-FHIR**:
    - Analyze in detail its use of Whistle for transformation
    - Provide examples of Whistle configurations for mapping

### 4. Infrastructure Setup

Provide with specific code examples:

- **Reference architecture**:
    - Design a detailed architecture diagram for a FHIR to OMOP pipeline
    - Include data flows, components, and specific technologies
    - Provide alternatives for cloud vs on-premise environments
- **Technical requirements**:
    - Detail specific configurations for PostgreSQL optimized for OMOP
    - Include Docker scripts for vocabulary service deployment
    - Document middleware options with configuration examples (Apache NiFi, Kafka, etc.)
- **Development and testing environments**:
    - Provide a complete docker-compose.yml for local configuration
    - Include templates for deployment on AWS, Azure, and GCP
    - Document validation and testing tools for the transformation

## 5. Sample Initial Pipeline

Design a starter pipeline for our project that includes:

- **Complete example code based on Python and SQL** for a functional FHIR to OMOP flow with:
    - Python ETL scripts for at least Patient, Encounter, Condition, and Observation
    - Optimized SQL queries for insertion into OMOP
    - Components for validating transformed data
    - Examples of using Python libraries for FHIR (fhir.resources, fhirclient) and databases (SQLAlchemy, pandas)
- **Step-by-step implementation guide**:
    - Detailed instructions from repository cloning to execution
    - Explanation of each component and its role in the pipeline
    - Documentation of file structures and dependencies
- **Incremental strategy**:
    - Plan for progressively expanding to more FHIR resource types
    - Metrics to validate transformation quality
    - Extension points for customization

## 6. Technical Document on Cutting-Edge Technologies

Develop a rigorous technical document that evaluates:

- **Scalable architectures for clinical data processing**:
    - Comparison between batch vs streaming architectures
    - Evaluation of big data technologies (Spark, Flink, etc.) for processing
    - Performance and latency analysis for different data volumes
- **Semantic technologies for mapping improvement**:
    - State of the art in the use of ontologies and RDF/OWL technologies
    - Tools for semantic validation (SHACL, ShEx)
    - Integration with terminology servers
- **Orchestration and monitoring strategies**:
    - Evaluation of tools like Airflow, NiFi, and Kubeflow
    - Metrics and KPIs to monitor quality and completeness
    - Integration with alert systems and dashboards
- **Security and regulatory compliance**:
    - Anonymization and pseudonymization techniques
    - Strategies for auditing and tracing transformations
    - GDPR, HIPAA, and other relevant regulatory considerations

## Constraints and Priorities

- **Focus exclusively on FHIR → OMOP transformation** (not OMOP → FHIR or conversions with openEHR)
- **Prioritize updated resources** (2020-2025) and actively maintained solutions
- **Provide direct links** to all repositories, documents, and tools mentioned
- **Include functional code examples** for each key transformation component
- **Evaluate maturity and limitations** of each tool, indicating its status (production, beta, proof of concept)
- **Document strategies to validate** the quality and fidelity of transformations
- **Identify known gaps and challenges**, especially in semantic preservation and terminology handling

## Delivery Format

Organize the repository with:

1. **Categorized inventory of tools** with links, description, maturity assessment, and usage examples
2. **Python and SQL code library** with examples classified by FHIR resource type and destination OMOP table
3. **Sample pipeline** complete and documented to start implementation, preferably in Python/SQL
4. **Step-by-step guides** for configuration and implementation of each component
5. **Technical document** on architecture and cutting-edge technologies
6. **Architecture diagrams** in editable format ([draw.io](http://draw.io/), PlantUML)
7. **Mapping matrices** between FHIR resources and OMOP tables with transformation rules
8. **Collection of SQL scripts** for post-transformation validation
9. **Learning resources** for non-Python/SQL technologies when necessary