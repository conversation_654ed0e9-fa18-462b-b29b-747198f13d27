# FHIR Data Loading Strategies: Overview

This document provides an overview of our FHIR data loading strategies and directs you to the detailed documentation.

## Executive Summary

We have established two primary methods for loading FHIR data:

1. **Direct Transaction Bundles**: For bulk loading with permissive server configuration
2. **Selective Loading**: For incremental loading with reference integrity

For detailed guides on these methods, please refer to the documentation in the [data-loading directory](../data-loading/).

## Our Implemented Methods

| Method | Description | Best For | Documentation |
|--------|-------------|----------|--------------|
| **Direct Transaction Bundles** | Converts NDJSON files to transaction bundles and sends them directly to the FHIR server with permissive configuration | Bulk loading, initial data population | [Direct Transaction Tutorial](../data-loading/direct-transaction-tutorial.md) |
| **Selective Loading** | Analyzes references between resources and loads only those with satisfied dependencies | Incremental loading, reference integrity | [Selective Loading Guide](../data-loading/selective-loading.md) |

For a quick comparison of both methods, see the [Quick Reference Guide](../data-loading/quick-reference.md).

## Server Configuration

For optimal data loading, especially with the Transaction Bundle approach, we recommend a temporary configuration for your HAPI FHIR server during the import process.

### Temporary Import Configuration vs. Default Settings

| Setting | Default (Production) | Import Mode | Purpose |
|---------|---------------------|-------------|---------|
| `validation.enabled` | `true` | `false` | Disables schema validation of resources |
| `validation.request_validator.mode` | `PERMISSIVE` | `NONE` | Disables request validation |
| `auto_create_placeholder_reference_targets` | `false` | `true` | Creates placeholder resources for missing references |
| `enforce_referential_integrity_on_write` | `true` | `false` | Allows resources with unresolved references |
| `enforce_referential_integrity_on_delete` | `true` | `false` | Allows deletion without checking references |

### Implementation in docker-compose.yml

We've structured our configuration file to make this process clear and reversible:

```yaml
# Basic FHIR server settings
- hapi.fhir.default_encoding=json             # Sets default encoding to JSON
- hapi.fhir.fhir_version=${FHIR_VERSION:-R4}  # Sets the FHIR version to use

# Bulk operations configuration
- hapi.fhir.bulk_export_enabled=true          # Enables FHIR bulk export feature
- hapi.fhir.bulk_import_enabled=true          # Enables FHIR bulk import feature
- hapi.fhir.client_id_strategy=ANY            # Allows any client ID for bulk operations

# FHIR Server Data Validation Configuration
# By default, the server enforces data integrity and validation
# For bulk data loading, uncomment the following lines to disable validation and referential integrity checks
# After completing data import, comment these lines again to restore default security and integrity settings
# -----------------------------------------------------------------------------------------------------
# - hapi.fhir.validation.enabled=false                        # Disables validation
# - hapi.fhir.validation.request_validator.mode=NONE          # Disables request validation
# - hapi.fhir.auto_create_placeholder_reference_targets=true  # Creates placeholder resources for missing references
# - hapi.fhir.enforce_referential_integrity_on_write=false    # Disables referential integrity checks on write
# - hapi.fhir.enforce_referential_integrity_on_delete=false   # Disables referential integrity checks on delete
```

### Why These Settings Matter

**Critical Note**: These settings are essential for the Transaction Bundle approach to work with complex reference relationships. With default server settings (validation and referential integrity checks enabled), the server will reject resources with unresolved references, making it difficult to load interconnected data.

The import settings allow the server to:
1. Bypass validation of incoming resources
2. Automatically create placeholder resources for missing references
3. Accept resources even when they reference non-existent resources
4. Process bulk import operations

### Recommended Workflow

1. **Before Data Import**:
   - Uncomment the data import configuration lines
   - Restart the FHIR server: `./manage-fhir-server.sh restart postgres`

2. **After Data Import**:
   - Comment out the data import configuration lines
   - Restart the FHIR server to restore default settings
   - Verify data integrity with appropriate queries

This approach allows you to temporarily relax constraints for data loading while maintaining proper security and integrity for normal operation.

## Implementation Overview

Both of our data loading methods are implemented in Python scripts located in the `servers/fhir-server/scripts/transaction_bundles` directory:

- `ndjson_to_bundle.py`: Converts NDJSON files to transaction bundles
- `send_bundle.py`: Sends transaction bundles to the FHIR server
- `selective_loader.py`: Implements the selective loading approach

For detailed implementation steps, please refer to the [Manual Bundle Tutorial](../data-loading/manual-bundle-tutorial.md).

## Summary

This document provided an overview of our FHIR data loading strategies. For detailed implementation guides, please refer to:

- [Quick Reference Guide](../data-loading/quick-reference.md)
- [Direct Transaction Tutorial](../data-loading/direct-transaction-tutorial.md)
- [Selective Loading Guide](../data-loading/selective-loading.md)
- [Manual Bundle Tutorial](../data-loading/manual-bundle-tutorial.md)


