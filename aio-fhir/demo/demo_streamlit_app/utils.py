"""
Utility functions for the FHIR Server Demo Streamlit application.

This module provides helper functions to interact with the FHIR server
and integrate with existing scripts from the fhir-omop project.
"""

import sys
import os
import requests
import subprocess
import json
import traceback
import logging
import re
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Create a logger for this module
logger = logging.getLogger(__name__)

# Add the parent directory to the Python path
# This allows us to import modules from the fhir-omop project
PROJECT_ROOT = Path(__file__).parent.parent.parent
SCRIPT_DIR = PROJECT_ROOT / "servers" / "fhir-server" / "scripts"
sys.path.insert(0, str(SCRIPT_DIR))

# Define base directories
BASE_DIR = PROJECT_ROOT
DATA_DIR = BASE_DIR / "data"
GENERATED_BUNDLES_DIR = DATA_DIR / "generated_bundles"

def test_fhir_connection(server_url):
    """
    Test the connection to the FHIR server.

    Args:
        server_url (str): URL of the FHIR server

    Returns:
        tuple: (success, response) where success is a boolean and response is the server response or error message
    """
    try:
        response = requests.get(f"{server_url}/metadata", timeout=10)
        if response.status_code == 200:
            return True, response.json()
        else:
            return False, f"Error: Status code {response.status_code}"
    except Exception as e:
        return False, f"Error: {str(e)}"

def get_available_datasets():
    """
    Get the available datasets for loading into the FHIR server.

    Returns:
        list: List of dictionaries with dataset information
    """
    datasets = []

    # Check for sample FHIR data directories
    sample_dir = DATA_DIR / "sample_fhir"
    if sample_dir.exists():
        for item in sample_dir.iterdir():
            if item.is_dir() and any(file.suffix == ".ndjson" for file in item.glob("**/*")):
                datasets.append({
                    "name": item.name,
                    "path": str(item),
                    "type": "sample"
                })

    # Check for MIMIC data
    mimic_dir = DATA_DIR / "fhir_data" / "mimic-iv-clinical-database-demo-on-fhir-2.0" / "mimic-fhir"
    if mimic_dir.exists() and any(file.suffix == ".ndjson" for file in mimic_dir.glob("**/*")):
        datasets.append({
            "name": "MIMIC-IV Demo (100 patients)",
            "path": str(mimic_dir),
            "type": "mimic"
        })

    # If no datasets found, add a placeholder
    if not datasets:
        datasets.append({
            "name": "No datasets found",
            "path": "",
            "type": "none"
        })

    return datasets

def load_data_transaction_bundles(input_dir, server_url, batch_size=500, use_existing_bundles=False):
    """
    Load data using the Transaction Bundles method.

    This function implements the process described in the direct-transaction-tutorial.md:
    1. Convert NDJSON files to transaction bundles (if use_existing_bundles=False)
    2. Load the bundles to the FHIR server

    Args:
        input_dir (str): Path to the directory containing NDJSON files
        server_url (str): URL of the FHIR server
        batch_size (int): Maximum number of resources per bundle
        use_existing_bundles (bool): If True, skip conversion and use existing bundles

    Returns:
        tuple: (success, result) where success is a boolean and result contains output and statistics
    """
    # Determine input and output directories
    input_path = Path(input_dir)
    input_name = input_path.name
    output_dir = GENERATED_BUNDLES_DIR / f"{input_name}-bundles"

    # Step 1: Convert NDJSON to bundles (if needed)
    if not use_existing_bundles:
        ndjson_script = SCRIPT_DIR / "data_loading" / "ndjson_to_bundle.py"

        # Check if input directory exists
        if not input_path.exists():
            return False, {
                "error": f"Input directory not found: {input_dir}",
                "details": "Please check the path and ensure the directory exists."
            }

        # Check if input directory contains NDJSON files
        ndjson_files = list(input_path.glob("*.ndjson"))
        if not ndjson_files:
            return False, {
                "error": f"No NDJSON files found in {input_dir}",
                "details": "The directory must contain FHIR resources in NDJSON format."
            }

        # Create command for conversion
        cmd_convert = [
            "python", str(ndjson_script),
            "--input-dir", str(input_dir),
            "--output-dir", str(output_dir),
            "--batch-size", str(batch_size)
        ]

        # Execute conversion
        try:
            result = subprocess.run(cmd_convert, capture_output=True, text=True, check=True)
            conversion_output = result.stdout

            # Check if output directory was created
            if not output_dir.exists():
                return False, {
                    "error": "Bundle conversion failed",
                    "details": "The output directory was not created. Check the conversion output for details.",
                    "conversion_output": conversion_output
                }

            # Check if bundles were created
            bundle_files = list(output_dir.glob("**/*.json"))
            if not bundle_files:
                return False, {
                    "error": "No bundle files were created",
                    "details": "The conversion process did not generate any bundle files.",
                    "conversion_output": conversion_output
                }

        except subprocess.CalledProcessError as e:
            return False, {
                "error": "Error during NDJSON to bundle conversion",
                "details": e.stderr,
                "command": " ".join(cmd_convert)
            }
    else:
        # Check if output directory exists when using existing bundles
        if not output_dir.exists():
            return False, {
                "error": f"Bundle directory not found: {output_dir}",
                "details": "When using existing bundles, the bundle directory must already exist."
            }

        # Check if output directory contains bundle files
        bundle_files = list(output_dir.glob("**/*.json"))
        if not bundle_files:
            return False, {
                "error": f"No bundle files found in {output_dir}",
                "details": "The directory must contain FHIR transaction bundles in JSON format."
            }

    # Step 2: Load bundles to the server
    load_script = SCRIPT_DIR / "data_loading" / "load_all_bundles.py"

    # Create command for loading
    cmd_load = [
        "python", str(load_script),
        "--bundle-dir", str(output_dir),
        "--server-url", server_url,
        "--export-performance"
    ]

    # Execute loading
    try:
        # Run the command with a timeout to prevent hanging
        result = subprocess.run(cmd_load, capture_output=True, text=True, check=True, timeout=300)
        load_output = result.stdout

        # Extract statistics from output
        stats = {}
        resources_processed = None
        processing_time = None
        resources_per_second = None
        failed_bundles = []

        # Look for the failed bundles section
        failed_bundles_section = False

        for line in load_output.split("\n"):
            # Check for section headers
            if "FAILED BUNDLES:" in line:
                failed_bundles_section = True
            elif "================" in line and failed_bundles_section:
                failed_bundles_section = False

            # Extract statistics
            if "Total Resources Processed:" in line:
                stats["resources"] = line.strip()
                try:
                    resources_processed = line.split(":")[1].strip()
                except:
                    resources_processed = "Unknown"
            elif "Total Processing Time:" in line:
                stats["time"] = line.strip()
                try:
                    processing_time = line.split(":")[1].strip()
                except:
                    processing_time = "Unknown"
            elif "Resources Per Second:" in line:
                stats["throughput"] = line.strip()
                try:
                    resources_per_second = line.split(":")[1].strip()
                except:
                    resources_per_second = "Unknown"
            elif "Results:" in line and "successful" in line and "failed" in line:
                stats["results"] = line.strip()

            # Extract failed bundles information
            if failed_bundles_section and "|" in line and not "Resource Type" in line and not "----------------" in line:
                parts = line.split("|")
                if len(parts) >= 4:
                    resource_type = parts[1].strip()
                    file_name = parts[2].strip()
                    resources = parts[3].strip()
                    error = parts[4].strip() if len(parts) > 4 else "Unknown error"

                    failed_bundles.append({
                        "resource_type": resource_type,
                        "file_name": file_name,
                        "resources": resources,
                        "error": error
                    })

        # Extract detailed report information
        report_data = extract_detailed_report(load_output)

        # Log the extracted report data for debugging
        logger.debug(f"Extracted report data: {report_data}")

        # Check if there were any failed bundles
        if failed_bundles:
            # Some bundles failed, but overall process completed
            return True, {
                "output": load_output,
                "stats": stats,
                "summary": {
                    "resources_processed": resources_processed,
                    "processing_time": processing_time,
                    "resources_per_second": resources_per_second,
                    "bundle_directory": str(output_dir),
                    "server_url": server_url,
                    "partial_success": True,
                    "failed_bundles": failed_bundles
                },
                "report": report_data
            }
        else:
            # All bundles loaded successfully
            return True, {
                "output": load_output,
                "stats": stats,
                "summary": {
                    "resources_processed": resources_processed,
                    "processing_time": processing_time,
                    "resources_per_second": resources_per_second,
                    "bundle_directory": str(output_dir),
                    "server_url": server_url
                },
                "report": report_data
            }
    except subprocess.TimeoutExpired:
        error_message = "Timeout error: The loading process took too long to complete."
        suggestion = "Try loading with a smaller batch size or fewer bundles at a time."
        return False, {
            "error": error_message,
            "details": "The command execution timed out after 300 seconds.",
            "suggestion": suggestion,
            "command": " ".join(cmd_load)
        }
    except subprocess.CalledProcessError as e:
        # Try to extract meaningful error information
        error_details = e.stderr
        stdout_output = e.stdout

        # Extract statistics and report data regardless of error
        stats = {}
        resources_processed = None
        processing_time = None
        resources_per_second = None
        failed_bundles = []
        partial_success = False

        # Look for basic statistics in the output
        for line in stdout_output.split("\n"):
            if "Total Resources Processed:" in line:
                stats["resources"] = line.strip()
                try:
                    resources_processed = line.split(":")[1].strip()
                    # If we have processed resources, consider it a partial success
                    if resources_processed and resources_processed != "0":
                        partial_success = True
                except:
                    resources_processed = "Unknown"
            elif "Total Processing Time:" in line:
                stats["time"] = line.strip()
                try:
                    processing_time = line.split(":")[1].strip()
                except:
                    processing_time = "Unknown"
            elif "Resources Per Second:" in line:
                stats["throughput"] = line.strip()
                try:
                    resources_per_second = line.split(":")[1].strip()
                except:
                    resources_per_second = "Unknown"
            elif "Results:" in line and "successful" in line and "failed" in line:
                stats["results"] = line.strip()
                # If we have successful bundles, consider it a partial success
                if "successful" in line and not "0 successful" in line:
                    partial_success = True

        # Extract failed bundles information
        failed_bundles_section = False
        for line in stdout_output.split("\n"):
            if "FAILED BUNDLES:" in line:
                failed_bundles_section = True
                # If we have a failed bundles section, it means some bundles were processed
                partial_success = True
            elif "================" in line and failed_bundles_section:
                failed_bundles_section = False

            if failed_bundles_section and "|" in line and not "Resource Type" in line and not "----------------" in line:
                parts = line.split("|")
                if len(parts) >= 4:
                    resource_type = parts[1].strip() if len(parts) > 1 else "Unknown"
                    file_name = parts[2].strip() if len(parts) > 2 else "Unknown"
                    resources = parts[3].strip() if len(parts) > 3 else "Unknown"
                    error = parts[4].strip() if len(parts) > 4 else "Unknown error"

                    failed_bundles.append({
                        "resource_type": resource_type,
                        "file_name": file_name,
                        "resources": resources,
                        "error": error
                    })

        # Extract detailed report data
        report_data = extract_detailed_report(stdout_output)
        logger.debug(f"Extracted report data from output: {report_data}")

        # Check if we have any meaningful report data
        has_report_data = report_data and any([
            report_data["resource_breakdown"],
            report_data["overall_stats"],
            report_data["performance_metrics"],
            report_data["system_resources"],
            report_data["top_resources"]
        ])

        # If we have report data or processed resources, consider it a partial success
        if has_report_data:
            partial_success = True

        # If we have a partial success (some data was loaded)
        if partial_success:
            error_message = f"Partial success: Some bundles were loaded but {len(failed_bundles)} failed."
            suggestion = "Check the failed bundles details for specific errors."

            # Return as a success with partial_success flag
            return True, {
                "output": stdout_output,
                "stats": stats,
                "summary": {
                    "resources_processed": resources_processed,
                    "processing_time": processing_time,
                    "resources_per_second": resources_per_second,
                    "bundle_directory": str(output_dir),
                    "server_url": server_url,
                    "partial_success": True
                },
                "failed_bundles": failed_bundles,
                "report": report_data,
                "error": error_message,
                "suggestion": suggestion,
                "partial_success": True
            }

        # If no data was loaded, handle as a complete failure
        # Check for common errors in stderr
        if "Connection refused" in error_details:
            error_message = "Connection refused: The FHIR server is not responding."
            suggestion = "Make sure the server is running and accessible at the specified URL."
        elif "404" in error_details:
            error_message = "404 Not Found: The FHIR server endpoint was not found."
            suggestion = "Check the server URL and ensure it includes the correct base path (e.g., /fhir)."
        elif "401" in error_details or "403" in error_details:
            error_message = "Authentication/Authorization error: The request was not authorized."
            suggestion = "Check if the server requires authentication credentials."
        elif "409" in error_details:
            error_message = "409 Conflict: Resources with the same IDs already exist on the server."
            suggestion = "Consider resetting the server or using a different server URL."
        elif "validation" in error_details.lower():
            error_message = "Validation error: The server rejected the resources due to validation issues."
            suggestion = "Make sure the server is configured in permissive mode as described in the tutorial."
        elif "No such file or directory" in error_details:
            error_message = "File not found: The bundle directory or files could not be found."
            suggestion = "Check that the bundle directory exists and contains valid bundle files."
        elif "Permission denied" in error_details:
            error_message = "Permission denied: Cannot access the bundle files."
            suggestion = "Check file permissions for the bundle directory and files."
        else:
            # Try to extract error from stdout if available
            if "Error processing bundle" in stdout_output:
                for line in stdout_output.split("\n"):
                    if "Error processing bundle" in line:
                        error_message = line.strip()
                        break
                suggestion = "Check the server logs for more details about the error."
            else:
                error_message = "Error loading bundles to the FHIR server."
                suggestion = "Check the error details and server logs for more information."

        result = {
            "error": error_message,
            "details": error_details if error_details else "No detailed error information available.",
            "stdout": stdout_output if stdout_output else "No standard output available.",
            "suggestion": suggestion,
            "command": " ".join(cmd_load)
        }

        # Add report data if available
        if report_data:
            result["report"] = report_data

        # Add failed bundles if available
        if failed_bundles:
            result["failed_bundles"] = failed_bundles

        return False, result
    except Exception as e:
        # Handle any other unexpected errors
        return False, {
            "error": f"Unexpected error: {str(e)}",
            "details": traceback.format_exc(),
            "suggestion": "This is an unexpected error. Please report this issue.",
            "command": " ".join(cmd_load)
        }

def load_data_selective(input_dir, server_url, batch_size=500):
    """
    Load data using the Selective Loader method.

    This method analyzes references between resources and loads only those with satisfied dependencies.
    It's safer but may load fewer resources compared to the Transaction Bundles method.

    Args:
        input_dir (str): Path to the directory containing NDJSON files
        server_url (str): URL of the FHIR server
        batch_size (int): Maximum number of resources per batch

    Returns:
        tuple: (success, result) where success is a boolean and result contains output and statistics
    """
    # Check if input directory exists
    input_path = Path(input_dir)
    if not input_path.exists():
        return False, {
            "error": f"Input directory not found: {input_dir}",
            "details": "Please check the path and ensure the directory exists."
        }

    # Check if input directory contains NDJSON files
    ndjson_files = list(input_path.glob("*.ndjson"))
    if not ndjson_files:
        return False, {
            "error": f"No NDJSON files found in {input_dir}",
            "details": "The directory must contain FHIR resources in NDJSON format."
        }

    selective_script = SCRIPT_DIR / "data_loading" / "selective_loader.py"

    cmd = [
        "python", str(selective_script),
        "--data-dir", str(input_dir),
        "--server-url", server_url,
        "--batch-size", str(batch_size),
        "--verify"
    ]

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        output = result.stdout

        # Extract statistics from output
        stats = {}
        total_resources = None
        processing_time = None
        resource_types = None

        for line in output.split("\n"):
            if "Total resources loaded:" in line:
                stats["resources"] = line.strip()
                total_resources = line.split(":")[1].strip()
            elif "Processing completed successfully in" in line:
                stats["time"] = line.strip()
                processing_time = line.replace("Processing completed successfully in", "").strip()
            elif "Resource types loaded:" in line:
                stats["types"] = line.strip()
                resource_types = line.split(":")[1].strip()

        # Create a more detailed result
        return True, {
            "output": output,
            "stats": stats,
            "summary": {
                "resources_processed": total_resources,
                "processing_time": processing_time,
                "resource_types": resource_types,
                "server_url": server_url
            }
        }
    except subprocess.CalledProcessError as e:
        error_details = e.stderr

        # Check for common errors
        if "Connection refused" in error_details:
            error_message = "Connection refused: The FHIR server is not responding."
            suggestion = "Make sure the server is running and accessible at the specified URL."
        elif "404" in error_details:
            error_message = "404 Not Found: The FHIR server endpoint was not found."
            suggestion = "Check the server URL and ensure it includes the correct base path (e.g., /fhir)."
        elif "401" in error_details or "403" in error_details:
            error_message = "Authentication/Authorization error: The request was not authorized."
            suggestion = "Check if the server requires authentication credentials."
        elif "No resources found" in error_details:
            error_message = "No resources found in the input directory."
            suggestion = "Make sure the directory contains valid NDJSON files with FHIR resources."
        else:
            error_message = "Error in selective loading."
            suggestion = "Check the error details for more information."

        return False, {
            "error": error_message,
            "details": error_details,
            "suggestion": suggestion,
            "command": " ".join(cmd)
        }

def get_server_status(server_type="local"):
    """
    Get the status of the FHIR server.

    Args:
        server_type (str): Type of server ('local' or 'cloud')

    Returns:
        tuple: (is_running, status_message)
    """
    if server_type == "local":
        # Check if the local server is running
        cmd = ["docker", "ps", "--filter", "name=fhir-server", "--format", "{{.Status}}"]
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            if result.stdout.strip():
                return True, "Running"
            else:
                return False, "Stopped"
        except subprocess.CalledProcessError:
            return False, "Error checking status"
    else:
        # For cloud server, just check connectivity
        try:
            response = requests.get("https://fhir.aiotek.ai/fhir/metadata", timeout=5)
            if response.status_code == 200:
                return True, "Running"
            else:
                return False, f"Error: Status code {response.status_code}"
        except Exception as e:
            return False, f"Error: {str(e)}"

def start_local_server():
    """
    Start the local FHIR server.

    Returns:
        tuple: (success, output)
    """
    server_script = BASE_DIR / "servers" / "fhir-server" / "manage-fhir-server.sh"

    cmd = ["bash", str(server_script), "start"]

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, f"Error starting server: {e.stderr}"

def stop_local_server():
    """
    Stop the local FHIR server.

    Returns:
        tuple: (success, output)
    """
    server_script = BASE_DIR / "servers" / "fhir-server" / "manage-fhir-server.sh"

    cmd = ["bash", str(server_script), "stop"]

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, f"Error stopping server: {e.stderr}"

def restart_local_server():
    """
    Restart the local FHIR server.

    Returns:
        tuple: (success, output)
    """
    server_script = BASE_DIR / "servers" / "fhir-server" / "manage-fhir-server.sh"

    cmd = ["bash", str(server_script), "restart"]

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, f"Error restarting server: {e.stderr}"

def execute_fhir_query(server_url, query_path):
    """
    Execute a FHIR query and return the results.

    Args:
        server_url (str): URL of the FHIR server
        query_path (str): FHIR query path

    Returns:
        tuple: (success, result) where success is a boolean and result contains the response or error message
    """
    # Ensure the query doesn't start with /
    if query_path.startswith("/"):
        query_path = query_path[1:]

    # Build the complete URL
    url = f"{server_url}/{query_path}"

    try:
        response = requests.get(url, timeout=30)
        if response.status_code == 200:
            return True, response.json()
        else:
            return False, f"Error: Status code {response.status_code}"
    except Exception as e:
        return False, f"Error: {str(e)}"

def format_fhir_bundle_as_dataframe(bundle):
    """
    Convert a FHIR bundle to a DataFrame for tabular visualization.

    Args:
        bundle (dict): FHIR bundle

    Returns:
        pandas.DataFrame: DataFrame containing the bundle data
    """
    import pandas as pd

    if not bundle or "entry" not in bundle:
        return pd.DataFrame()

    entries = bundle["entry"]
    if not entries:
        return pd.DataFrame()

    # Determine the resource type
    resource_type = entries[0]["resource"]["resourceType"]

    # Extract data based on resource type
    if resource_type == "Patient":
        data = []
        for entry in entries:
            resource = entry["resource"]
            patient = {
                "id": resource.get("id", ""),
                "gender": resource.get("gender", ""),
                "birthDate": resource.get("birthDate", ""),
                "active": resource.get("active", False)
            }

            # Extract name
            if "name" in resource and resource["name"]:
                name = resource["name"][0]
                patient["family"] = name.get("family", "")
                patient["given"] = " ".join(name.get("given", []))
            else:
                patient["family"] = ""
                patient["given"] = ""

            data.append(patient)

        return pd.DataFrame(data)

    elif resource_type == "Observation":
        data = []
        for entry in entries:
            resource = entry["resource"]
            obs = {
                "id": resource.get("id", ""),
                "status": resource.get("status", ""),
                "code": resource.get("code", {}).get("text", "") if "code" in resource else "",
                "subject": resource.get("subject", {}).get("reference", "") if "subject" in resource else "",
                "effectiveDateTime": resource.get("effectiveDateTime", "")
            }

            # Extract value
            if "valueQuantity" in resource:
                obs["value"] = f"{resource['valueQuantity'].get('value', '')} {resource['valueQuantity'].get('unit', '')}"
            elif "valueString" in resource:
                obs["value"] = resource["valueString"]
            elif "component" in resource:
                # Handle blood pressure or other component-based observations
                components = []
                for component in resource["component"]:
                    if "code" in component and "valueQuantity" in component:
                        code_text = component["code"].get("text", "")
                        if not code_text and "coding" in component["code"]:
                            for coding in component["code"]["coding"]:
                                if "display" in coding:
                                    code_text = coding["display"]
                                    break

                        value = component["valueQuantity"].get("value", "")
                        unit = component["valueQuantity"].get("unit", "")
                        components.append(f"{code_text}: {value} {unit}")

                obs["value"] = " | ".join(components) if components else "Multiple components"

                # Add individual component values as separate columns
                for component in resource["component"]:
                    if "code" in component and "valueQuantity" in component:
                        code_text = component["code"].get("text", "")
                        if not code_text and "coding" in component["code"]:
                            for coding in component["code"]["coding"]:
                                if "display" in coding:
                                    code_text = coding["display"]
                                    break

                        if code_text:
                            # Create a safe column name
                            column_name = code_text.replace(" ", "_").lower()
                            value = component["valueQuantity"].get("value", "")
                            obs[column_name] = value
            else:
                obs["value"] = ""

            data.append(obs)

        return pd.DataFrame(data)

    # For other resource types, return a generic DataFrame
    else:
        data = []
        for entry in entries:
            resource = entry["resource"]
            # Extract common fields
            item = {
                "id": resource.get("id", ""),
                "resourceType": resource.get("resourceType", ""),
            }
            data.append(item)

        return pd.DataFrame(data)

def get_resource_counts(server_url):
    """
    Get the count of resources by type.

    Args:
        server_url (str): URL of the FHIR server

    Returns:
        dict: Dictionary with resource types as keys and counts as values
    """
    resource_types = [
        "Patient", "Observation", "Condition", "Encounter",
        "Procedure", "MedicationRequest", "AllergyIntolerance",
        "DiagnosticReport", "Immunization", "Device"
    ]

    counts = {}

    for resource_type in resource_types:
        try:
            response = requests.get(f"{server_url}/{resource_type}?_summary=count", timeout=10)
            if response.status_code == 200:
                data = response.json()
                counts[resource_type] = data.get("total", 0)
            else:
                counts[resource_type] = 0
        except Exception:
            counts[resource_type] = 0

    return counts

def create_resource_count_chart(counts):
    """
    Create a bar chart with resource counts.

    Args:
        counts (dict): Dictionary with resource types as keys and counts as values

    Returns:
        plotly.graph_objects.Figure: Plotly figure object
    """
    import plotly.express as px
    import pandas as pd

    # Filter only types with count > 0
    filtered_counts = {k: v for k, v in counts.items() if v > 0}

    if not filtered_counts:
        return None

    df = pd.DataFrame({
        "Resource Type": list(filtered_counts.keys()),
        "Count": list(filtered_counts.values())
    })

    fig = px.bar(
        df,
        x="Resource Type",
        y="Count",
        title="Resources by Type",
        color="Resource Type"
    )

    return fig

def extract_detailed_report(output_text):
    """
    Extract detailed report information from the load_all_bundles.py output.

    Args:
        output_text (str): The output text from the load_all_bundles.py script

    Returns:
        dict: Structured report data
    """
    report = {
        "resource_breakdown": [],
        "overall_stats": {},
        "performance_metrics": {},
        "system_resources": {},
        "top_resources": [],
        "failed_bundles": []
    }

    # First, try to extract basic information regardless of report format
    try:
        # Extract basic statistics from anywhere in the output
        for line in output_text.split('\n'):
            # Look for resource counts
            if "Total Resources Processed:" in line:
                try:
                    value = line.split(":", 1)[1].strip()
                    report["overall_stats"]["Total Resources Processed"] = value
                except:
                    pass

            # Look for processing time
            elif "Total Processing Time:" in line:
                try:
                    value = line.split(":", 1)[1].strip()
                    report["overall_stats"]["Total Processing Time"] = value
                except:
                    pass

            # Look for resources per second
            elif "Resources Per Second:" in line:
                try:
                    value = line.split(":", 1)[1].strip()
                    report["performance_metrics"]["Resources Per Second"] = value
                except:
                    pass

            # Look for results summary
            elif "Results:" in line and "successful" in line and "failed" in line:
                try:
                    parts = line.split("Results:")[1].strip().split(",")
                    successful = parts[0].strip()
                    failed = parts[1].strip()
                    total = parts[2].strip() if len(parts) > 2 else "Unknown"

                    report["overall_stats"]["Bundle Results"] = f"{successful}, {failed}, {total}"

                    # Try to extract just the numbers
                    successful_num = successful.split(" ")[0]
                    failed_num = failed.split(" ")[0]
                    total_num = total.split(" ")[0] if total != "Unknown" else "Unknown"

                    report["overall_stats"]["Successful Bundles"] = successful_num
                    report["overall_stats"]["Failed Bundles"] = failed_num
                    report["overall_stats"]["Total Bundles"] = total_num
                except Exception as e:
                    logger.debug(f"Error parsing results line: {str(e)}")

            # Look for CPU usage
            elif "CPU Usage:" in line or "Peak CPU Usage:" in line:
                try:
                    key = "Peak CPU Usage" if "Peak" in line else "CPU Usage"
                    value = line.split(":", 1)[1].strip()
                    report["system_resources"][key] = value
                except:
                    pass

            # Look for memory usage
            elif "Memory Usage:" in line or "Peak Memory Usage:" in line:
                try:
                    key = "Peak Memory Usage" if "Peak" in line else "Memory Usage"
                    value = line.split(":", 1)[1].strip()
                    report["system_resources"][key] = value
                except:
                    pass

            # Look for disk I/O
            elif "Disk I/O:" in line:
                try:
                    value = line.split(":", 1)[1].strip()
                    report["system_resources"]["Disk I/O"] = value
                except:
                    pass

            # Look for network I/O
            elif "Network I/O:" in line:
                try:
                    value = line.split(":", 1)[1].strip()
                    report["system_resources"]["Network I/O"] = value
                except:
                    pass
    except Exception as e:
        logger.error(f"Error extracting basic statistics: {str(e)}")

    # Extract failed bundles information from anywhere in the output
    try:
        failed_bundles_section = False
        for line in output_text.split('\n'):
            if "FAILED BUNDLES:" in line:
                failed_bundles_section = True
                continue
            elif "================" in line and failed_bundles_section:
                failed_bundles_section = False
                continue

            if failed_bundles_section and "|" in line:
                # Skip header rows
                if "Resource Type" in line or "----" in line:
                    continue

                parts = [p.strip() for p in line.split("|") if p.strip()]
                if len(parts) >= 3:
                    try:
                        # Handle different table formats
                        if len(parts) == 3:
                            # Simpler format with just type, file, error
                            resource_type = parts[0]
                            file_name = parts[1]
                            error = parts[2]
                            resources = "Unknown"
                        else:
                            # Full format with type, file, resources, error
                            resource_type = parts[0]
                            file_name = parts[1]
                            resources = parts[2]
                            error = parts[3] if len(parts) > 3 else "Unknown error"

                        report["failed_bundles"].append({
                            "resource_type": resource_type,
                            "file_name": file_name,
                            "resources": resources,
                            "error": error
                        })
                    except Exception as e:
                        logger.debug(f"Error parsing failed bundle line: {str(e)}")
    except Exception as e:
        logger.error(f"Error extracting failed bundles: {str(e)}")

    # Now try to extract the full structured report if it exists
    if "FHIR BUNDLE LOADING SUMMARY REPORT" in output_text:
        logger.debug("Found structured summary report section")

        # Define section markers
        section_markers = {
            "resource_breakdown": "RESOURCE TYPE BREAKDOWN:",
            "overall_stats": "OVERALL STATISTICS:",
            "performance_metrics": "PERFORMANCE METRICS:",
            "system_resources": "SYSTEM RESOURCE USAGE:",
            "top_resources": "TOP RESOURCE TYPES BY VOLUME:",
            "failed_bundles": "FAILED BUNDLES:"
        }

        # Track current section
        current_section = None
        in_report_section = False

        # Process the output line by line
        lines = output_text.split('\n')
        for line_idx, line in enumerate(lines):
            # Check if we're in the report section
            if "FHIR BUNDLE LOADING SUMMARY REPORT" in line:
                in_report_section = True
                continue

            if not in_report_section:
                continue

            # Check for section headers
            for section, marker in section_markers.items():
                if marker in line:
                    current_section = section
                    logger.debug(f"Found section: {section}")
                    break

            # Skip if no section is active or line is a separator
            if not current_section or "====" in line or "----" in line:
                continue

            # Process based on current section
            if current_section == "resource_breakdown" and "|" in line:
                # Skip the header row
                if "Resource Type" in line and "Bundles" in line:
                    continue

                # Parse resource breakdown table
                parts = [p.strip() for p in line.split("|") if p.strip()]
                if len(parts) >= 4:
                    try:
                        resource_type = parts[0]
                        bundles = parts[1]
                        resources = parts[2]
                        time = parts[3]
                        success_rate = parts[4] if len(parts) > 4 else "N/A"

                        # Only add if we don't already have this resource type
                        if not any(item["resource_type"] == resource_type for item in report["resource_breakdown"]):
                            report["resource_breakdown"].append({
                                "resource_type": resource_type,
                                "bundles": bundles,
                                "resources": resources,
                                "time": time,
                                "success_rate": success_rate
                            })
                    except Exception as e:
                        logger.debug(f"Error parsing resource breakdown: {str(e)}")

            elif current_section == "overall_stats" and ":" in line:
                # Parse overall statistics
                try:
                    key, value = [p.strip() for p in line.split(":", 1)]
                    report["overall_stats"][key] = value
                except Exception as e:
                    logger.debug(f"Error parsing overall stats: {str(e)}")

            elif current_section == "performance_metrics" and ":" in line:
                # Parse performance metrics
                try:
                    key, value = [p.strip() for p in line.split(":", 1)]
                    report["performance_metrics"][key] = value
                except Exception as e:
                    logger.debug(f"Error parsing performance metrics: {str(e)}")

            elif current_section == "system_resources" and ":" in line:
                # Parse system resource usage
                try:
                    key, value = [p.strip() for p in line.split(":", 1)]
                    report["system_resources"][key] = value
                except Exception as e:
                    logger.debug(f"Error parsing system resources: {str(e)}")

            elif current_section == "top_resources" and "." in line:
                # Parse top resources by volume
                try:
                    parts = line.split(".", 1)
                    if len(parts) == 2:
                        rank = parts[0].strip()
                        rest = parts[1].strip()

                        if ":" in rest:
                            resource_type, count_info = rest.split(":", 1)
                            report["top_resources"].append({
                                "rank": rank,
                                "resource_type": resource_type.strip(),
                                "count_info": count_info.strip()
                            })
                except Exception as e:
                    logger.debug(f"Error parsing top resources: {str(e)}")

            elif current_section == "failed_bundles" and "|" in line:
                # Skip the header row
                if "Resource Type" in line and "File" in line:
                    continue

                # Parse failed bundles table - we already did this in the first pass
                # This is just to ensure we don't miss anything
                parts = [p.strip() for p in line.split("|") if p.strip()]
                if len(parts) >= 3:
                    try:
                        resource_type = parts[0]
                        file_name = parts[1]
                        resources = parts[2] if len(parts) > 2 else "Unknown"
                        error = parts[3] if len(parts) > 3 else "Unknown error"

                        # Check if we already have this failed bundle
                        if not any(item["file_name"] == file_name and item["resource_type"] == resource_type
                                for item in report["failed_bundles"]):
                            report["failed_bundles"].append({
                                "resource_type": resource_type,
                                "file_name": file_name,
                                "resources": resources,
                                "error": error
                            })
                    except Exception as e:
                        logger.debug(f"Error parsing failed bundles: {str(e)}")

    # Try to extract resource breakdown from non-standard output
    if not report["resource_breakdown"]:
        try:
            # Look for lines that might contain resource type information
            resource_pattern = re.compile(r'Processing (\w+) resources')
            count_pattern = re.compile(r'Processed (\d+) resources')

            current_type = None
            current_count = None

            for line in output_text.split('\n'):
                type_match = resource_pattern.search(line)
                if type_match:
                    current_type = type_match.group(1)

                count_match = count_pattern.search(line)
                if count_match and current_type:
                    current_count = count_match.group(1)

                    # Add to resource breakdown if we have both type and count
                    if current_type and current_count:
                        report["resource_breakdown"].append({
                            "resource_type": current_type,
                            "bundles": "1",
                            "resources": current_count,
                            "time": "N/A",
                            "success_rate": "N/A"
                        })

                        current_type = None
                        current_count = None
        except Exception as e:
            logger.error(f"Error extracting resource breakdown from non-standard output: {str(e)}")

    # Log the extracted report data
    logger.debug(f"Extracted report data: {report}")

    return report

def export_query_results(data, format="csv"):
    """
    Export query results to a file.

    Args:
        data (dict): FHIR bundle or other data
        format (str): Export format ('csv' or 'json')

    Returns:
        tuple: (success, filename) where success is a boolean and filename is the path to the exported file
    """
    import json
    from datetime import datetime

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    if format == "csv":
        # Convert to DataFrame if it's a FHIR bundle
        if isinstance(data, dict) and data.get("resourceType") == "Bundle" and "entry" in data:
            # Import pandas here to avoid unused import warning
            import pandas as pd

            df = format_fhir_bundle_as_dataframe(data)
            if df.empty:
                return False, "Could not extract tabular data from the bundle"

            filename = f"fhir_query_results_{timestamp}.csv"
            df.to_csv(filename, index=False)
            return True, filename
        else:
            return False, "The data is not a valid FHIR bundle for CSV export"

    elif format == "json":
        filename = f"fhir_query_results_{timestamp}.json"
        with open(filename, "w") as f:
            json.dump(data, f, indent=2)
        return True, filename

    else:
        return False, f"Unsupported format: {format}"
