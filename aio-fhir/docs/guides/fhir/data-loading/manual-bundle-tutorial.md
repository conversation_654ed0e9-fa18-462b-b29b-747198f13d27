# Manual FHIR Bundle Creation and Loading Tutorial

This tutorial guides you through the process of manually creating, verifying, and loading FHIR transaction bundles from NDJSON files.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Overview](#overview)
- [Server Configuration](#server-configuration)
- [Step 1: Analyze NDJSON Files](#step-1-analyze-ndjson-files)
- [Step 2: Create Transaction Bundles](#step-2-create-transaction-bundles)
- [Step 3: Verify Bundle Structure](#step-3-verify-bundle-structure)
- [Step 4: Load Bundles to FHIR Server](#step-4-load-bundles-to-fhir-server)
- [Step 5: Verify Loaded Resources](#step-5-verify-loaded-resources)
- [Automating the Process](#automating-the-process)
- [Troubleshooting](#troubleshooting)

## Prerequisites

- Python 3.6+
- HAPI FHIR Server running
- FHIR data in NDJSON format
- Required Python packages: `requests`, `json`

## Overview

The process involves these key steps:

1. Configuring the server for data loading (critical for resources with references)
2. Analyzing NDJSON files to understand resource types and references
3. Converting NDJSON files to FHIR transaction bundles
4. Verifying the structure of the created bundles
5. Loading bundles to the FHIR server
6. Verifying that resources were loaded correctly

## Server Configuration

**Important**: Server configuration dramatically affects which resources can be loaded.

### Default Configuration

With default server settings (validation and referential integrity enabled):
- Only resources with satisfied references will load
- Resources with references to non-existent resources will be rejected
- Only Patient, AllergyIntolerance, and similar resources with minimal dependencies will load

### Import Mode Configuration

For loading all resources regardless of references:

1. Edit `servers/fhir-server/docker-compose-postgres.yml`
2. Uncomment these lines:
   ```yaml
   - hapi.fhir.validation.enabled=false
   - hapi.fhir.validation.request_validator.mode=NONE
   - hapi.fhir.auto_create_placeholder_reference_targets=true
   - hapi.fhir.enforce_referential_integrity_on_write=false
   - hapi.fhir.enforce_referential_integrity_on_delete=false
   ```
3. Restart the server: `./manage-fhir-server.sh restart postgres`

For a more detailed guide on server configuration, see the [Direct Transaction Tutorial](./direct-transaction-tutorial.md).

## Step 1: Analyze NDJSON Files

First, let's examine the NDJSON files to understand their structure and references.

```bash
# View the first few lines of an NDJSON file
head -n 1 data/sample_fhir/bulk-export/Patient.000.ndjson | python -m json.tool

# Count resources in an NDJSON file
wc -l data/sample_fhir/bulk-export/Patient.000.ndjson

# List all NDJSON files in the directory
ls -la data/sample_fhir/bulk-export/*.ndjson
```

You can also use our reference analyzer script to get a comprehensive analysis:

```bash
python scripts/transaction_bundles/reference_analyzer.py \
  --data-dir data/sample_fhir/bulk-export \
  --output-file reference_analysis.json
```

## Step 2: Create Transaction Bundles

Now, let's create a transaction bundle from an NDJSON file. We'll start with `Patient` resources since they typically have no external dependencies.

```bash
# Create a transaction bundle for Patient resources
python scripts/transaction_bundles/ndjson_to_bundle.py \
  --input-file data/sample_fhir/bulk-export/Patient.000.ndjson \
  --output-file data/generated_bundles/Patient_bundle.json
```

Note: The script will automatically create the output directory if it doesn't exist.

You can also do this manually for a single resource:

```python
# Example Python code to create a bundle manually
import json

# Read a single resource from NDJSON
with open('data/sample_fhir/bulk-export/Patient.000.ndjson', 'r') as f:
    patient_resource = json.loads(f.readline())

# Create a transaction bundle
bundle = {
    "resourceType": "Bundle",
    "type": "transaction",
    "entry": [
        {
            "resource": patient_resource,
            "request": {
                "method": "PUT",
                "url": f"Patient/{patient_resource['id']}"
            }
        }
    ]
}

# Save the bundle
with open('data/generated_bundles/single_patient_bundle.json', 'w') as f:
    json.dump(bundle, f, indent=2)
```

## Step 3: Verify Bundle Structure

Before loading, verify that the bundle has the correct structure:

```bash
# Verify the bundle structure
python scripts/transaction_bundles/verify_bundle.py \
  --bundle-file data/generated_bundles/Patient_bundle.json
```

You can also manually inspect the bundle:

```bash
# View the bundle structure
cat data/generated_bundles/Patient_bundle.json | python -m json.tool | head -n 30
```

Key things to check:
- The bundle has `"resourceType": "Bundle"` and `"type": "transaction"`
- Each entry has a `resource` and a `request` section
- The `request` section has `method` (PUT) and `url` (ResourceType/id)

## Step 4: Load Bundles to FHIR Server

Now, let's load the bundle to the FHIR server:

```bash
# Load the bundle to the FHIR server
python scripts/transaction_bundles/send_bundle.py \
  --bundle-file data/generated_bundles/Patient_bundle.json \
  --server-url http://localhost:8080/fhir
```

You can also do this manually with curl:

```bash
# Load the bundle using curl
curl -X POST \
  -H "Content-Type: application/json" \
  -d @data/generated_bundles/Patient_bundle.json \
  http://localhost:8080/fhir
```

## Step 5: Verify Loaded Resources

Finally, verify that the resources were loaded correctly:

```bash
# Verify loaded resources
python scripts/transaction_bundles/verify_loaded_resources.py \
  --ndjson-file data/sample_fhir/bulk-export/Patient.000.ndjson \
  --server-url http://localhost:8080/fhir
```

You can also manually check a specific resource:

```bash
# Get a specific resource from the server
# First, extract an ID from the NDJSON file
PATIENT_ID=$(head -n 1 data/sample_fhir/bulk-export/Patient.000.ndjson | python -c "import sys, json; print(json.loads(sys.stdin.read())['id'])")

# Then fetch the resource
curl -X GET \
  -H "Accept: application/json" \
  http://localhost:8080/fhir/Patient/$PATIENT_ID | python -m json.tool
```

## Automating the Process

To automate this process for multiple resource types, you can use our selective loader script:

```bash
# Reset the FHIR server database (optional)
./manage-fhir-server.sh stop postgres
docker volume rm fhir-server_postgres-data
./manage-fhir-server.sh start postgres

# Run the selective loader
python scripts/transaction_bundles/selective_loader.py \
  --data-dir data/sample_fhir/bulk-export \
  --server-url http://localhost:8080/fhir \
  --verify
```

## Troubleshooting

### Common Issues

1. **Bundle Format Errors**:
   - Ensure the bundle has the correct structure
   - Check that each entry has a valid resource and request section

2. **Server Connection Issues**:
   - Verify the FHIR server is running
   - Check the server URL is correct

3. **Resource Not Found After Loading**:
   - Check for errors in the server response
   - Verify the resource ID is correct

4. **Reference Errors**:
   - With default configuration: Resources with unresolved references will be rejected
   - With import mode: Resources should load even with unresolved references
   - If resources are still rejected, check server configuration and restart the server

5. **Server Configuration Issues**:
   - If resources with references are rejected, verify server configuration is in import mode
   - After changing configuration, restart the server completely
   - Check server logs for configuration-related messages

### Debugging Tips

- Use the `--verbose` flag with our scripts for more detailed output
- Check the FHIR server logs for error messages
- Use the FHIR server's web interface to browse loaded resources
- If only some resources load, check if server is using default configuration
