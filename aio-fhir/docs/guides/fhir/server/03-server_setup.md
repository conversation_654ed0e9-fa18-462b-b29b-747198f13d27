# FHIR Server Installation

This section provides step-by-step instructions for setting up the HAPI FHIR server using Docker.

## Installation Flow

```mermaid
flowchart TD
    A["Step 1: Create Project Directory<br><small>mkdir -p servers/fhir-server</small>"] --> B["Step 2: Create .env File<br><small>Configure ports, versions, PostgreSQL</small>"]
    B --> C["Step 3: Create docker-compose-postgres.yml<br><small>HAPI FHIR with PostgreSQL database</small>"]
    C --> D["Step 4: Start FHIR Server<br><small>./manage-fhir-server.sh start</small>"]
    D --> E["Step 5: Verify Server<br><small>http://localhost:8080/fhir/metadata</small>"]
    E --> F["Step 6: Server Management<br><small>manage-fhir-server.sh</small>"]

    F -.-> G["Start<br><small>./manage-fhir-server.sh start</small>"]
    F -.-> H["Status<br><small>./manage-fhir-server.sh status</small>"]
    F -.-> I["Logs<br><small>./manage-fhir-server.sh logs</small>"]
    F -.-> J["Stop<br><small>./manage-fhir-server.sh stop</small>"]
    F -.-> K["Restart<br><small>./manage-fhir-server.sh restart</small>"]

    E --> L["Step 7: Load Sample Data<br><small>Using Transaction Bundles</small>"]
    L -.-> L1["Direct Transaction Bundles<br><small>For bulk loading</small>"]
    L -.-> L2["Selective Loading<br><small>For reference integrity</small>"]

    subgraph "Core Installation"
        A
        B
        C
        D
        E
    end

    subgraph "Server Management"
        F
        G
        H
        I
        J
        K
    end

    subgraph "Data Loading"
        L
        L1
        L2
    end

    style A fill:#f9f9f9,stroke:#333,stroke-width:1px,color:#000
    style B fill:#f9f9f9,stroke:#333,stroke-width:1px,color:#000
    style C fill:#f9f9f9,stroke:#333,stroke-width:1px,color:#000
    style D fill:#f9f9f9,stroke:#333,stroke-width:1px,color:#000
    style E fill:#f9f9f9,stroke:#333,stroke-width:1px,color:#000
    style F fill:#e6f3ff,stroke:#333,stroke-width:1px,color:#000
    style G fill:#e6f3ff,stroke:#333,stroke-width:1px,stroke-dasharray: 5 5,color:#000
    style H fill:#e6f3ff,stroke:#333,stroke-width:1px,stroke-dasharray: 5 5,color:#000
    style I fill:#e6f3ff,stroke:#333,stroke-width:1px,stroke-dasharray: 5 5,color:#000
    style J fill:#e6f3ff,stroke:#333,stroke-width:1px,stroke-dasharray: 5 5,color:#000
    style K fill:#e6f3ff,stroke:#333,stroke-width:1px,stroke-dasharray: 5 5,color:#000
    style L fill:#f0e68c,stroke:#333,stroke-width:1px,color:#000
    style L1 fill:#f0e68c,stroke:#333,stroke-width:1px,stroke-dasharray: 5 5,color:#000
    style L2 fill:#f0e68c,stroke:#333,stroke-width:1px,stroke-dasharray: 5 5,color:#000
```

The diagram above illustrates the installation process:
1. **Core Installation Steps** (solid lines): Essential steps to get the FHIR server running
2. **Management Operations** (dashed lines): Optional but recommended operations using the management scripts
3. **Data Loading Operations** (yellow nodes): Steps for loading sample data into the FHIR server

Our installation process uses PostgreSQL as the database, which is the recommended configuration for production use.

## Stage 4: Set Up HAPI FHIR Server

**Objective**: Deploy a local HAPI FHIR server using Docker.

### Step 1: Create Project Directory Structure
```bash
# Create a directory for the FHIR server project
mkdir -p servers/fhir-server
cd servers/fhir-server
```

### Step 2: Create Environment Configuration File

Create a `.env` file for environment variables:
```bash
cat > .env << EOL
# HAPI FHIR Server Configuration

# Server configuration
FHIR_PORT=8080
FHIR_VERSION=R4

# Authentication (uncomment to enable)
#FHIR_USERNAME=fhir_user
#FHIR_PASSWORD=secure_fhir_password

# PostgreSQL configuration (primary database for production)
POSTGRES_DB=hapi
POSTGRES_USER=admin
POSTGRES_PASSWORD=secure_password
POSTGRES_PORT=5432
EOL
```

### Step 3: Create Docker Compose File for HAPI FHIR with PostgreSQL

Docker Compose allows defining and running multi-container Docker applications using a YAML file. We use Docker Compose to manage both the FHIR server and PostgreSQL database containers.

#### Structure of docker-compose-postgres.yml

The `docker-compose-postgres.yml` file defines:
- **Services**: The containers that make up the application
- **Volumes**: Persistent storage spaces
- **Networks**: Communication configuration between containers
- **Environment variables**: Configuration that can change between environments

Let's create a `docker-compose-postgres.yml` file that uses PostgreSQL:
```bash
cat > docker-compose-postgres.yml << EOL
services:
  # HAPI FHIR Server with PostgreSQL
  # Based on the official HAPI FHIR JPA Server Starter example:
  # https://github.com/hapifhir/hapi-fhir-jpaserver-starter
  hapi-fhir-server:
    image: hapiproject/hapi:latest                  # Official HAPI FHIR server image
    ports:
      - "${FHIR_PORT:-8080}:8080"                   # Exposes container port 8080 to the host port defined by FHIR_PORT or defaults to 8080
    environment:
      # Basic FHIR server settings
      - hapi.fhir.default_encoding=json             # Sets default encoding to JSON
      - hapi.fhir.fhir_version=${FHIR_VERSION:-R4}  # Sets the FHIR version to use

      # Bulk operations configuration
      - hapi.fhir.bulk_export_enabled=true          # Enables FHIR bulk export feature
      - hapi.fhir.bulk_import_enabled=true          # Enables FHIR bulk import feature
      - hapi.fhir.client_id_strategy=ANY            # Allows any client ID for bulk operations

      # FHIR Server Data Validation Configuration
      # By default, the server enforces data integrity and validation
      # For bulk data loading, uncomment the following lines to disable validation and referential integrity checks
      # After completing data import, comment these lines again to restore default security and integrity settings
      # -----------------------------------------------------------------------------------------------------
      # - hapi.fhir.validation.enabled=false                        # Disables validation
      # - hapi.fhir.validation.request_validator.mode=NONE          # Disables request validation
      # - hapi.fhir.auto_create_placeholder_reference_targets=true  # Creates placeholder resources for missing references
      # - hapi.fhir.enforce_referential_integrity_on_write=false    # Disables referential integrity checks on write
      # - hapi.fhir.enforce_referential_integrity_on_delete=false   # Disables referential integrity checks on delete

      # PostgreSQL configuration
      # Using the recommended dialect from the HAPI FHIR documentation:
      # https://hapifhir.io/hapi-fhir/docs/server_jpa/database_support.html
      - spring.datasource.url=*******************************:${POSTGRES_PORT}/${POSTGRES_DB}  # JDBC connection URL to PostgreSQL database
      - spring.datasource.username=${POSTGRES_USER}                                            # Database username
      - spring.datasource.password=${POSTGRES_PASSWORD}                                        # Database password
      - spring.datasource.driverClassName=org.postgresql.Driver                                 # JDBC driver for PostgreSQL
      - spring.jpa.properties.hibernate.dialect=ca.uhn.fhir.jpa.model.dialect.HapiFhirPostgresDialect  # Optimized dialect for HAPI FHIR and PostgreSQL
      - spring.jpa.properties.hibernate.search.enabled=false                                   # Disables advanced search (not required for most PostgreSQL use cases)
    volumes:
      - hapi-data:/data/hapi                        # Persistent volume for FHIR server data
    restart: unless-stopped                         # Restart container unless stopped manually
    depends_on:
      - fhir-postgres                               # Waits for the database to be ready before starting
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/fhir/metadata"]  # Checks if the metadata endpoint is available
      interval: 30s                                 # Interval between health checks
      timeout: 10s                                  # Maximum wait time for a response
      retries: 5                                    # Number of retries before marking as unhealthy

  # PostgreSQL database service
  # PostgreSQL 14 is used for compatibility and stability with HAPI FHIR
  # Note: PostgreSQL 15 and 16 have known compatibility issues with HAPI FHIR
  # See: https://groups.google.com/g/hapi-fhir/c/gQLLcRtlwpI
  fhir-postgres:
    image: postgres:14                              # Official PostgreSQL version 14 image
    environment:
      POSTGRES_DB: ${POSTGRES_DB}                  # Name of the database to create
      POSTGRES_USER: ${POSTGRES_USER}              # Database admin user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}      # Admin user password
    volumes:
      - postgres-data:/var/lib/postgresql/data    # Persistent volume for PostgreSQL data
    restart: unless-stopped                       # Restart container unless stopped manually
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]  # Checks if the database is ready
      interval: 10s                               # Interval between health checks
      timeout: 5s                                 # Maximum wait time for a response
      retries: 5                                  # Number of retries before marking as unhealthy

volumes:
  hapi-data:                                      # Persistent volume for FHIR server data
  postgres-data:                                  # Persistent volume for PostgreSQL data
EOL
```

#### Configuration Explanation

- **Docker Image**: `hapiproject/hapi:latest` is the official image from the HAPI FHIR project, providing a complete implementation of the FHIR standard.

- **Port Mapping**: `"${FHIR_PORT:-8080}:8080"` maps the container's internal port 8080 to the port specified in the FHIR_PORT variable (or 8080 if not defined).

- **Environment Variables**:
  - `hapi.fhir.default_encoding=json`: Sets JSON as the default format for responses
  - `hapi.fhir.bulk_export_enabled=true`: Enables bulk data export functionality
  - `hapi.fhir.fhir_version=${FHIR_VERSION:-R4}`: Configures which FHIR version to use

- **Database Configuration**:

  ## PostgreSQL Database

  [PostgreSQL](https://www.postgresql.org/) is our recommended database for production use. It provides:

  - Robust data persistence
  - Excellent performance for large datasets
  - Advanced database features
  - Strong concurrency support
  - Mature ecosystem with extensive tooling

  ## PostgreSQL Configuration (Primary)

  Our primary database configuration uses PostgreSQL, which is recommended for production environments according to the [official HAPI FHIR documentation](https://hapifhir.io/hapi-fhir/docs/server_jpa/database_support.html).

  ### PostgreSQL Configuration Details

  The PostgreSQL configuration offers significant advantages:

  1. **Robust persistence**: Enterprise-grade reliability with proper transaction support
  2. **Scalability**: Handles large datasets and high concurrency efficiently
  3. **Advanced features**: Rich set of features for complex queries and operations
  4. **Mature ecosystem**: Extensive tooling and community support
  5. **Production-ready**: Designed for stable, long-term operation

  ### Using PostgreSQL with HAPI FHIR

  Our implementation uses PostgreSQL as the primary database configuration. To use it:

  1. **Create a PostgreSQL Docker Compose file**:

     We've created a separate `docker-compose-postgres.yml` file in the `servers/fhir-server` directory with the following configuration:

     ```yaml
     version: '3'

     services:
       # HAPI FHIR Server with PostgreSQL
       # Based on the official HAPI FHIR JPA Server Starter example:
       # https://github.com/hapifhir/hapi-fhir-jpaserver-starter
       hapi-fhir-server:
         image: hapiproject/hapi:latest                  # Official HAPI FHIR server image
         ports:
           - "${FHIR_PORT:-8080}:8080"                   # Exposes container port 8080 to the host port defined by FHIR_PORT or defaults to 8080
         environment:
           - hapi.fhir.default_encoding=json             # Sets default encoding to JSON
           - hapi.fhir.bulk_export_enabled=true          # Enables FHIR bulk export feature
           - hapi.fhir.fhir_version=${FHIR_VERSION:-R4}  # Sets the FHIR version to use
           # PostgreSQL configuration
           # Using the recommended dialect from the HAPI FHIR documentation:
           # https://hapifhir.io/hapi-fhir/docs/server_jpa/database_support.html
           - spring.datasource.url=*******************************:${POSTGRES_PORT}/${POSTGRES_DB}  # JDBC connection URL to PostgreSQL database
           - spring.datasource.username=${POSTGRES_USER}                                            # Database username
           - spring.datasource.password=${POSTGRES_PASSWORD}                                        # Database password
           - spring.datasource.driverClassName=org.postgresql.Driver                                 # JDBC driver for PostgreSQL
           - spring.jpa.properties.hibernate.dialect=ca.uhn.fhir.jpa.model.dialect.HapiFhirPostgresDialect  # Optimized dialect for HAPI FHIR and PostgreSQL
           - spring.jpa.properties.hibernate.search.enabled=false                                   # Disables advanced search (not required for most PostgreSQL use cases)
         volumes:
           - hapi-data:/data/hapi                        # Persistent volume for FHIR server data
         restart: unless-stopped                         # Restart container unless stopped manually
         depends_on:
           - fhir-postgres                               # Waits for the database to be ready before starting
         healthcheck:
           test: ["CMD", "curl", "-f", "http://localhost:8080/fhir/metadata"]  # Checks if the metadata endpoint is available
           interval: 30s                                 # Interval between health checks
           timeout: 10s                                  # Maximum wait time for a response
           retries: 5                                    # Number of retries before marking as unhealthy

       # PostgreSQL database service
       # PostgreSQL 14 is used for compatibility and stability with HAPI FHIR
       # Note: PostgreSQL 15 and 16 have known compatibility issues with HAPI FHIR
       # See: https://groups.google.com/g/hapi-fhir/c/gQLLcRtlwpI
       fhir-postgres:
         image: postgres:14                              # Official PostgreSQL version 14 image
         environment:
           POSTGRES_DB: ${POSTGRES_DB}                  # Name of the database to create
           POSTGRES_USER: ${POSTGRES_USER}              # Database admin user
           POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}      # Admin user password
         volumes:
           - postgres-data:/var/lib/postgresql/data    # Persistent volume for PostgreSQL data
         restart: unless-stopped                       # Restart container unless stopped manually
         healthcheck:
           test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]  # Checks if the database is ready
           interval: 10s                               # Interval between health checks
           timeout: 5s                                 # Maximum wait time for a response
           retries: 5                                  # Number of retries before marking as unhealthy

     volumes:
       hapi-data:                                      # Persistent volume for FHIR server data
       postgres-data:                                  # Persistent volume for PostgreSQL data
     ```

  2. **Use the Server Management Script**:

     We've created a new management script `manage-fhir-server.sh` that supports both H2 and PostgreSQL configurations:

     ```bash
     # Start with PostgreSQL (recommended for production)
     ./manage-fhir-server.sh start postgres

     # Start with H2 (for development/testing)
     ./manage-fhir-server.sh start h2

     # Check server status
     ./manage-fhir-server.sh status

     # View server logs
     ./manage-fhir-server.sh logs

     # Stop the server
     ./manage-fhir-server.sh stop
     ```

  3. **Load Sample Data**:

     Use the `load-sample-data.sh` script to load sample data into either database configuration:

     ```bash
     # Load sample data into PostgreSQL server
     ./load-sample-data.sh postgres

     # Load sample data into H2 server
     ./load-sample-data.sh h2
     ```

  For more detailed information about the PostgreSQL migration, see the [PostgreSQL Migration Guide](postgresql-migration.md).

  ## Loading Data into the FHIR Server

  Once your FHIR server is running, you'll likely want to load data into it for testing and development. For detailed information on different methods to interact with the FHIR server and load data, see the [Data Interaction and Loading](09-data_interaction.md) guide.

- **Healthcheck**: Configures how Docker verifies if the server is functioning correctly:
  - `test`: The command to check health (queries the metadata endpoint)
  - `interval`: Frequency of checks (every 30 seconds)
  - `timeout`: Maximum time to consider a check failed (10 seconds)
  - `retries`: Number of attempts before marking the container as unhealthy (5 attempts)

- **Volumes**: `hapi-data` provides persistent storage for FHIR server data, ensuring that certain information survives container restarts.

### Step 4: Start the HAPI FHIR Server

Once the `docker-compose-postgres.yml` file is created, we can start the FHIR server using the `manage-fhir-server.sh` script:

```bash
# Make the script executable
chmod +x manage-fhir-server.sh

# Start the FHIR server with PostgreSQL
./manage-fhir-server.sh start

# Check if containers are running
./manage-fhir-server.sh status

# View logs to monitor startup
./manage-fhir-server.sh logs
```

#### What does the script do?

1. **Reads configuration**: Processes the `docker-compose-postgres.yml` file
2. **Downloads images**: If not available locally
3. **Creates networks**: For container communication
4. **Creates volumes**: For data persistence
5. **Creates and starts containers**: According to the defined configuration
6. **Detached mode**: Runs containers in the background

#### Why use `docker-compose` instead of `docker run`?

- **Simplicity**: A single file defines all configuration
- **Reproducibility**: Ensures everyone uses exactly the same configuration
- **Dependency management**: Automatically handles container startup order
- **Scalability**: Makes it easy to add new services (like PostgreSQL) in the future
- **Maintainability**: Easier to maintain and version than scripts with multiple `docker run` commands

### Step 5: Verify HAPI FHIR Server is Running

Open a web browser and navigate to:
```
http://localhost:8080/
```

You should see the HAPI FHIR server welcome page. To check the FHIR capabilities, navigate to:
```
http://localhost:8080/fhir/metadata
```

This will display the FHIR CapabilityStatement in JSON format, showing which FHIR resources and operations are supported.

#### Understanding FHIR Endpoints and CapabilityStatement

- **FHIR Endpoint**: In FHIR terminology, an endpoint is a URL path that provides access to a specific FHIR resource or operation. The base FHIR endpoint in our setup is `http://localhost:8080/fhir`.

- **CapabilityStatement**: This is a special FHIR resource that describes what a FHIR server can do. It includes:
  - Supported FHIR version
  - Supported resources (Patient, Observation, etc.)
  - Supported operations (search, create, update, etc.)
  - Supported search parameters
  - Authentication requirements

- **Metadata Endpoint**: The `/metadata` endpoint (accessed via `http://localhost:8080/fhir/metadata`) returns the CapabilityStatement resource, which is essential for clients to understand what the server can do before attempting to interact with it.

Examining the CapabilityStatement is an important first step when working with any FHIR server, as it helps you understand what features are available.

## Using the Management Script

The `manage-fhir-server.sh` script makes it easier to manage the FHIR server with PostgreSQL. This script is already included in the repository.

#### Why Bash instead of Python?

While Python is the primary language for our project, we chose Bash for this management script for several reasons:

1. **Minimal dependencies**: The script requires no additional packages beyond what's already installed on most systems
2. **Direct shell integration**: Bash scripts can directly execute shell commands without subprocess calls
3. **Simplicity**: For this specific use case (wrapping Docker commands), Bash provides a more direct approach
4. **Universal availability**: Bash is available on virtually all Unix-like systems without additional installation

For more complex management tasks that require data processing or API interactions, Python would be the preferred choice. This script, however, simply wraps Docker Compose commands in a user-friendly interface.

Here's how to use the management script:

```bash
# Start the FHIR server with PostgreSQL
./manage-fhir-server.sh start

# Check the status of the FHIR server
./manage-fhir-server.sh status

# View the logs of the FHIR server
./manage-fhir-server.sh logs

# Stop the FHIR server
./manage-fhir-server.sh stop

# Restart the FHIR server
./manage-fhir-server.sh restart
```

The script handles all the Docker Compose commands for you, making it easier to manage the FHIR server.

## Creating a Test Script

To verify that the FHIR server is working correctly, it's useful to create an automated test script. We'll use Python for this purpose, as it's well-suited for HTTP requests and data processing.

### Why Python for Testing?

Python is ideal for testing FHIR servers because:

1. **Rich libraries**: Libraries like `requests` make HTTP interactions straightforward
2. **JSON handling**: Python has excellent built-in JSON support, perfect for FHIR's JSON format
3. **Readability**: Python code is easy to read and maintain
4. **Extensibility**: The script can be easily extended for more complex testing scenarios

### Creating the Test Script

```bash
cat > test_fhir_server.py << EOL
#!/usr/bin/env python3
"""
Script to test connectivity and functionality of the HAPI FHIR server.

This script performs basic validation of the FHIR server by:
1. Checking if the server is running and accessible
2. Creating a test patient resource
3. Retrieving the created patient resource

Usage:
    python test_fhir_server.py

Requirements:
    - requests library: pip install requests
    - python-dotenv library: pip install python-dotenv
"""
import os
import sys
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# FHIR server configuration
FHIR_PORT = os.getenv("FHIR_PORT", "8080")
FHIR_SERVER_URL = f"http://localhost:{FHIR_PORT}/fhir"
FHIR_USERNAME = os.getenv("FHIR_USERNAME", "")
FHIR_PASSWORD = os.getenv("FHIR_PASSWORD", "")

# Authentication configuration
auth = None
if FHIR_USERNAME and FHIR_PASSWORD:
    auth = (FHIR_USERNAME, FHIR_PASSWORD)

def check_server_status():
    """Check if the FHIR server is running.

    Returns:
        bool: True if server is running, False otherwise
    """
    try:
        response = requests.get(f"{FHIR_SERVER_URL}/metadata", auth=auth)
        if response.status_code == 200:
            print("✅ FHIR server is running.")
            return True
        else:
            print(f"❌ FHIR server responded with status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Could not connect to FHIR server: {e}")
        return False

def create_test_patient():
    """Create a test patient in the FHIR server.

    Returns:
        str: Patient ID if created successfully, None otherwise
    """
    patient_data = {
        "resourceType": "Patient",
        "active": True,
        "name": [
            {
                "use": "official",
                "family": "Test",
                "given": ["Patient"]
            }
        ],
        "gender": "male",
        "birthDate": "1970-01-01"
    }

    headers = {"Content-Type": "application/fhir+json"}

    try:
        response = requests.post(
            f"{FHIR_SERVER_URL}/Patient",
            json=patient_data,
            headers=headers,
            auth=auth
        )

        if response.status_code in [200, 201]:
            patient_id = response.json().get("id")
            print(f"✅ Test patient created with ID: {patient_id}")
            return patient_id
        else:
            print(f"❌ Error creating test patient: {response.status_code}")
            print(response.text)
            return None
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error when creating patient: {e}")
        return None

def get_patient(patient_id):
    """Get a patient by ID.

    Args:
        patient_id (str): The ID of the patient to retrieve

    Returns:
        bool: True if patient retrieved successfully, False otherwise
    """
    try:
        response = requests.get(
            f"{FHIR_SERVER_URL}/Patient/{patient_id}",
            auth=auth
        )

        if response.status_code == 200:
            print(f"✅ Patient retrieved successfully:")
            patient_data = response.json()
            print(f"  - ID: {patient_data.get('id')}")
            name = patient_data.get('name', [{}])[0]
            full_name = f"{' '.join(name.get('given', []))} {name.get('family', '')}"
            print(f"  - Name: {full_name}")
            print(f"  - Gender: {patient_data.get('gender', 'not specified')}")
            print(f"  - Birth date: {patient_data.get('birthDate', 'not specified')}")
            return True
        else:
            print(f"❌ Error retrieving patient: {response.status_code}")
            print(response.text)
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error when retrieving patient: {e}")
        return False

def run_tests():
    """Run all tests.

    This function orchestrates the test sequence:
    1. Check server status
    2. Create test patient
    3. Retrieve patient

    Returns:
        int: 0 if all tests pass, 1 otherwise (for shell exit code)
    """
    print("\n=== HAPI FHIR Server Test ===\n")

    # Check server status
    if not check_server_status():
        print("\n❌ The FHIR server is not available. Make sure it is running.")
        return 1

    # Create test patient
    print("\n--- Creating test patient ---")
    patient_id = create_test_patient()
    if not patient_id:
        print("\n❌ Could not create the test patient.")
        return 1

    # Retrieve patient
    print("\n--- Retrieving patient ---")
    if not get_patient(patient_id):
        print("\n❌ Could not retrieve the patient.")
        return 1

    print("\n✅ All tests completed successfully.")
    print(f"The HAPI FHIR server is working correctly at: {FHIR_SERVER_URL}")
    return 0

if __name__ == "__main__":
    sys.exit(run_tests())
EOL

chmod +x test_fhir_server.py
```

### Running the Test Script

Before running the test script, make sure you have the required Python packages installed:

```bash
pip install requests python-dotenv
```

Then run the script:

```bash
python test_fhir_server.py
```

If all tests pass, you'll see a success message. If any test fails, the script will provide information about what went wrong.

### Test Script Benefits

- **Automated verification**: Quickly confirms the server is working correctly
- **Comprehensive testing**: Tests multiple aspects of the FHIR server functionality
- **Reusable**: Can be run anytime to verify the server status
- **Educational**: Demonstrates how to interact with FHIR resources programmatically
- **Extensible**: Can be expanded to test additional FHIR resources and operations

## Conclusions and Production Considerations

### Key Decisions in Our Implementation

1. **PostgreSQL Database**: We chose PostgreSQL for robust data persistence and better performance, which is suitable for both development and production use.

2. **Docker Containerization**: Using Docker provides isolation and reproducibility, making it easier to deploy the same environment across different systems.

3. **Management Scripts**: We created scripts to simplify common operations, improving developer experience.

4. **Automated Testing**: The Python test script ensures the server is functioning correctly.

### Production Considerations

Before moving to a production environment, consider the following enhancements:

1. **Security Measures**:
   - Enable HTTPS with proper certificates
   - Implement robust authentication and authorization
   - Configure appropriate access controls
   - Set up audit logging

2. **High Availability**: Configure clustering or load balancing for production workloads.

3. **Monitoring and Alerting**: Set up monitoring tools to track server health and performance.

4. **Backup Strategy**: Implement regular database backups and disaster recovery procedures.

5. **Resource Allocation**: Adjust container resource limits based on expected workload.

6. **Update Strategy**: Plan for how to handle updates and migrations with minimal downtime.

### Implementation Roadmap

A typical progression from development to production might include:

1. **Development**: Current setup with PostgreSQL database
2. **Testing**: PostgreSQL database with test data
3. **Staging**: Full production-like environment with anonymized data
4. **Production**: Complete setup with all security measures and monitoring

## Next Steps

Now that you have the FHIR server up and running and verified its functionality, you can proceed to the [Postman Setup and Usage](04-postman_setup.md) section to learn how to interact with the FHIR server using Postman.
