# OMOP CDM Database Setup Guide

This guide provides an overview of database options for the OMOP Common Data Model (CDM) v5.4.2 to use with the FHIR to OMOP transformation project. For detailed, step-by-step instructions, please refer to our specific setup guides for each database system.

## Official Resources

- [OHDSI CommonDataModel Repository](https://github.com/OHDSI/CommonDataModel)
- [OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/)
- [DDL Files for CDM v5.4.2](https://github.com/OHDSI/CommonDataModel/tree/v5.4.2/inst/ddl/5.4)

## Database Setup Methods

We provide Python-based implementation for OMOP CDM database setup:

### Python-based Implementation (Recommended)

Uses our proven Python scripts based on official OHDSI methodology:
- ✅ **Official OHDSI methodology** - Based on <PERSON>'s official recommendations
- ✅ **Proven performance** - 62K records/sec documented performance
- ✅ **Educational value** - Step-by-step learning process
- ✅ **Customizable** - Easy to modify and extend
- ✅ **Integrated workflow** - Seamless with Python ETL pipeline
- ✅ **Community validated** - Confirmed by latest OHDSI projects (2025)

**Available for:**
- [📚 PostgreSQL Setup Guide →](postgresql_setup.md) (Recommended for production)
- [📚 SQLite Setup Guide →](sqlite_setup.md) (Recommended for development)

## Database Options and Setup Overview

A consolidated comparison of **PostgreSQL (Production)** and **SQLite (Development)**, followed by general setup steps and links to detailed guides.

| **Attribute**              | **PostgreSQL 🐘**<br>**Production**                | **SQLite 🐭**<br>**Development**               |
|----------------------------|---------------------------------------------------|-----------------------------------------------|
| **Best For**               | Large datasets, multi-user environments           | Testing, portability, single-user apps        |
| **Performance**            | High ✔️                                           | Limited ⚡ (small datasets)                   |
| **Scalability**            | Excellent 📈                                      | Limited                                       |
| **Concurrent Users**       | Supported                                       | Single user                                 |
| **Server Required**        | Yes 🖥️                                           | No 🚫                                         |
| **Portability**            | Low                                               | High 🌐                                       |
| **OHDSI Compatibility**    | Full ✅                                           | Partial ⚠️                                    |
| **Pros**                   | High perf, concurrent access, full OHDSI           | Simple setup, portable, no server req.       |
| **Cons**                   | Server setup required                             | Limited perf, single user                    |

### **Setup Process Overview**
1. **Create** the database and user  
2. **Define** OMOP CDM tables, keys, indexes, and constraints  
3. **Load** vocabulary data  
4. **Verify** the installation

### **Detailed Setup Guides**
- 📚 [PostgreSQL Setup Guide](postgresql_setup.md) – _Recommended for production_  
- 📚 [SQLite Setup Guide](sqlite_setup.md) – _Recommended for development_

## **Technical Decision: Python vs R Implementation**

Our implementation uses Python instead of the official OHDSI R method after comprehensive technical analysis. Key findings:

- **Superior constraint handling**: Our Python method resolves circular foreign key dependencies that R official method cannot handle
- **Complete functionality**: R official method only covers database structure, not vocabulary loading
- **Community validation**: Latest OHDSI community project (Synthea2OMOP-ETL, Jan 2025) uses identical methodology to ours
- **Performance**: Documented 62K records/sec vs uncertain R performance

**Detailed Analysis**: See [R vs Python Technical Analysis](../../architecture/r-vs-python-technical-analysis.md) for complete evaluation with evidence and citations.

## Vocabulary Setup

After setting up the database structure, you need to load the OMOP vocabularies. This process is described in detail in our database-specific setup guides.

The general process includes:

1. **Download vocabularies from [Athena](https://athena.ohdsi.org/)**
   - Register for an account
   - Select the vocabularies you need (SNOMED, LOINC, RxNorm, etc.)
   - Download the vocabulary files

2. **Load vocabularies into your database**
   - For PostgreSQL: Use the `\COPY` command
   - For SQLite: Use a Python script or SQLite's `.import` command

## Next Steps

After setting up the database:

1. Configure the environment variables in `.env`
2. Run the ETL process to transform FHIR data to OMOP
3. Consider running data quality checks using the [OHDSI Data Quality Dashboard](https://github.com/OHDSI/DataQualityDashboard)

## References

1. [OHDSI Common Data Model](https://ohdsi.github.io/CommonDataModel/)
2. [The Book of OHDSI](https://ohdsi.github.io/TheBookOfOhdsi/)
3. [OHDSI GitHub Repository](https://github.com/OHDSI/CommonDataModel)
