# Gemini - Estado del Arte sobre la Interoperabilidad Semántica y la Transformación de Datos entre los Estándares FHIR, OMOP CDM y openEHR en Informática Sanitaria

## **I. Introducción y Rationale**

La interoperabilidad efectiva entre los sistemas de información sanitaria se erige como un pilar fundamental para la mejora continua de la atención al paciente, el avance de la investigación clínica y la optimización de la salud pública.1 En este complejo ecosistema, estándares de datos como HL7 FHIR (Fast Healthcare Interoperability Resources), OHDSI OMOP (Observational Health Data Sciences and Informatics Observational Medical Outcomes Partnership) Common Data Model (CDM) y openEHR se han consolidado como elementos clave, cada uno con fortalezas y enfoques distintivos.1 Si bien cada estándar aborda necesidades específicas dentro del ámbito de la informática sanitaria, la consecución de una interoperabilidad semántica fluida y la implementación de transformaciones de datos fiables entre ellos continúa representando un desafío significativo.1 Este estudio tiene como objetivo primordial realizar una revisión exhaustiva y rigurosa del estado actual del arte en este dominio crucial, con un enfoque particular en las implementaciones prácticas y las herramientas disponibles, especialmente en el contexto de los sistemas de Historia Clínica Electrónica (HCE).1 La comprensión profunda de las estrategias, las tecnologías y los desafíos asociados a la interoperabilidad entre estos estándares resulta esencial para avanzar hacia un ecosistema de datos sanitarios más integrado y eficiente.

## **II. Entendiendo los Fundamentos: Principios Clave, Modelos de Datos y Casos de Uso Primarios**

### **II.A. HL7 FHIR**

HL7 FHIR se fundamenta en principios clave que priorizan la interoperabilidad y el intercambio de datos mediante el uso de tecnologías web modernas, como las APIs RESTful y los formatos de datos JSON y XML.2 El modelo de datos de FHIR se basa en el concepto de "recursos", donde la información sanitaria se representa como entidades discretas y autocontenidas.2 Estos recursos están diseñados para ser fácilmente comprensibles e intercambiables entre diferentes sistemas. En el contexto de los sistemas de HCE, FHIR se utiliza principalmente para facilitar el intercambio de datos entre diferentes sistemas y aplicaciones sanitarias, permitiendo una visión más completa y coordinada de la información del paciente.4 Además, FHIR desempeña un papel crucial en la mejora del acceso de los pacientes a sus propios registros de salud a través de portales y aplicaciones.6 Su estructura modular y su enfoque en la facilidad de uso para desarrolladores también lo convierten en una tecnología clave para la implementación de sistemas de soporte a la decisión clínica, proporcionando a los profesionales sanitarios información relevante y oportuna para la toma de decisiones.6 Asimismo, FHIR se está adoptando cada vez más en el ámbito de la vigilancia de la salud pública y la notificación de datos a las autoridades sanitarias.6 Finalmente, su capacidad para estructurar y compartir datos de investigación clínica de manera estandarizada está impulsando la colaboración y el avance del conocimiento médico.6 La flexibilidad y extensibilidad de FHIR se logran a través de mecanismos de "profiling" y "extensions", que permiten adaptar los recursos base a necesidades específicas.8 La adopción de FHIR se ha visto impulsada por su diseño, que aprovecha estándares web ampliamente utilizados, lo que facilita su implementación por parte de desarrolladores.2 Sin embargo, esta flexibilidad, aunque poderosa, puede generar variaciones en las implementaciones, lo que podría complicar la interoperabilidad si no se gestiona cuidadosamente mediante la definición de perfiles claros y guías de implementación detalladas.8

### **II.B. OHDSI OMOP Common Data Model (CDM)**

El OHDSI OMOP Common Data Model (CDM) se basa en principios fundamentales que buscan la estandarización de datos de salud observacionales para permitir su análisis sistemático y la generación de conocimiento científico.4 A diferencia de FHIR, OMOP CDM utiliza una estructura de base de datos relacional que organiza los datos en un conjunto de tablas estandarizadas, como las tablas de Persona, Visita, Condición y Medición.8 El principal caso de uso de OMOP CDM, especialmente en el contexto de los datos de HCE, radica en su capacidad para facilitar el análisis a gran escala y la investigación observacional a través de diversas fuentes de datos, incluyendo los propios sistemas de HCE.4 Al convertir datos heterogéneos de diferentes sistemas a un formato común, OMOP CDM permite la generación de evidencia del mundo real sobre la efectividad de los tratamientos y los resultados en pacientes.4 Además, facilita la colaboración en investigación entre diferentes instituciones y países, al proporcionar un marco de datos común para el análisis conjunto.4 OMOP CDM también juega un papel importante en la evaluación de la calidad de los datos y en la estandarización de las terminologías médicas utilizadas en las fuentes de datos originales.11 Para lograr esta estandarización semántica, OMOP CDM se apoya en el uso de vocabularios estandarizados como SNOMED CT, LOINC e ICD.8 La principal fortaleza de OMOP reside en su capacidad para armonizar datos de diversas procedencias en un formato consistente, idóneo para el análisis en profundidad.4 No obstante, su enfoque en la investigación, en contraposición a los flujos de trabajo clínicos en tiempo real, implica que en ciertos escenarios clínicos podría no capturar el mismo nivel de detalle que FHIR u openEHR.8 Su diseño centrado en la investigación y la analítica requiere una estructura rígida y estandarizada para permitir la comparación de datos de diferentes sistemas de HCE. Sin embargo, esta necesidad de estandarización puede llevar a la pérdida de detalles finos que podrían ser cruciales en un entorno clínico donde la captura de cada matiz de un encuentro con el paciente es importante.

### **II.C. openEHR**

openEHR se define por sus principios fundamentales como estándar abierto y neutral respecto al proveedor para la creación de Historias Clínicas Electrónicas centradas en el paciente y a lo largo de toda su vida, con un énfasis particular en el modelado clínico y la persistencia de datos.2 Su enfoque distintivo reside en su arquitectura de modelado de dos niveles: un Modelo de Referencia (RM) estable y un Modelo de Arquetipos (AM) flexible que utiliza arquetipos y plantillas.2 Esta separación permite que el sistema central (RM) permanezca estable y no requiera modificaciones ante la evolución del conocimiento clínico o los requisitos cambiantes.17 En el contexto de los sistemas de HCE, openEHR se emplea principalmente para proporcionar un modelo detallado y exhaustivo para la documentación clínica y el almacenamiento de datos.8 Su arquitectura está diseñada para ofrecer flexibilidad y adaptabilidad a la evolución del conocimiento clínico sin interrumpir los sistemas existentes.15 openEHR también se destaca por su capacidad para soportar la interoperabilidad semántica a través de su rico modelo de información y su enfoque basado en arquetipos.19 Además, la plataforma openEHR facilita el desarrollo rápido de aplicaciones mediante el uso de herramientas de bajo código.21 Su énfasis en la interoperabilidad semántica y los datos de salud computables lo convierte en una base sólida para sistemas de HCE robustos y adaptables.19 La principal fortaleza de openEHR radica en sus capacidades de modelado clínico detallado y su capacidad para evolucionar con el tiempo sin necesidad de revisiones importantes del sistema.15 Sin embargo, su complejidad y su curva de aprendizaje potencialmente más pronunciada en comparación con FHIR podrían obstaculizar su adopción generalizada en ciertos contextos.8 Su modelo complejo, con sus arquetipos y plantillas, podría hacerlo más desafiante de implementar y trabajar inicialmente en comparación con el enfoque más directo basado en recursos de FHIR.

## **III. El Desafío de la Interoperabilidad: Comparando Estructuras y Modelos de Datos**

### **III.A. Barreras Estructurales para la Interoperabilidad**

Una comparación detallada de las arquitecturas subyacentes de FHIR, OMOP CDM y openEHR revela diferencias fundamentales en sus filosofías de diseño que representan barreras estructurales para la interoperabilidad directa.2 FHIR, con su enfoque en el intercambio, adopta un modelo de datos más simplificado basado en recursos discretos, diseñados para ser fácilmente transferibles a través de redes.2 En contraste, openEHR ofrece un enfoque de modelado detallado, utilizando una arquitectura de dos niveles con un Modelo de Referencia estable y arquetipos flexibles para capturar la complejidad de la información clínica.2 OMOP CDM, por su parte, se centra en la investigación y el análisis, utilizando una estructura tabular en una base de datos relacional, optimizada para consultas y análisis estadísticos a gran escala.4 Esta disparidad en los enfoques estructurales implica que una correspondencia directa "talla única" entre los modelos de datos a menudo no es factible sin riesgo de pérdida de información o tergiversación.14 De hecho, la existencia de múltiples estándares con funcionalidades superpuestas puede generar una "paradoja de la interoperabilidad", donde en lugar de facilitar el intercambio de información, la abundancia de opciones lo dificulta.23 La decisión sobre qué estándar implementar puede volverse compleja debido a la superposición de funcionalidades y la falta de un claro ganador para todos los casos de uso.

### **III.B. Barreras Semánticas para la Interoperabilidad**

Lograr una verdadera interoperabilidad semántica va más allá de la mera correspondencia estructural; requiere una comprensión profunda del significado detrás de los elementos de datos en cada estándar y cómo se relacionan entre sí.19 Las diferencias en los niveles de granularidad y riqueza semántica que ofrece cada estándar representan una barrera significativa. openEHR, con sus "conjuntos de datos máximos" definidos por arquetipos, busca capturar la totalidad de la información relevante para un concepto clínico, mientras que FHIR, con sus "conjuntos de datos mínimos" en los recursos base, a menudo requiere el uso de perfiles y extensiones para alcanzar la granularidad necesaria.8 Además, las variaciones en el uso de terminologías y sistemas de codificación entre los estándares pueden generar inconsistencias semánticas.8 Por ejemplo, un mismo concepto clínico puede representarse con códigos diferentes en FHIR y OMOP, lo que dificulta su interpretación uniforme. Esta falta de alineación semántica puede conducir a la pérdida o alteración de información durante la transformación de datos, ya que las diferentes interpretaciones del significado clínico pueden no ser capturadas con precisión en el modelo de destino.14 Superar estas barreras semánticas a menudo implica un mapeo complejo de terminologías y la aplicación de tecnologías semánticas para vincular y armonizar los modelos de datos.

### **III.C. Matriz Comparativa de Estándares**

La siguiente tabla resume las diferencias estructurales y semánticas clave entre FHIR, OMOP y openEHR en relación con la interoperabilidad:

| **Característica** | **HL7 FHIR** | **OHDSI OMOP CDM** | **openEHR** |
| --- | --- | --- | --- |
| **Foco Primario** | Interoperabilidad e Intercambio de Datos | Investigación y Analítica | Modelado Clínico y Persistencia de Datos |
| **Modelo de Datos** | Basado en Recursos (RESTful) | Relacional (Tabular) | Dual-Nivel (RM y AM con Arquetipos y Plantillas) |
| **Granularidad de Datos** | Mínima (Recursos Base), Moderada (Perfiles) | Moderada | Máxima (Arquetipos) |
| **Flexibilidad/Extensibilidad** | Alta (a través de perfiles y extensiones) | Limitada (personalización con impacto) | Alta (a través de especialización de arquetipos) |
| **Casos de Uso Primarios** | Intercambio entre sistemas, acceso paciente, CDS, salud pública, investigación | Investigación observacional, análisis a gran escala, generación de evidencia | Documentación clínica detallada, EHR a largo plazo, interoperabilidad semántica |
| **Énfasis Semántico** | Moderado (uso de terminologías) | Alto (uso de vocabularios estandarizados) | Muy Alto (modelo de información rico, arquetipos) |
| **Facilidad de Implementación** | Relativamente Fácil | Compleja | Moderada |

## **IV. Mapeos y Transformaciones entre Estándares**

### **IV.A. Estrategias de Mapeo Documentadas**

Se han documentado diversas estrategias para el mapeo estructural y semántico entre pares de estándares. En el caso de FHIR y OMOP, se han explorado enfoques que buscan aprovechar la capacidad de FHIR para representar datos de manera granular y luego transformarlos en la estructura tabular de OMOP, optimizada para el análisis.4 Estas estrategias a menudo involucran el uso de servidores de terminología para alinear los códigos y conceptos entre los dos modelos.27 Para el mapeo entre openEHR y FHIR, las estrategias documentadas se centran en la traducción de la estructura jerárquica y rica en semántica de los arquetipos de openEHR a la estructura más plana y orientada al intercambio de los recursos FHIR.1 Esto a menudo requiere definir correspondencias entre los nodos de los arquetipos y los elementos de los recursos, considerando las diferencias en la granularidad y la representación de los conceptos clínicos. En cuanto al mapeo entre openEHR y OMOP, las estrategias buscan aprovechar la riqueza semántica de los arquetipos de openEHR para poblar las tablas estandarizadas de OMOP CDM, que están diseñadas para la investigación.1 Esto puede implicar la creación de consultas específicas (por ejemplo, utilizando AQL en openEHR) para extraer los datos relevantes de los arquetipos y luego mapearlos a los campos correspondientes en las tablas de OMOP. En general, los marcos y enfoques utilizados para estos mapeos varían desde reglas de mapeo directas y el uso de servidores de terminología hasta el desarrollo de lenguajes de mapeo específicos.27 La elección del enfoque a menudo depende del caso de uso específico y del nivel de fidelidad semántica que se busca alcanzar.

### **IV.B. El Rol de las Tecnologías Semánticas**

Las tecnologías semánticas, como RDF (Resource Description Framework), OWL (Web Ontology Language) y SPARQL (SPARQL Protocol and RDF Query Language), están demostrando ser herramientas valiosas para facilitar la vinculación de datos y la armonización entre FHIR, OMOP y openEHR.35 Estas tecnologías proporcionan una base semántica común para representar datos de diferentes estándares de una manera legible por máquinas, lo que permite una integración y consulta más inteligente.37 Por ejemplo, al transformar datos de FHIR, OMOP u openEHR a RDF, se pueden crear grafos de conocimiento donde los datos se vinculan en función de su significado, utilizando OWL para definir ontologías que formalizan las relaciones entre los conceptos.36 SPARQL, como lenguaje de consulta para RDF, permite realizar consultas complejas a través de estos grafos de conocimiento, facilitando la extracción de información integrada de fuentes heterogéneas. Se han documentado casos de uso donde estas tecnologías se han aplicado con éxito para abordar desafíos de interoperabilidad en el sector sanitario, como la creación de ontologías personalizadas para integrar datos administrativos y clínicos, y el desarrollo de sistemas que exponen bases de datos OMOP como grafos RDF compatibles con FHIR.36 Estas aplicaciones demuestran el potencial de las tecnologías semánticas para mejorar la interoperabilidad semántica al proporcionar un marco para comprender y relacionar datos representados en diferentes estándares.

### **IV.C. Automatización en el Mapeo y la Transformación**

La automatización se considera un factor crucial para escalar los esfuerzos de interoperabilidad y reducir la carga manual asociada con la transformación de datos entre FHIR, OMOP y openEHR.33 Se han realizado esfuerzos significativos para desarrollar herramientas y pipelines que automaticen los procesos de mapeo y transformación. Por ejemplo, se han creado convertidores de FHIR a OMOP y puentes de openEHR a FHIR que utilizan reglas predefinidas y lógicas de mapeo para facilitar la integración de datos.31 Además, se está investigando el uso de técnicas de aprendizaje automático y procesamiento del lenguaje natural para agilizar el mapeo semántico y la identificación de correspondencias entre elementos de datos en diferentes estándares.33 Estas técnicas pueden ayudar a comprender el significado de los elementos de datos y a sugerir mapeos potenciales, aunque a menudo se requiere la supervisión humana para garantizar la precisión y la validez de las transformaciones. Si bien se han logrado avances importantes en la automatización de estos procesos, alcanzar transformaciones completamente automatizadas y confiables sigue siendo un desafío debido a la complejidad semántica inherente a los datos sanitarios y las sutiles diferencias entre los estándares.22 Sin embargo, la disponibilidad y la madurez creciente de estas herramientas de código abierto representan un recurso valioso para el desarrollo futuro de nuevas soluciones de interoperabilidad.

## **V. Vinculación Terminológica y Armonización**

### **V.A. Uso de Terminologías Estándar**

El uso de terminologías estándar como SNOMED CT, LOINC e ICD es fundamental para facilitar la interoperabilidad semántica entre FHIR, OMOP y openEHR.1 Estos sistemas de terminología proporcionan un lenguaje común para representar conceptos clínicos, lo que permite que los datos intercambiados entre diferentes sistemas se comprendan con el mismo significado. FHIR, OMOP y openEHR admiten y utilizan estas terminologías en diversos grados. OMOP CDM depende en gran medida de vocabularios estandarizados para la representación del contenido de los datos, lo que garantiza la coherencia para el análisis.8 FHIR también incorpora terminologías estándar en sus recursos y proporciona mecanismos para especificar los sistemas de codificación utilizados.3 openEHR, a través de su enfoque basado en arquetipos, permite la vinculación de puntos de datos específicos a terminologías externas como SNOMED CT y LOINC, lo que enriquece la semántica de los datos modelados.20 Se han puesto en marcha iniciativas para promover el uso coherente de estas terminologías en los diferentes estándares. Por ejemplo, existe una colaboración activa entre SNOMED International y openEHR para mejorar la interoperabilidad y garantizar que los arquetipos de openEHR utilicen SNOMED CT de manera efectiva.42 La adopción generalizada de terminologías estándar es un pilar para lograr una interoperabilidad semántica sólida.

### **V.B. Desafíos en la Alineación Terminológica**

A pesar de la amplia adopción de terminologías estándar, persisten desafíos en el logro de una alineación terminológica completa entre FHIR, OMOP y openEHR.26 Uno de los principales desafíos radica en la gestión de diferentes versiones y subconjuntos de terminologías que pueden utilizar cada estándar o diferentes sistemas de salud.26 Por ejemplo, una organización que utiliza una versión específica de SNOMED CT podría tener dificultades para intercambiar datos con otra que utiliza una versión diferente, lo que podría llevar a interpretaciones inconsistentes de los conceptos clínicos. Otro desafío importante es el mapeo de códigos locales o no estándar a terminologías estándar.27 Muchos sistemas de HCE heredados utilizan sus propios sistemas de codificación internos, y la tarea de mapear estos códigos a SNOMED CT, LOINC o ICD puede ser compleja y requerir mucho tiempo. Además, pueden surgir complejidades debido a las variaciones en la forma en que se implementan y utilizan las terminologías en diferentes sistemas y regiones geográficas.26 Superar estos desafíos requiere un esfuerzo continuo en la armonización de terminologías, el desarrollo de mejores herramientas de mapeo y la promoción de directrices claras para el uso de estándares terminológicos.

### **V.C. Servidores y Servicios de Terminología**

Los servidores de terminología desempeñan un papel crucial en la provisión de acceso y la gestión de terminologías estándar para la interoperabilidad en el sector sanitario.4 Estos servidores actúan como repositorios centrales para vocabularios como SNOMED CT, LOINC e ICD, y ofrecen servicios para la validación de códigos, la expansión de conjuntos de valores y la traducción entre diferentes sistemas de codificación.46 FHIR aprovecha activamente los servidores de terminología a través de su especificación de API, lo que permite a las aplicaciones consultar y validar códigos, así como realizar operaciones más complejas como la traducción de conceptos entre diferentes terminologías.46 Existe un potencial significativo para utilizar servidores de terminología para facilitar el mapeo entre las terminologías utilizadas por FHIR, OMOP y openEHR.4 Al proporcionar un punto centralizado para la gestión de terminologías y el mapeo de conceptos, los servidores de terminología pueden ayudar a superar algunas de las barreras semánticas que dificultan la interoperabilidad entre estos estándares. El desarrollo y la adopción de servidores de terminología robustos y accesibles son pasos importantes hacia la mejora de la interoperabilidad semántica en el ecosistema de datos sanitarios.

## **VI. Herramientas y Pipelines de Código Abierto para la Transformación de Datos**

Existe un número creciente de herramientas y pipelines de código abierto diseñados para la transformación de datos entre FHIR, OMOP CDM y openEHR.31 Estas herramientas varían en su funcionalidad, la tecnología subyacente, su madurez, sus limitaciones y los posibles vacíos que aún existen. Por ejemplo, se han desarrollado convertidores de FHIR a OMOP que permiten transformar datos representados en recursos FHIR a la estructura tabular de OMOP CDM, facilitando el uso de datos clínicos para la investigación observacional.39 También existen puentes y conectores que permiten el intercambio de datos entre openEHR y FHIR, traduciendo entre la estructura basada en arquetipos de openEHR y la estructura basada en recursos de FHIR.31 Algunas plataformas buscan ofrecer capacidades de mapeo bidireccional, permitiendo la transformación de datos en ambas direcciones entre los estándares.40 La tecnología subyacente de estas herramientas puede incluir lenguajes de programación como Java y Python, así como marcos y plataformas específicos para el manejo de datos sanitarios. La madurez de estas herramientas varía; algunas son proyectos bien establecidos con una comunidad activa de desarrolladores y usuarios, mientras que otras se encuentran en etapas más tempranas de desarrollo. Las limitaciones pueden incluir la falta de soporte para todas las funcionalidades de los estándares, la necesidad de configuración y personalización para casos de uso específicos, y posibles pérdidas de información durante la transformación.39 Los posibles vacíos en las herramientas existentes pueden incluir la falta de soporte para ciertos tipos de transformaciones o la necesidad de herramientas más intuitivas y fáciles de usar para la definición y gestión de mapeos. El análisis detallado de estas herramientas de código abierto es fundamental para comprender su potencial utilidad y sus limitaciones en el desarrollo futuro de nuevas soluciones de interoperabilidad.

## **VII. Casos de Uso e Implementaciones Reales en Sistemas de HCE**

Se han documentado varios casos de uso e implementaciones reales de interoperabilidad entre FHIR, OMOP y openEHR, con un enfoque específico en el contexto de los sistemas de HCE.1 Estos casos de uso abarcan la integración de datos dentro de los sistemas de HCE, el intercambio de datos entre diferentes sistemas de HCE y la transformación de datos de HCE para fines de investigación. Por ejemplo, se han implementado soluciones que utilizan FHIR como estándar de intercambio para permitir que diferentes sistemas de HCE compartan información del paciente de manera más fluida.4 En el ámbito de la investigación, se han realizado proyectos que transforman datos de HCE a OMOP CDM para facilitar el análisis a gran escala y la generación de evidencia del mundo real.1 También se han explorado implementaciones que involucran a openEHR como repositorio central de datos clínicos, con interfaces FHIR para el intercambio con otros sistemas o para el acceso por parte de aplicaciones.19 Estos casos de uso reales proporcionan información valiosa sobre los desafíos prácticos y los éxitos en la consecución de la interoperabilidad entre estos estándares en entornos de HCE. Los desafíos encontrados a menudo incluyen la complejidad del mapeo de datos entre modelos diferentes, la necesidad de abordar las variaciones en la implementación de los estándares y la importancia de garantizar la calidad y la coherencia de los datos durante la transformación.15 Las lecciones aprendidas de estas implementaciones reales pueden informar y guiar los esfuerzos futuros en el avance de la interoperabilidad en el sector sanitario.

## **VIII. Retos Actuales: Técnicos y Semánticos**

La interoperabilidad entre FHIR, OMOP y openEHR en el contexto de los sistemas de HCE aún enfrenta importantes retos tanto técnicos como semánticos.10 Entre los principales desafíos técnicos se encuentra la **pérdida de información** durante el proceso de transformación de datos entre modelos con diferentes niveles de detalle y granularidad.14 Las **inconsistencias semánticas** también representan un obstáculo, ya que los mismos conceptos clínicos pueden representarse de manera diferente en cada estándar, utilizando distintas terminologías o estructuras de datos.14 La **escalabilidad** es otro desafío técnico importante, especialmente cuando se trata de grandes volúmenes de datos de HCE, donde la eficiencia de los procesos de transformación y la capacidad de los sistemas para manejar la carga de trabajo son cruciales.10 La **complejidad del mapeo** entre los diferentes modelos de datos y las terminologías asociadas también dificulta la interoperabilidad.10 Además, las **variaciones en la implementación y el "profiling"** de los estándares pueden introducir inconsistencias y dificultar el intercambio de datos entre diferentes sistemas.8

En cuanto a los desafíos semánticos, las **diferencias en la granularidad y la expresividad** de los modelos de datos pueden llevar a la pérdida de significado o a la necesidad de generalizar la información durante la transformación.8 La **ambigüedad y la dependencia del contexto** de los datos clínicos también complican la interoperabilidad semántica, ya que la misma información puede tener diferentes interpretaciones según el contexto en el que se registró.14 Las **dificultades en la alineación de conceptos** a través de diferentes terminologías y ontologías representan otro reto importante.27 Finalmente, un desafío semántico fundamental es **preservar el significado y el contexto original** de los datos durante el proceso de transformación, asegurando que la información se interprete correctamente en el sistema de destino.22 Superar estos retos técnicos y semánticos es esencial para lograr una interoperabilidad efectiva entre FHIR, OMOP y openEHR en el entorno de la informática sanitaria.

## **IX. Direcciones Futuras e Iniciativas de Armonización Emergentes**

El futuro de la interoperabilidad en el sector sanitario se perfila a través de diversas direcciones e iniciativas emergentes que buscan mejorar la integración entre FHIR, OMOP y openEHR.25 Los **principios FAIR** (Findable, Accessible, Interoperable, Reusable) están ganando cada vez más reconocimiento como guía para los esfuerzos de interoperabilidad, promoviendo la reutilización de datos de salud mediante la mejora de su localización, accesibilidad, interoperabilidad y reutilización.51 La **Inteligencia Artificial (IA)** y el **Aprendizaje Automático (ML)** también ofrecen un gran potencial para mejorar la integración de datos y el mapeo semántico entre diferentes estándares, automatizando tareas complejas y mejorando la precisión de las transformaciones.25 El concepto de **modelos computables de salud**, que buscan proporcionar representaciones formales del conocimiento clínico, podría facilitar la interoperabilidad al ofrecer una base común para la comprensión semántica de los datos.17 Además, se observa una tendencia hacia la **interoperabilidad pragmática**, que se centra en lograr la interoperabilidad para casos de uso específicos y de alta prioridad, en lugar de buscar una solución universal para todos los escenarios.51 Esta aproximación reconoce que la interoperabilidad completa y perfecta puede no ser siempre factible o necesaria, y que los esfuerzos deben dirigirse a resolver los problemas de intercambio de datos más críticos. Estas direcciones futuras y las iniciativas de armonización emergentes sugieren un panorama dinámico y en evolución para la interoperabilidad en la informática sanitaria.

## **X. Conclusiones y Recomendaciones**

El estado del arte en la interoperabilidad entre FHIR, OMOP CDM y openEHR revela un campo activo de investigación y desarrollo. Si bien cada estándar tiene propósitos y fortalezas distintos, la necesidad de un intercambio y una integración de datos fluidos y significativos es cada vez más apremiante en el sector sanitario. Se han logrado avances en el desarrollo de estrategias de mapeo, herramientas de código abierto y la aplicación de tecnologías semánticas, pero persisten desafíos técnicos y semánticos que dificultan una interoperabilidad completa.

Para avanzar hacia una mejor interoperabilidad, especialmente en el contexto de los sistemas de HCE, se proponen las siguientes recomendaciones:

- **Desarrollar directrices de mapeo estandarizadas y bien documentadas** para casos de uso específicos entre FHIR, OMOP y openEHR. Estas directrices deben considerar tanto los aspectos estructurales como los semánticos y deben ser accesibles a la comunidad.
- **Fomentar la adopción y el uso coherente de terminologías estándar** como SNOMED CT, LOINC e ICD en los tres estándares. Esto incluye la promoción de la implementación de servidores de terminología y servicios que faciliten la validación y el mapeo de códigos.
- **Promover el desarrollo y la evaluación de herramientas y plataformas de código abierto** para la transformación de datos entre los estándares. Se debe prestar especial atención a la mejora de la usabilidad, la funcionalidad y la cobertura de estas herramientas.
- **Investigar y aplicar el potencial de las tecnologías semánticas** (RDF, OWL, SPARQL) y la **Inteligencia Artificial (IA)** para mejorar la interoperabilidad semántica y automatizar los procesos de mapeo y transformación de datos.
- **Apoyar iniciativas que promuevan los principios FAIR** para los datos sanitarios, asegurando que los datos sean localizables, accesibles, interoperables y reutilizables para una variedad de propósitos, incluyendo la investigación.

En última instancia, lograr una interoperabilidad efectiva requiere un esfuerzo continuo de colaboración y estandarización dentro de la comunidad de informática sanitaria. Al abordar los desafíos técnicos y semánticos y al adoptar las direcciones futuras emergentes, se puede avanzar hacia un ecosistema de datos sanitarios más integrado y eficiente, que beneficie a los pacientes, los profesionales de la salud y la investigación.

### **Works cited**

1. GMS Medizinische Informatik, Biometrie und Epidemiologie | Mapping from openEHR to FHIR and OMOP CDM to support interoperability for infection control - German Medical Science, accessed April 10, 2025, [https://www.egms.de/en/journals/mibe/2021-17/mibe000221.shtml](https://www.egms.de/en/journals/mibe/2021-17/mibe000221.shtml)
2. Comparison of OpenEHR and HL7 FHIR Standards - ResearchGate, accessed April 10, 2025, [https://www.researchgate.net/publication/373951043_Comparison_of_OpenEHR_and_HL7_FHIR_Standards](https://www.researchgate.net/publication/373951043_Comparison_of_OpenEHR_and_HL7_FHIR_Standards)
3. Fast Healthcare Interoperability Resources (FHIR) for Interoperability in Health Research: Systematic Review - PMC - PubMed Central, accessed April 10, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC9346559/](https://pmc.ncbi.nlm.nih.gov/articles/PMC9346559/)
4. OMOP and FHIR in Data Standardization Process - Kodjin, accessed April 10, 2025, [https://kodjin.com/blog/omop-and-fhir-data-standardization/](https://kodjin.com/blog/omop-and-fhir-data-standardization/)
5. FHIR® - Fast Healthcare Interoperability Resources, accessed April 10, 2025, [https://ecqi.healthit.gov/fhir](https://ecqi.healthit.gov/fhir)
6. FHIR Use Cases: How FHIR is Transforming Healthcare - Binariks, accessed April 10, 2025, [https://binariks.com/blog/fhir-use-cases/](https://binariks.com/blog/fhir-use-cases/)
7. FHIR Use Cases - Breaking Down Digital Health Barriers and Transforming Care, accessed April 10, 2025, [https://www.intersystems.com/resources/fhir-use-cases-digital-health-barriers-transform-care/](https://www.intersystems.com/resources/fhir-use-cases-digital-health-barriers-transform-care/)
8. Which Health IT Standard to Pick: FHIR, openEHR, or OMOP? - Medblocks, accessed April 10, 2025, [https://medblocks.com/blog/which-health-it-standard-to-pick-fhir-openehr-or-omop](https://medblocks.com/blog/which-health-it-standard-to-pick-fhir-openehr-or-omop)
9. What is HL7 FHIR?, accessed April 10, 2025, [https://www.healthit.gov/sites/default/files/page/2021-04/What%20Is%20FHIR%20Fact%20Sheet.pdf](https://www.healthit.gov/sites/default/files/page/2021-04/What%20Is%20FHIR%20Fact%20Sheet.pdf)
10. FHIR vs openEHR, accessed April 10, 2025, [https://discourse.openehr.org/t/fhir-vs-openehr/2854](https://discourse.openehr.org/t/fhir-vs-openehr/2854)
11. Data Standardization – OHDSI, accessed April 10, 2025, [https://www.ohdsi.org/data-standardization/](https://www.ohdsi.org/data-standardization/)
12. OMOP Common Data Model - GitHub Pages, accessed April 10, 2025, [https://ohdsi.github.io/CommonDataModel/](https://ohdsi.github.io/CommonDataModel/)
13. A Guide to the OMOP Common Data Model - BioVox, accessed April 10, 2025, [https://biovox.eu/a-guide-to-the-omop-common-data-model/](https://biovox.eu/a-guide-to-the-omop-common-data-model/)
14. 2025 FHIR and OMOP Resource Alignment challenges - Vulcan Accelerator - HL7, accessed April 10, 2025, [https://confluence.hl7.org/spaces/VA/pages/325452052/2025+FHIR+and+OMOP+Resource+Alignment+challenges?src=contextnavpagetreemode](https://confluence.hl7.org/spaces/VA/pages/325452052/2025+FHIR+and+OMOP+Resource+Alignment+challenges?src=contextnavpagetreemode)
15. openEHR vs FHIR and other health IT standards - Medblocks, accessed April 10, 2025, [https://medblocks.com/blog/openehr-vs-fhir-and-other-health-it-standards](https://medblocks.com/blog/openehr-vs-fhir-and-other-health-it-standards)
16. openEHR and FHIR: best of both worlds. - vitagroup HIP, accessed April 10, 2025, [https://hip.vitagroup.ag/en/openehr-fhir/](https://hip.vitagroup.ag/en/openehr-fhir/)
17. openEHR Architecture Overview - openEHR Specifications, accessed April 10, 2025, [https://specifications.openehr.org/releases/BASE/Release-1.0.2/architecture_overview.html](https://specifications.openehr.org/releases/BASE/Release-1.0.2/architecture_overview.html)
18. What are openEHR Archetypes and Templates? A Beginner's Guide - Medblocks, accessed April 10, 2025, [https://medblocks.com/blog/what-are-openehr-archetypes-and-templates-a-beginners-guide](https://medblocks.com/blog/what-are-openehr-archetypes-and-templates-a-beginners-guide)
19. From OpenEHR to FHIR and OMOP Data Model for Microbiology Findings - ResearchGate, accessed April 10, 2025, [https://www.researchgate.net/publication/351922189_From_OpenEHR_to_FHIR_and_OMOP_Data_Model_for_Microbiology_Findings](https://www.researchgate.net/publication/351922189_From_OpenEHR_to_FHIR_and_OMOP_Data_Model_for_Microbiology_Findings)
20. openEHR vs. FHIR, accessed April 10, 2025, [https://openehr.ch/?p=1313](https://openehr.ch/?p=1313)
21. openehr.org – The future of digital health is open, accessed April 10, 2025, [https://www.openehr.org/](https://www.openehr.org/)
22. Issues in Data Mapping Between #fhir , #openEHR and #omop - YouTube, accessed April 10, 2025, [https://www.youtube.com/watch?v=c7ucoBIMnJY](https://www.youtube.com/watch?v=c7ucoBIMnJY)
23. Converge or Collide? Making Sense of a Plethora of Open Data Standards in Health Care, accessed April 10, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC11040436/](https://pmc.ncbi.nlm.nih.gov/articles/PMC11040436/)
24. Converge or Collide? Making Sense of a Plethora of Open Data Standards in Health Care, accessed April 10, 2025, [https://www.jmir.org/2024/1/e55779/](https://www.jmir.org/2024/1/e55779/)
25. State-of-the-Art Fast Healthcare Interoperability Resources (FHIR)–Based Data Model and Structure Implementations: Systematic Scoping Review, accessed April 10, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC11472501/](https://pmc.ncbi.nlm.nih.gov/articles/PMC11472501/)
26. Structure of Health Information With Different Information Models: Evaluation Study With Competency Questions - ResearchGate, accessed April 10, 2025, [https://www.researchgate.net/publication/372861008_Structure_of_Health_Information_With_Different_Information_Models_Evaluation_Study_With_Competency_Questions](https://www.researchgate.net/publication/372861008_Structure_of_Health_Information_With_Different_Information_Models_Evaluation_Study_With_Competency_Questions)
27. OHDSI on FHIR Platform Development with OMOP CDM mapping to FHIR Resources, accessed April 10, 2025, [https://www.ohdsi.org/web/wiki/lib/exe/fetch.php?media=resources:ohdsionfhir_gatech.pdf](https://www.ohdsi.org/web/wiki/lib/exe/fetch.php?media=resources:ohdsionfhir_gatech.pdf)
28. Home - FHIR to OMOP FHIR IG v0.1.0 - FHIR specification, accessed April 10, 2025, [https://build.fhir.org/ig/HL7/fhir-omop-ig/](https://build.fhir.org/ig/HL7/fhir-omop-ig/)
29. FHIR Coded Source Data to OMOP Patterns - Vulcan Accelerator - HL7 - Confluence, accessed April 10, 2025, [https://confluence.hl7.org/display/VA/FHIR+Coded+Source+Data+to+OMOP+Patterns](https://confluence.hl7.org/display/VA/FHIR+Coded+Source+Data+to+OMOP+Patterns)
30. 2025 FHIR Coded Source Data to OMOP Patterns - Vulcan Accelerator - Confluence, accessed April 10, 2025, [https://confluence.hl7.org/spaces/VA/pages/325452053/2025+FHIR+Coded+Source+Data+to+OMOP+Patterns](https://confluence.hl7.org/spaces/VA/pages/325452053/2025+FHIR+Coded+Source+Data+to+OMOP+Patterns)
31. openFHIR - Bridging openEHR & HL7 FHIR, accessed April 10, 2025, [https://open-fhir.com/](https://open-fhir.com/)
32. openEHR and FHIR, what to choose for the healthcare system processes in 2021 - Proxet, accessed April 10, 2025, [https://www.proxet.com/blog/openehr-and-fhir-what-to-choose-for-the-healthcare-system-processes-in-2021](https://www.proxet.com/blog/openehr-and-fhir-what-to-choose-for-the-healthcare-system-processes-in-2021)
33. Eos and OMOCL: Towards a seamless integration of openEHR records into the OMOP Common Data Model - ResearchGate, accessed April 10, 2025, [https://www.researchgate.net/publication/372290268_Eos_and_OMOCL_Towards_a_seamless_integration_of_openEHR_records_into_the_OMOP_Common_Data_Model](https://www.researchgate.net/publication/372290268_Eos_and_OMOCL_Towards_a_seamless_integration_of_openEHR_records_into_the_OMOP_Common_Data_Model)
34. ATLAS - openEHR, accessed April 10, 2025, [https://discourse.openehr.org/uploads/short-url/ns0kR6AzFHOMk8LekfkElUAqeXI.pptx](https://discourse.openehr.org/uploads/short-url/ns0kR6AzFHOMk8LekfkElUAqeXI.pptx)
35. Keynotes - Semantic Web Applications and Tools for Healthcare and Life Sciences, accessed April 10, 2025, [https://www.swat4ls.org/workshops/barcelona2025/programme/keynotes/](https://www.swat4ls.org/workshops/barcelona2025/programme/keynotes/)
36. Connecting openEHR data to Semantic Web - Integration, accessed April 10, 2025, [https://discourse.openehr.org/t/connecting-openehr-data-to-semantic-web/6244](https://discourse.openehr.org/t/connecting-openehr-data-to-semantic-web/6244)
37. Building clinical knowledge graphs in FHIR RDF with the OMOP Common data Model - PMC, accessed April 10, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC9561043/](https://pmc.ncbi.nlm.nih.gov/articles/PMC9561043/)
38. (PDF) Semantic Architecture for Interoperability in Distributed Healthcare Systems, accessed April 10, 2025, [https://www.researchgate.net/publication/365644176_Semantic_Architecture_for_Interoperability_in_Distributed_Healthcare_Systems](https://www.researchgate.net/publication/365644176_Semantic_Architecture_for_Interoperability_in_Distributed_Healthcare_Systems)
39. State-of-the-Art Fast Healthcare Interoperability Resources (FHIR)–Based Data Model and Structure Implementations: Systematic Scoping Review - JMIR Medical Informatics, accessed April 10, 2025, [https://medinform.jmir.org/2024/1/e58445](https://medinform.jmir.org/2024/1/e58445)
40. FHIR mapping tools - Implementation - openEHR, accessed April 10, 2025, [https://discourse.openehr.org/t/fhir-mapping-tools/4544](https://discourse.openehr.org/t/fhir-mapping-tools/4544)
41. Map clinical notes to the OMOP Common Data Model and healthcare ontologies using Amazon Comprehend Medical | AWS Machine Learning Blog, accessed April 10, 2025, [https://aws.amazon.com/blogs/machine-learning/map-clinical-notes-to-the-omop-common-data-model-and-healthcare-ontologies-using-amazon-comprehend-medical/](https://aws.amazon.com/blogs/machine-learning/map-clinical-notes-to-the-omop-common-data-model-and-healthcare-ontologies-using-amazon-comprehend-medical/)
42. SNOMED International and openEHR collaborate to advance health data interoperability, accessed April 10, 2025, [https://www.snomed.org/news/snomed-international-and-openehr-collaborate-to-advance-health-data-interoperability](https://www.snomed.org/news/snomed-international-and-openehr-collaborate-to-advance-health-data-interoperability)
43. OpenEHR with SNOMED,LOINC,HL-7 Support - Platform, accessed April 10, 2025, [https://discourse.openehr.org/t/openehr-with-snomed-loinc-hl-7-support/291](https://discourse.openehr.org/t/openehr-with-snomed-loinc-hl-7-support/291)
44. Assessing the Use of German Claims Data Vocabularies for Research in the Observational Medical Outcomes Partnership Common Data Model: Development and Evaluation Study, accessed April 10, 2025, [https://medinform.jmir.org/2023/1/e47959](https://medinform.jmir.org/2023/1/e47959)
45. OMOP limitations - Researchers - OHDSI Forums, accessed April 10, 2025, [https://forums.ohdsi.org/t/omop-limitations/13228](https://forums.ohdsi.org/t/omop-limitations/13228)
46. FHIR Terminology Service Explained with SNOMED CT & LOINC - YouTube, accessed April 10, 2025, [https://www.youtube.com/watch?v=-mQF3Rx-GdI](https://www.youtube.com/watch?v=-mQF3Rx-GdI)
47. Creating Terminology Server for FHIR using OMOP CDM - OHDSI, accessed April 10, 2025, [https://www.ohdsi.org/wp-content/uploads/2019/08/Creating-Terminology-Server.pdf](https://www.ohdsi.org/wp-content/uploads/2019/08/Creating-Terminology-Server.pdf)
48. FHIR Ecosystem | Interoperability Standards Platform (ISP), accessed April 10, 2025, [https://www.healthit.gov/isp/fhir-ecosystem](https://www.healthit.gov/isp/fhir-ecosystem)
49. Translate from OMOP to FHIR to bridge the gap between electronic health record formats, accessed April 10, 2025, [https://palantir.com/docs/foundry/use-case-examples/translating-from-omop-to-fhir-to-bridge-the-gap-between-electronic-health-record-formats//](https://palantir.com/docs/foundry/use-case-examples/translating-from-omop-to-fhir-to-bridge-the-gap-between-electronic-health-record-formats//)
50. Azure-Powered Healthcare: Implementing OpenEHR, FHIR, and OMOP in the European Health Data Space | by Olha Sholohon | Medium, accessed April 10, 2025, [https://medium.com/@olha.sholohon/azure-powered-healthcare-implementing-openehr-fhir-and-omop-in-the-european-health-data-space-5247a4bd71ad](https://medium.com/@olha.sholohon/azure-powered-healthcare-implementing-openehr-fhir-and-omop-in-the-european-health-data-space-5247a4bd71ad)
51. Can OpenEHR, ISO 13606, and HL7 FHIR Work Together? An Agnostic Approach for the Selection and Application of Electronic Health Record Standards to the Next-Generation Health Data Spaces, accessed April 10, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC10784985/](https://pmc.ncbi.nlm.nih.gov/articles/PMC10784985/)
52. Growing Compatibility of Open Standards in Digital Health - HealthManagement.org, accessed April 10, 2025, [https://healthmanagement.org/c/it/News/growing-compatibility-of-open-standards-in-digital-health](https://healthmanagement.org/c/it/News/growing-compatibility-of-open-standards-in-digital-health)
53. Aligning Semantic Interoperability Frameworks with the FOXS Stack for FAIR Health Data - Thieme Connect, accessed April 10, 2025, [https://www.thieme-connect.com/products/ejournals/pdf/10.1055/a-1993-8036.pdf](https://www.thieme-connect.com/products/ejournals/pdf/10.1055/a-1993-8036.pdf)
54. FAIR Guiding Principles - FHIR for FAIR - FHIR Implementation Guide v1.0.0, accessed April 10, 2025, [http://build.fhir.org/ig/HL7/fhir-for-fair/FAIR.html](http://build.fhir.org/ig/HL7/fhir-for-fair/FAIR.html)
55. (PDF) openEHR Is FAIR-Enabling by Design - ResearchGate, accessed April 10, 2025, [https://www.researchgate.net/publication/351930794_openEHR_Is_FAIR-Enabling_by_Design](https://www.researchgate.net/publication/351930794_openEHR_Is_FAIR-Enabling_by_Design)