{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Mapping FHIR to OMOP CDM - Part 1\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### cookbook : https://www.ohdsi.org/2024showcase-16/\n", "\n", "![FHIR to OMOP Cookbook](https://www.ohdsi.org/wp-content/uploads/2024/10/16-<PERSON>-FHIRtoOMOPCookbook-May-Terry.png)\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Overview\n", "\n", "This tutorial demonstrates how to transform data from HL7 FHIR (Fast Healthcare Interoperability Resources) to the OMOP Common Data Model (CDM). By the end of this tutorial, you'll understand:\n", "\n", "- The conceptual mapping between FHIR resources and OMOP tables\n", "- How to extract data from FHIR resources using Python\n", "- How to transform FHIR data to fit the OMOP CDM structure\n", "- How to handle vocabulary mapping between FHIR and OMOP\n", "- How to load transformed data into an OMOP database\n", "\n", "## Prerequisites\n", "\n", "- Understanding of FHIR resources (from Tutorial 1)\n", "- Understanding of OMOP CDM structure (from Tutorial 2)\n", "- Basic Python programming knowledge\n", "- Familiarity with pandas and SQL\n", "\n", "Let's begin by importing the libraries we'll need for this tutorial:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime, timedelta\n", "import os\n", "import sqlite3\n", "from sqlalchemy import create_engine, text\n", "import requests\n", "from fhirclient import client\n", "from fhirclient.models import patient, condition, observation, encounter, medicationrequest\n", "import warnings\n", "\n", "# Set up visualization style\n", "plt.style.use('ggplot')\n", "sns.set(style=\"whitegrid\")\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Conceptual Mapping Between FHIR and OMOP\n", "\n", "Before we start coding, it's important to understand the conceptual mapping between FHIR resources and OMOP tables. While both models represent healthcare data, they have different structures and purposes:\n", "\n", "- **FHIR** is designed for interoperability and data exchange between systems\n", "- **OMOP** is designed for standardized analytics and research\n", "\n", "Here's a high-level mapping of key FHIR resources to OMOP tables:\n", "\n", "| FHIR Resource | OMOP Table(s) | Notes |\n", "|---------------|---------------|-------|\n", "| Patient | PERSON | Demographics and person identifiers |\n", "| Encounter | VISIT_OCCURRENCE | Healthcare encounters |\n", "| Condition | CONDITION_OCCURRENCE | Diagnoses and medical conditions |\n", "| Observation | MEASUREMENT or OBSERVATION | Lab tests, vital signs, and other observations |\n", "| MedicationRequest | DRUG_EXPOSURE | Medication orders and prescriptions |\n", "| Procedure | PROCEDURE_OCCURRENCE | Medical procedures |\n", "| Immunization | DRUG_EXPOSURE | Vaccinations |\n", "| AllergyIntolerance | CONDITION_OCCURRENCE | Allergies and intolerances |\n", "| Device | DEVICE_EXPOSURE | Medical devices |\n", "\n", "### Mapping Challenges\n", "\n", "Several challenges arise when mapping FHIR to OMOP:\n", "\n", "1. **Structural differences**: FHIR uses a resource-based model, while OMOP uses a relational model\n", "2. **Vocabulary differences**: FHIR and OMOP use different terminology systems\n", "3. **Granularity differences**: FHIR may have more detailed information than OMOP can represent\n", "4. **Relationship representation**: FHIR uses references, while OMOP uses foreign keys\n", "5. **Extensions**: FHIR allows custom extensions that may not map cleanly to OMOP\n", "\n", "Let's examine each of these challenges and how to address them."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Mapping FHIR to OMOP CDM - Part 2\n", "\n", "## 2. Setting Up the Environment\n", "\n", "Before we start mapping data, let's set up our environment. We'll need:\n", "\n", "1. A source of FHIR data\n", "2. An OMOP CDM database\n", "3. Vocabulary mappings\n", "\n", "For this tutorial, we'll use:\n", "\n", "- Sample FHIR resources in JSON format\n", "- A SQLite database with OMOP CDM tables\n", "- Simplified vocabulary mappings"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a directory for our data if it doesn't exist\n", "os.makedirs('data', exist_ok=True)\n", "\n", "# Create sample FHIR resources\n", "sample_patient = {\n", "    \"resourceType\": \"Patient\",\n", "    \"id\": \"patient-1\",\n", "    \"identifier\": [\n", "        {\n", "            \"system\": \"http://hospital.example.org\",\n", "            \"value\": \"12345\"\n", "        }\n", "    ],\n", "    \"active\": True,\n", "    \"name\": [\n", "        {\n", "            \"use\": \"official\",\n", "            \"family\": \"<PERSON>\",\n", "            \"given\": [\"<PERSON>\", \"<PERSON>\"]\n", "        }\n", "    ],\n", "    \"gender\": \"male\",\n", "    \"birthDate\": \"1970-01-25\",\n", "    \"address\": [\n", "        {\n", "            \"use\": \"home\",\n", "            \"line\": [\"123 Main St\"],\n", "            \"city\": \"Anytown\",\n", "            \"state\": \"CA\",\n", "            \"postalCode\": \"12345\",\n", "            \"country\": \"US\"\n", "        }\n", "    ]\n", "}\n", "\n", "sample_encounter = {\n", "    \"resourceType\": \"Encounter\",\n", "    \"id\": \"encounter-1\",\n", "    \"status\": \"finished\",\n", "    \"class\": {\n", "        \"system\": \"http://terminology.hl7.org/CodeSystem/v3-ActCode\",\n", "        \"code\": \"AMB\",\n", "        \"display\": \"ambulatory\"\n", "    },\n", "    \"type\": [\n", "        {\n", "            \"coding\": [\n", "                {\n", "                    \"system\": \"http://snomed.info/sct\",\n", "                    \"code\": \"270427003\",\n", "                    \"display\": \"Patient-initiated encounter\"\n", "                }\n", "            ]\n", "        }\n", "    ],\n", "    \"subject\": {\n", "        \"reference\": \"Patient/patient-1\"\n", "    },\n", "    \"period\": {\n", "        \"start\": \"2023-01-15T09:00:00Z\",\n", "        \"end\": \"2023-01-15T10:30:00Z\"\n", "    }\n", "}\n", "\n", "sample_condition = {\n", "    \"resourceType\": \"Condition\",\n", "    \"id\": \"condition-1\",\n", "    \"clinicalStatus\": {\n", "        \"coding\": [\n", "            {\n", "                \"system\": \"http://terminology.hl7.org/CodeSystem/condition-clinical\",\n", "                \"code\": \"active\",\n", "                \"display\": \"Active\"\n", "            }\n", "        ]\n", "    },\n", "    \"verificationStatus\": {\n", "        \"coding\": [\n", "            {\n", "                \"system\": \"http://terminology.hl7.org/CodeSystem/condition-ver-status\",\n", "                \"code\": \"confirmed\",\n", "                \"display\": \"Confirmed\"\n", "            }\n", "        ]\n", "    },\n", "    \"code\": {\n", "        \"coding\": [\n", "            {\n", "                \"system\": \"http://snomed.info/sct\",\n", "                \"code\": \"59621000\",\n", "                \"display\": \"Essential hypertension\"\n", "            }\n", "        ]\n", "    },\n", "    \"subject\": {\n", "        \"reference\": \"Patient/patient-1\"\n", "    },\n", "    \"encounter\": {\n", "        \"reference\": \"Encounter/encounter-1\"\n", "    },\n", "    \"onsetDateTime\": \"2023-01-15T09:30:00Z\",\n", "    \"recordedDate\": \"2023-01-15T09:30:00Z\"\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Mapping FHIR to OMOP CDM - Part 3\n", "\n", "## 2. Setting Up the Environment (continued)\n", "\n", "Let's continue setting up our environment by creating the sample observation and medication request resources, and then setting up our OMOP database."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample_observation = {\n", "    \"resourceType\": \"Observation\",\n", "    \"id\": \"observation-1\",\n", "    \"status\": \"final\",\n", "    \"category\": [\n", "        {\n", "            \"coding\": [\n", "                {\n", "                    \"system\": \"http://terminology.hl7.org/CodeSystem/observation-category\",\n", "                    \"code\": \"vital-signs\",\n", "                    \"display\": \"Vital Signs\"\n", "                }\n", "            ]\n", "        }\n", "    ],\n", "    \"code\": {\n", "        \"coding\": [\n", "            {\n", "                \"system\": \"http://loinc.org\",\n", "                \"code\": \"8480-6\",\n", "                \"display\": \"Systolic blood pressure\"\n", "            }\n", "        ]\n", "    },\n", "    \"subject\": {\n", "        \"reference\": \"Patient/patient-1\"\n", "    },\n", "    \"encounter\": {\n", "        \"reference\": \"Encounter/encounter-1\"\n", "    },\n", "    \"effectiveDateTime\": \"2023-01-15T09:15:00Z\",\n", "    \"valueQuantity\": {\n", "        \"value\": 140,\n", "        \"unit\": \"mmHg\",\n", "        \"system\": \"http://unitsofmeasure.org\",\n", "        \"code\": \"mm[Hg]\"\n", "    }\n", "}\n", "\n", "sample_medication_request = {\n", "    \"resourceType\": \"MedicationRequest\",\n", "    \"id\": \"medicationrequest-1\",\n", "    \"status\": \"active\",\n", "    \"intent\": \"order\",\n", "    \"medicationCodeableConcept\": {\n", "        \"coding\": [\n", "            {\n", "                \"system\": \"http://www.nlm.nih.gov/research/umls/rxnorm\",\n", "                \"code\": \"314076\",\n", "                \"display\": \"Lisinopril 10 MG Oral Tablet\"\n", "            }\n", "        ]\n", "    },\n", "    \"subject\": {\n", "        \"reference\": \"Patient/patient-1\"\n", "    },\n", "    \"encounter\": {\n", "        \"reference\": \"Encounter/encounter-1\"\n", "    },\n", "    \"authoredOn\": \"2023-01-15T10:00:00Z\",\n", "    \"requester\": {\n", "        \"reference\": \"Practitioner/practitioner-1\"\n", "    },\n", "    \"dosageInstruction\": [\n", "        {\n", "            \"text\": \"Take 1 tablet by mouth once daily\",\n", "            \"timing\": {\n", "                \"repeat\": {\n", "                    \"frequency\": 1,\n", "                    \"period\": 1,\n", "                    \"periodUnit\": \"d\"\n", "                }\n", "            },\n", "            \"doseAndRate\": [\n", "                {\n", "                    \"doseQuantity\": {\n", "                        \"value\": 1,\n", "                        \"unit\": \"tablet\",\n", "                        \"system\": \"http://terminology.hl7.org/CodeSystem/v3-orderableDrugForm\",\n", "                        \"code\": \"TAB\"\n", "                    }\n", "                }\n", "            ]\n", "        }\n", "    ],\n", "    \"dispenseRequest\": {\n", "        \"numberOfRepeatsAllowed\": 3,\n", "        \"quantity\": {\n", "            \"value\": 30,\n", "            \"unit\": \"tablet\",\n", "            \"system\": \"http://terminology.hl7.org/CodeSystem/v3-orderableDrugForm\",\n", "            \"code\": \"TAB\"\n", "        },\n", "        \"expectedSupplyDuration\": {\n", "            \"value\": 30,\n", "            \"unit\": \"days\",\n", "            \"system\": \"http://unitsofmeasure.org\",\n", "            \"code\": \"d\"\n", "        }\n", "    }\n", "}\n", "\n", "# Save sample FHIR resources to files\n", "with open('data/sample_patient.json', 'w') as f:\n", "    json.dump(sample_patient, f, indent=2)\n", "\n", "with open('data/sample_encounter.json', 'w') as f:\n", "    json.dump(sample_encounter, f, indent=2)\n", "\n", "with open('data/sample_condition.json', 'w') as f:\n", "    json.dump(sample_condition, f, indent=2)\n", "\n", "with open('data/sample_observation.json', 'w') as f:\n", "    json.dump(sample_observation, f, indent=2)\n", "\n", "with open('data/sample_medication_request.json', 'w') as f:\n", "    json.dump(sample_medication_request, f, indent=2)\n", "\n", "print(\"Sample FHIR resources created and saved to files.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now let's create a simple OMOP CDM database using SQLite:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a SQLite database with OMOP CDM tables\n", "def create_omop_database():\n", "    \"\"\"Create a SQLite database with OMOP CDM tables.\"\"\"\n", "    # Create a connection to a new SQLite database\n", "    conn = sqlite3.connect('data/omop_cdm.db')\n", "    cursor = conn.cursor()\n", "    \n", "    # Create PERSON table\n", "    cursor.execute('''\n", "    CREATE TABLE IF NOT EXISTS person (\n", "        person_id INTEGER PRIMARY KEY,\n", "        gender_concept_id INTEGER,\n", "        year_of_birth INTEGER,\n", "        month_of_birth INTEGER,\n", "        day_of_birth INTEGER,\n", "        birth_datetime TEXT,\n", "        race_concept_id INTEGER,\n", "        ethnicity_concept_id INTEGER,\n", "        location_id INTEGER,\n", "        provider_id INTEGER,\n", "        care_site_id INTEGER,\n", "        person_source_value TEXT,\n", "        gender_source_value TEXT,\n", "        gender_source_concept_id INTEGER,\n", "        race_source_value TEXT,\n", "        race_source_concept_id INTEGER,\n", "        ethnicity_source_value TEXT,\n", "        ethnicity_source_concept_id INTEGER\n", "    )\n", "    ''')\n", "    \n", "    # Create VISIT_OCCURRENCE table\n", "    cursor.execute('''\n", "    CREATE TABLE IF NOT EXISTS visit_occurrence (\n", "        visit_occurrence_id INTEGER PRIMARY KEY,\n", "        person_id INTEGER,\n", "        visit_concept_id INTEGER,\n", "        visit_start_date TEXT,\n", "        visit_start_datetime TEXT,\n", "        visit_end_date TEXT,\n", "        visit_end_datetime TEXT,\n", "        visit_type_concept_id INTEGER,\n", "        provider_id INTEGER,\n", "        care_site_id INTEGER,\n", "        visit_source_value TEXT,\n", "        visit_source_concept_id INTEGER,\n", "        admitted_from_concept_id INTEGER,\n", "        admitted_from_source_value TEXT,\n", "        discharge_to_concept_id INTEGER,\n", "        discharge_to_source_value TEXT,\n", "        preceding_visit_occurrence_id INTEGER,\n", "        FOREIGN KEY (person_id) REFERENCES person(person_id)\n", "    )\n", "    ''')\n", "    \n", "    # Create CONDITION_OCCURRENCE table\n", "    cursor.execute('''\n", "    CREATE TABLE IF NOT EXISTS condition_occurrence (\n", "        condition_occurrence_id INTEGER PRIMARY KEY,\n", "        person_id INTEGER,\n", "        condition_concept_id INTEGER,\n", "        condition_start_date TEXT,\n", "        condition_start_datetime TEXT,\n", "        condition_end_date TEXT,\n", "        condition_end_datetime TEXT,\n", "        condition_type_concept_id INTEGER,\n", "        condition_status_concept_id INTEGER,\n", "        stop_reason TEXT,\n", "        provider_id INTEGER,\n", "        visit_occurrence_id INTEGER,\n", "        visit_detail_id INTEGER,\n", "        condition_source_value TEXT,\n", "        condition_source_concept_id INTEGER,\n", "        condition_status_source_value TEXT,\n", "        FOREIGN KEY (person_id) REFERENCES person(person_id),\n", "        FOREIGN KEY (visit_occurrence_id) REFERENCES visit_occurrence(visit_occurrence_id)\n", "    )\n", "    ''')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Mapping FHIR to OMOP CDM - Part 4\n", "\n", "## 2. Setting Up the Environment (continued)\n", "\n", "Let's continue setting up our OMOP database with the necessary tables and concept mappings."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create MEASUREMENT table\n", "    cursor.execute('''\n", "    CREATE TABLE IF NOT EXISTS measurement (\n", "        measurement_id INTEGER PRIMARY KEY,\n", "        person_id INTEGER,\n", "        measurement_concept_id INTEGER,\n", "        measurement_date TEXT,\n", "        measurement_datetime TEXT,\n", "        measurement_time TEXT,\n", "        measurement_type_concept_id INTEGER,\n", "        operator_concept_id INTEGER,\n", "        value_as_number REAL,\n", "        value_as_concept_id INTEGER,\n", "        unit_concept_id INTEGER,\n", "        range_low REAL,\n", "        range_high REAL,\n", "        provider_id INTEGER,\n", "        visit_occurrence_id INTEGER,\n", "        visit_detail_id INTEGER,\n", "        measurement_source_value TEXT,\n", "        measurement_source_concept_id INTEGER,\n", "        unit_source_value TEXT,\n", "        value_source_value TEXT,\n", "        FOREIGN KEY (person_id) REFERENCES person(person_id),\n", "        FOREIGN KEY (visit_occurrence_id) REFERENCES visit_occurrence(visit_occurrence_id)\n", "    )\n", "    ''')\n", "    \n", "    # Create DRUG_EXPOSURE table\n", "    cursor.execute('''\n", "    CREATE TABLE IF NOT EXISTS drug_exposure (\n", "        drug_exposure_id INTEGER PRIMARY KEY,\n", "        person_id INTEGER,\n", "        drug_concept_id INTEGER,\n", "        drug_exposure_start_date TEXT,\n", "        drug_exposure_start_datetime TEXT,\n", "        drug_exposure_end_date TEXT,\n", "        drug_exposure_end_datetime TEXT,\n", "        verbatim_end_date TEXT,\n", "        drug_type_concept_id INTEGER,\n", "        stop_reason TEXT,\n", "        refills INTEGER,\n", "        quantity REAL,\n", "        days_supply INTEGER,\n", "        sig TEXT,\n", "        route_concept_id INTEGER,\n", "        lot_number TEXT,\n", "        provider_id INTEGER,\n", "        visit_occurrence_id INTEGER,\n", "        visit_detail_id INTEGER,\n", "        drug_source_value TEXT,\n", "        drug_source_concept_id INTEGER,\n", "        route_source_value TEXT,\n", "        dose_unit_source_value TEXT,\n", "        FOREIGN KEY (person_id) REFERENCES person(person_id),\n", "        FOREIGN KEY (visit_occurrence_id) REFERENCES visit_occurrence(visit_occurrence_id)\n", "    )\n", "    ''')\n", "    \n", "    # Create CONCEPT table (simplified for this tutorial)\n", "    cursor.execute('''\n", "    CREATE TABLE IF NOT EXISTS concept (\n", "        concept_id INTEGER PRIMARY KEY,\n", "        concept_name TEXT,\n", "        domain_id TEXT,\n", "        vocabulary_id TEXT,\n", "        concept_class_id TEXT,\n", "        standard_concept TEXT,\n", "        concept_code TEXT,\n", "        valid_start_date TEXT,\n", "        valid_end_date TEXT,\n", "        invalid_reason TEXT\n", "    )\n", "    ''')\n", "    \n", "    # Create SOURCE_TO_CONCEPT_MAP table (simplified for this tutorial)\n", "    cursor.execute('''\n", "    CREATE TABLE IF NOT EXISTS source_to_concept_map (\n", "        source_code TEXT,\n", "        source_vocabulary_id TEXT,\n", "        source_code_description TEXT,\n", "        target_concept_id INTEGER,\n", "        target_vocabulary_id TEXT,\n", "        valid_start_date TEXT,\n", "        valid_end_date TEXT,\n", "        invalid_reason TEXT,\n", "        PRIMARY KEY (source_code, source_vocabulary_id)\n", "    )\n", "    ''')\n", "    \n", "    # Insert some concept mappings\n", "    concept_data = [\n", "        (8507, \"Male\", \"Gender\", \"Gender\", \"Gender\", \"S\", \"M\", \"1970-01-01\", \"2099-12-31\", None),\n", "        (8532, \"Female\", \"Gender\", \"Gender\", \"Gender\", \"S\", \"F\", \"1970-01-01\", \"2099-12-31\", None),\n", "        (9202, \"Outpatient Visit\", \"Visit\", \"Visit\", \"Visit\", \"S\", \"OP\", \"1970-01-01\", \"2099-12-31\", None),\n", "        (9201, \"Inpatient Visit\", \"Visit\", \"Visit\", \"Visit\", \"S\", \"IP\", \"1970-01-01\", \"2099-12-31\", None),\n", "        (320128, \"Essential hypertension\", \"Condition\", \"SNOMED\", \"Clinical Finding\", \"S\", \"59621000\", \"1970-01-01\", \"2099-12-31\", None),\n", "        (3012888, \"Systolic blood pressure\", \"Measurement\", \"LOINC\", \"Clinical Observation\", \"S\", \"8480-6\", \"1970-01-01\", \"2099-12-31\", None),\n", "        (1545999, \"Lisinopril 10 MG Oral Tablet\", \"Drug\", \"RxNorm\", \"Clinical Drug\", \"S\", \"314076\", \"1970-01-01\", \"2099-12-31\", None),\n", "        (32020, \"EHR encounter diagnosis\", \"Type Concept\", \"Type Concept\", \"Type Concept\", \"S\", \"EHRDX\", \"1970-01-01\", \"2099-12-31\", None),\n", "        (44818518, \"Visit derived from EHR record\", \"Type Concept\", \"Type Concept\", \"Type Concept\", \"S\", \"EHRVIST\", \"1970-01-01\", \"2099-12-31\", None),\n", "        (44818701, \"From physical examination\", \"Type Concept\", \"Type Concept\", \"Type Concept\", \"S\", \"EXAM\", \"1970-01-01\", \"2099-12-31\", None),\n", "        (38000177, \"Prescription written\", \"Type Concept\", \"Type Concept\", \"Type Concept\", \"S\", \"RX\", \"1970-01-01\", \"2099-12-31\", None),\n", "        (4172703, \"Equal\", \"Meas Value Operator\", \"Meas Value Operator\", \"Operator\", \"S\", \"=\", \"1970-01-01\", \"2099-12-31\", None),\n", "        (8876, \"millimeter mercury column\", \"Unit\", \"Unit\", \"Unit\", \"S\", \"mmHg\", \"1970-01-01\", \"2099-12-31\", None)\n", "    ]\n", "    \n", "    cursor.executemany('''\n", "    INSERT INTO concept (\n", "        concept_id, concept_name, domain_id, vocabulary_id, concept_class_id,\n", "        standard_concept, concept_code, valid_start_date, valid_end_date, invalid_reason\n", "    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n", "    ''', concept_data)\n", "    \n", "    # Insert source to concept mappings\n", "    mapping_data = [\n", "        (\"male\", \"FHIR\", \"Male gender\", 8507, \"Gender\", \"1970-01-01\", \"2099-12-31\", None),\n", "        (\"female\", \"FHIR\", \"Female gender\", 8532, \"Gender\", \"1970-01-01\", \"2099-12-31\", None),\n", "        (\"AMB\", \"FHIR\", \"Ambulatory visit\", 9202, \"Visit\", \"1970-01-01\", \"2099-12-31\", None),\n", "        (\"IMP\", \"FHIR\", \"Inpatient visit\", 9201, \"Visit\", \"1970-01-01\", \"2099-12-31\", None),\n", "        (\"59621000\", \"SNOMED\", \"Essential hypertension\", 320128, \"SNOMED\", \"1970-01-01\", \"2099-12-31\", None),\n", "        (\"8480-6\", \"LOINC\", \"Systolic blood pressure\", 3012888, \"LOINC\", \"1970-01-01\", \"2099-12-31\", None),\n", "        (\"314076\", \"RxNorm\", \"Lisinopril 10 MG Oral Tablet\", 1545999, \"RxNorm\", \"1970-01-01\", \"2099-12-31\", None)\n", "    ]\n", "    \n", "    cursor.executemany('''\n", "    INSERT INTO source_to_concept_map (\n", "        source_code, source_vocabulary_id, source_code_description,\n", "        target_concept_id, target_vocabulary_id, valid_start_date, valid_end_date, invalid_reason\n", "    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)\n", "    ''', mapping_data)\n", "    \n", "    # Commit changes and close connection\n", "    conn.commit()\n", "    conn.close()\n", "    \n", "    return 'data/omop_cdm.db'\n", "\n", "# Create the OMOP database\n", "omop_db_path = create_omop_database()\n", "print(f\"OMOP CDM database created at: {omop_db_path}\")\n", "\n", "# Create a SQLAlchemy engine for the database\n", "engine = create_engine(f'sqlite:///{omop_db_path}')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}