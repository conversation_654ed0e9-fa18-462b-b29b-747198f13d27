# Issue: Migration from PyTest to unittest

## **General Information**

- **Type**: Refactoring/Technical Debt
- **Priority**: Medium-Low
- **Estimation**: 3-4 development days + 1 testing day
- **Prerequisite**: Complete FHIR→OMOP transformation MVP
- **Target branch**: `refactor/migrate-to-unittest`

## **Context and Justification**

### **Current Situation**
The project has a hybrid testing implementation:
- `tests/test_omop_database/`: Uses pytest with complex fixtures
- `src/fhir_omop/etl/abu_dhabi_claims_mvp/`: Already uses unittest.TestCase
- External pytest dependency vs stdlib unittest

### **Reasons for Migration**
1. **Team experience alignment**: Team already masters unittest
2. **Consistency**: Unify testing framework across the entire project
3. **Reduce dependencies**: unittest is part of stdlib (no external deps)
4. **Maintainability**: More explicit code, less "magic"

### **Priority Status**
- ⚠️ **NOT critical for MVP**: This migration should be performed AFTER completing the first functional FHIR→OMOP transformation
- ✅ **Technical improvement**: Benefits long-term consistency and maintainability

## **Detailed Technical Analysis**

### **Affected Files**

#### **1. Configuration Files**
```
environment.yml          # Remove pytest, pytest-cov
pytest.ini              # DELETE file
.github/workflows/      # Update CI/CD commands
```

#### **2. Main Test Files**
```
tests/conftest.py                           # CRITICAL REFACTORING
tests/test_omop_database/conftest.py        # CRITICAL REFACTORING  
tests/test_omop_database/test_create_database.py  # MEDIUM REFACTORING
```

#### **3. Already Compatible Files**
```
src/fhir_omop/etl/abu_dhabi_claims_mvp/test_abu_dhabi_etl.py  # ✅ ALREADY CORRECT
```

## **Critical Migration Points**

### **CRITICAL 1: Global Fixtures Migration**

#### **Current State (tests/conftest.py)**
```python
@pytest.fixture
def mock_env_config():
    return {
        'OMOP_DB_HOST': 'localhost',
        'OMOP_DB_PORT': '5432',
        # ...
    }

@pytest.fixture
def temp_data_dir(tmp_path):
    data_dir = tmp_path / "test_data"
    data_dir.mkdir()
    return data_dir

@pytest.fixture
def mock_database_connection():
    with patch('psycopg2.connect') as mock_conn:
        mock_cursor = Mock()
        mock_conn.return_value.cursor.return_value = mock_cursor
        yield mock_conn
```

#### **Required Migration**
```python
# New: tests/base_test_case.py
import unittest
import tempfile
import shutil
from unittest.mock import Mock, patch

class BaseTestCase(unittest.TestCase):
    """Base class with common configuration for all tests."""
    
    @classmethod
    def setUpClass(cls):
        """Global configuration equivalent to session fixtures."""
        cls.mock_env_config = {
            'OMOP_DB_HOST': 'localhost',
            'OMOP_DB_PORT': '5432',
            'OMOP_DB_NAME': 'test_omop_cdm',
            'OMOP_DB_USERNAME': 'test_omop_user',
            'OMOP_DB_PASSWORD': 'test_password',
            'POSTGRES_ADMIN_USER': 'postgres',
            'POSTGRES_ADMIN_DB': 'postgres',
            'FHIR_SERVER_URL': 'http://localhost:8080/fhir'
        }
        
        cls.sample_fhir_patient = {
            "resourceType": "Patient",
            "id": "test-patient-123",
            "name": [{"family": "Doe", "given": ["John"]}],
            "gender": "male",
            "birthDate": "1990-01-01"
        }
        
        cls.sample_omop_person = {
            "person_id": 1,
            "gender_concept_id": 8507,
            "year_of_birth": 1990,
            # ... rest of fields
        }
    
    def setUp(self):
        """Per-test configuration equivalent to function fixtures."""
        # Temporary directory
        self.temp_data_dir = tempfile.mkdtemp()
        self.addCleanup(shutil.rmtree, self.temp_data_dir)
        
        # Mock database connection
        self.db_patcher = patch('psycopg2.connect')
        self.mock_connect = self.db_patcher.start()
        self.addCleanup(self.db_patcher.stop)
        
        mock_cursor = Mock()
        self.mock_connect.return_value.cursor.return_value = mock_cursor
        self.mock_connect.return_value.autocommit = True

class OMOPTestCase(BaseTestCase):
    """OMOP-specific class for tests with additional configuration."""
    
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.OMOP_CDM_EXPECTED_TABLES = 39
```

#### **⚠️ CRITICAL RISK**: 
- **Complexity**: Current fixtures are elegant and functional
- **Regression**: Changes may introduce bugs in working tests
- **Verbosity**: unittest version will be significantly more verbose

### **CRITICAL 2: Test Functions Refactoring**

#### **Current State**
```python
@pytest.mark.unit
def test_imports_can_be_resolved(database_creator_class, database_status_function):
    """Test that depends on injected fixtures."""
    assert database_creator_class is not None
    assert database_status_function is not None
```

#### **Required Migration**
```python
class TestOMOPDatabaseCreatorStandards(OMOPTestCase):
    """Tests for OMOP database creation."""
    
    def test_imports_can_be_resolved(self):
        """Test that must access configuration from base class."""
        # Import necessary modules
        from create_database import OMOPDatabaseCreator, get_database_status
        
        self.assertIsNotNone(OMOPDatabaseCreator)
        self.assertIsNotNone(get_database_status)
```

#### **⚠️ CRITICAL RISK**:
- **Signatures**: All test functions must change from `test_func(fixtures)` to `self.test_func()`
- **Imports**: Need to reorganize imports that were previously injected via fixtures
- **Naming**: Functions must be converted to class methods

### **CRITICAL 3: Markers Migration**

#### **Current State**
```python
@pytest.mark.unit
@pytest.mark.omop  
def test_something():
    pass
```

#### **Migration Strategy**
```python
# OPTION A: Organization by classes
class UnitTests(OMOPTestCase):
    def test_something(self):
        pass

class IntegrationTests(OMOPTestCase):
    def test_something(self):
        pass

# OPTION B: Conditional decorators
class TestSuite(OMOPTestCase):
    @unittest.skipUnless(RUN_UNIT_TESTS, "Unit tests disabled")
    def test_unit_something(self):
        pass
    
    @unittest.skipUnless(RUN_INTEGRATION_TESTS, "Integration tests disabled") 
    def test_integration_something(self):
        pass
```

#### **⚠️ MEDIUM RISK**:
- **Loss of flexibility**: `pytest -m "unit and not slow"` has no direct equivalent
- **Reorganization**: May require restructuring test organization

### **CRITICAL 4: CI/CD Configuration**

#### **Current State (.github/workflows/)**
```yaml
- name: Run tests
  run: pytest --cov=src/fhir_omop --cov-report=xml

- name: Run unit tests only
  run: pytest -m "unit"
```

#### **Required Migration**
```yaml
- name: Install dependencies
  run: |
    pip install coverage
    pip install -r requirements.txt

- name: Run tests with coverage
  run: |
    coverage run -m unittest discover tests/
    coverage xml

- name: Run unit tests only
  run: |
    # OPTION A: Specific directory
    python -m unittest discover tests/unit/
    
    # OPTION B: Custom script
    python scripts/run_unit_tests.py
```

#### **⚠️ MEDIUM RISK**:
- **CI/CD commands**: Change all existing workflows
- **Categorization**: Less elegant for running test subsets

## **Implementation Plan**

### **Phase 1: Preparation (0.5 days)**
1. **Create branch**: `refactor/migrate-to-unittest`
2. **Backup**: Ensure current branch works 100%
3. **Analysis**: Review all existing tests in detail

### **Phase 2: Configuration Migration (1 day)**
1. **Create** `tests/base_test_case.py` with base classes
2. **Update** `environment.yml` (remove pytest deps)
3. **Delete** `pytest.ini`
4. **Create** scripts for categorized execution if necessary

### **Phase 3: Tests Migration (2 days)**
1. **Migrate** `tests/test_omop_database/test_create_database.py`
   - Convert functions to methods
   - Change dependency injection to self.attributes
   - Update assertions
2. **Migrate** module-specific fixtures
3. **Delete** conftest.py files when possible

### **Phase 4: CI/CD Update (0.5 days)**
1. **Update** all GitHub Actions workflows
2. **Create** auxiliary scripts for categorization if necessary
3. **Verify** that coverage works correctly

### **Phase 5: Testing and Validation (1 day)**
1. **Execute** all migrated tests
2. **Verify** that coverage is equivalent
3. **Confirm** that CI/CD works
4. **Document** changes in README.md

## **Acceptance Criteria**

### **✅ Functionality**
- [ ] All existing tests pass with unittest
- [ ] Coverage reports equivalent metrics
- [ ] CI/CD executes tests correctly
- [ ] Test categorization works (unit, integration, etc.)

### **✅ Quality**
- [ ] Code more readable than pytest version (less "magic")
- [ ] No external dependencies for testing
- [ ] Documentation updated
- [ ] New tests follow unittest pattern

### **✅ Compatibility**
- [ ] abu_dhabi_claims_mvp continues working without changes
- [ ] New developers can work without installing pytest
- [ ] Development scripts updated

## **Risks and Mitigations**

### **🔴 HIGH RISK: Test Regression**
- **Mitigation**: Keep original branch as reference
- **Validation**: Execute complete test suite before and after
- **Rollback**: Plan to revert if critical issues arise

### **🟡 MEDIUM RISK: Functionality Loss**
- **Mitigation**: Document exactly which pytest features we use
- **Alternatives**: Create scripts to replace lost functionality
- **Testing**: Verify that specific workflows continue working

### **🟢 LOW RISK: Development Time**
- **Mitigation**: Conservative estimation (4 days vs 3 days)
- **Buffer**: Plan additional time for debugging
- **Support**: Ensure someone knows both frameworks

## **Dependencies and Prerequisites**

### **MUST be completed BEFORE this migration:**
- ✅ Functional FHIR→OMOP transformation MVP
- ✅ Stable abu_dhabi_claims_mvp tests
- ✅ At least one working E2E pipeline

### **MUST be completed DURING this migration:**
- 📝 Documentation of new testing patterns
- 🔄 Update contribution guides
- 📋 Scripts for categorized test execution

## **Additional Notes**

### **Post-Migration Considerations**
1. **Training**: Team must update knowledge on unittest best practices
2. **Documentation**: Update `docs/guides/development/unit_testing_standards.md`
3. **Templates**: Create templates for new tests following unittest pattern

### **Post-Migration Monitoring**
1. **Performance**: Verify that tests are not significantly slower
2. **Developer Experience**: Confirm that new developers adapt well
3. **Maintenance**: Evaluate if code is easier to maintain

## **Expected Result**

Upon completing this migration:
- ✅ **Zero external dependencies** for testing
- ✅ **Total consistency** in testing framework
- ✅ **Alignment** with team experience
- ✅ **More explicit code** and less "magic"
- ✅ **Solid foundation** for future scalability

---

**Note**: This issue should be implemented only after completing the FHIR→OMOP transformation MVP. It is not critical for project functionality, but improves long-term technical consistency.
