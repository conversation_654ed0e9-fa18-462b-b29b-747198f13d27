#!/bin/bash
# Script to set up the conda environment and run the Streamlit app

# Check if conda is installed
if ! command -v conda &> /dev/null; then
    echo "❌ Error: conda is not installed or not in PATH"
    echo "Please install Minicon<PERSON> or Anaconda and make sure 'conda' is in your PATH"
    exit 1
fi

# Get the directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

# Create or update the conda environment
echo "🔄 Setting up conda environment 'fhir-demo'..."
conda env update -f environment.yml

# Verify that all required packages are installed
echo "🔍 Verifying required packages..."
conda run -n fhir-demo python -c "import streamlit; import pandas; import plotly; print('✅ All required packages are installed successfully')"

# Activate the environment and run the app
echo "🚀 Starting Streamlit application..."
conda run -n fhir-demo streamlit run app.py
