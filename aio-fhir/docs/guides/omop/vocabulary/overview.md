# OMOP Vocabulary Overview

The OMOP Common Data Model (CDM) relies on standardized vocabularies to normalize different coding systems used in healthcare. This document provides an overview of OMOP vocabularies and their role in the FHIR-to-OMOP transformation process.

## Introduction to OMOP Vocabularies

OMOP vocabularies are a set of standardized terminologies that provide a common language for representing healthcare concepts. They enable:

1. **Standardization**: Converting source codes to standard concepts
2. **Normalization**: Representing similar concepts consistently
3. **Hierarchical Relationships**: Navigating concept hierarchies
4. **Cross-Vocabulary Mappings**: Relating concepts across different terminologies

## Vocabulary Tables

The OMOP CDM includes the following vocabulary-related tables:

| Table | Description |
|-------|-------------|
| `CONCEPT` | Contains all concepts across all vocabularies |
| `VOCABULARY` | Contains information about the vocabularies |
| `DOMAIN` | Defines domains for concepts (e.g., Condition, Drug) |
| `CONCEPT_CLASS` | Defines classes for concepts (e.g., Clinical Finding, Ingredient) |
| `CONCEPT_RELATIONSHIP` | Defines relationships between concepts |
| `RELATIONSHIP` | Defines types of relationships |
| `CONCEPT_SYNONYM` | Contains alternative names for concepts |
| `CONCEPT_ANCESTOR` | Contains hierarchical relationships |
| `SOURCE_TO_CONCEPT_MAP` | Maps source codes to standard concepts |
| `DRUG_STRENGTH` | Contains drug strength information |

## Key Vocabulary Concepts

### Standard vs. Source Concepts

- **Standard Concepts**: Preferred concepts for analysis, marked with `standard_concept = 'S'`
- **Source Concepts**: Original concepts from source data, not marked as standard
- **Classification Concepts**: Concepts used for classification, marked with `standard_concept = 'C'`

### Domains

Domains categorize concepts by their type:

- Condition
- Drug
- Procedure
- Measurement
- Observation
- Device
- Visit
- Provider
- etc.

### Concept Relationships

Relationships define how concepts relate to each other:

- **Maps to**: Links source concepts to standard concepts
- **Is a**: Defines hierarchical relationships
- **Subsumes**: Indicates broader concepts
- **Has ingredient**: Links drug products to ingredients
- **etc.**

## Core Vocabularies

The OMOP CDM uses several core vocabularies:

| Vocabulary | Description | Domain |
|------------|-------------|--------|
| SNOMED | Systematized Nomenclature of Medicine | Conditions, Procedures, Observations |
| RxNorm | Normalized drug names | Drugs |
| LOINC | Logical Observation Identifiers Names and Codes | Measurements, Observations |
| ICD-10-CM | International Classification of Diseases, 10th Revision, Clinical Modification | Conditions |
| ICD-10-PCS | International Classification of Diseases, 10th Revision, Procedure Coding System | Procedures |
| CPT-4 | Current Procedural Terminology, 4th Edition | Procedures |
| HCPCS | Healthcare Common Procedure Coding System | Procedures |
| NDC | National Drug Code | Drugs |
| ATC | Anatomical Therapeutic Chemical Classification | Drugs |

## Vocabulary in FHIR-to-OMOP Transformation

When transforming FHIR resources to OMOP CDM, vocabularies play a crucial role:

1. **Code System Mapping**: Mapping FHIR code systems to OMOP vocabularies
2. **Concept Lookup**: Finding OMOP concepts for FHIR codes
3. **Standard Concept Mapping**: Converting source concepts to standard concepts
4. **Domain Assignment**: Determining the appropriate domain for concepts
5. **Relationship Navigation**: Using concept relationships for mapping

## Vocabulary Service

The OMOP module includes a Vocabulary Service that provides:

- **Concept Lookup**: Finding concepts by code, name, or ID
- **Concept Mapping**: Mapping source concepts to standard concepts
- **Relationship Navigation**: Navigating concept hierarchies and relationships
- **Caching**: Optimizing performance for frequently used concepts

## Obtaining OMOP Vocabularies

OMOP vocabularies can be obtained from:

1. **Athena**: OHDSI's vocabulary repository (https://athena.ohdsi.org/)
2. **OHDSI Vocabulary Library**: Pre-packaged vocabularies for OHDSI tools
3. **Custom Extracts**: Subsets of vocabularies for specific use cases

For detailed instructions on obtaining and setting up vocabularies, see [Athena Setup](athena_setup.md).

## Loading Vocabularies

Vocabularies must be loaded into the OMOP CDM database before use. This involves:

1. **Downloading**: Obtaining vocabulary files from Athena
2. **Extraction**: Extracting the downloaded files
3. **Loading**: Loading the vocabulary files into the database
4. **Indexing**: Creating indexes for efficient querying

For detailed instructions on loading vocabularies, see [Vocabulary Loading](loading.md).

## Best Practices

When working with OMOP vocabularies:

1. **Use Standard Concepts**: Always map to standard concepts for analysis
2. **Preserve Source Concepts**: Store original source concepts for traceability
3. **Consider Domain Consistency**: Ensure concepts are used in appropriate domains
4. **Use Concept Relationships**: Leverage relationships for mapping and navigation
5. **Implement Caching**: Cache frequently used concepts for performance
6. **Keep Vocabularies Updated**: Regularly update vocabularies to stay current

## References

1. [OHDSI Vocabulary Resources](https://www.ohdsi.org/web/wiki/doku.php?id=documentation:vocabulary:sidebar)
2. [The Book of OHDSI: Chapter 4 - The Standardized Vocabularies](https://ohdsi.github.io/TheBookOfOhdsi/StandardizedVocabularies.html)
3. [OMOP CDM Vocabulary Documentation](https://ohdsi.github.io/CommonDataModel/vocabulary.html)
4. [Athena - OHDSI Vocabulary Repository](https://athena.ohdsi.org/)
