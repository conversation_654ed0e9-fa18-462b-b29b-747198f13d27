# FHIR Query Guide: Building Effective FHIR Queries

This guide provides comprehensive information on building effective FHIR queries for the FHIR-to-OMOP project. It documents the syntax, structure, and best practices for constructing FHIR queries based on our implementation experience.

## Table of Contents

1. [Introduction](#introduction)
2. [FHIR Query Basics](#fhir-query-basics)
3. [Query Parameters](#query-parameters)
4. [Common Query Patterns](#common-query-patterns)
5. [Resource-Specific Queries](#resource-specific-queries)
6. [Advanced Query Techniques](#advanced-query-techniques)
7. [Troubleshooting](#troubleshooting)
8. [Implementation in Our Application](#implementation-in-our-application)
9. [References](#references)

## Introduction

FHIR (Fast Healthcare Interoperability Resources) provides a powerful REST API for querying healthcare data. This guide documents our learnings and best practices for constructing effective FHIR queries, with a focus on how they're implemented in our application.

## FHIR Query Basics

### Query Structure

FHIR queries follow a RESTful pattern with the following structure:

```
[base]/[resource]?[parameter1]=[value1]&[parameter2]=[value2]...
```

Where:
- `[base]` is the base URL of the FHIR server (e.g., `https://fhir.aiotek.ai/fhir`)
- `[resource]` is the FHIR resource type (e.g., `Patient`, `Observation`)
- `[parameter]=[value]` are the search parameters and their values

### Example Queries

Simple query for all patients:
```
GET https://fhir.aiotek.ai/fhir/Patient
```

Query for observations of a specific type:
```
GET https://fhir.aiotek.ai/fhir/Observation?code=http://loinc.org|8302-2
```

## Query Parameters

### Common Parameters

| Parameter | Description | Example |
|-----------|-------------|---------|
| `_count` | Maximum number of results to return | `_count=50` |
| `_sort` | Sort order for results | `_sort=date` or `_sort=-date` (descending) |
| `_include` | Include referenced resources | `_include=Observation:patient` |
| `_revinclude` | Include resources that reference this one | `_revinclude=Provenance:target` |

### Comparison Operators

FHIR supports various comparison operators for parameters:

| Operator | Description | Example |
|----------|-------------|---------|
| `eq` | Equal to | `date=eq2021-01-01` |
| `ne` | Not equal to | `status:ne=cancelled` |
| `gt` | Greater than | `date=gt2021-01-01` |
| `lt` | Less than | `date=lt2021-01-01` |
| `ge` | Greater than or equal to | `date=ge2021-01-01` |
| `le` | Less than or equal to | `date=le2021-01-01` |

### Modifiers

Modifiers change how a parameter is interpreted:

| Modifier | Description | Example |
|----------|-------------|---------|
| `:exact` | Exact match | `name:exact=Smith` |
| `:contains` | Contains the value | `name:contains=Smi` |
| `:missing` | Whether value is missing | `name:missing=true` |
| `:text` | Text search | `code:text=heart` |

## Common Query Patterns

### Filtering by Date Range

To filter resources by a date range:

```
GET https://fhir.aiotek.ai/fhir/Observation?date=ge2021-01-01&date=le2021-12-31
```

### Searching for Multiple Values

To search for resources matching any of several values:

```
GET https://fhir.aiotek.ai/fhir/Condition?code=http://snomed.info/sct|44054006,http://snomed.info/sct|46635009
```

### Including Related Resources

To include patient information with observations:

```
GET https://fhir.aiotek.ai/fhir/Observation?_include=Observation:patient
```

## Resource-Specific Queries

### Patient Queries

Common parameters for Patient resources:

| Parameter | Description | Example |
|-----------|-------------|---------|
| `name` | Patient name | `name=Smith` |
| `family` | Family name | `family=Smith` |
| `given` | Given name | `given=John` |
| `identifier` | Patient identifier | `identifier=12345` |
| `gender` | Patient gender | `gender=male` |
| `birthdate` | Birth date | `birthdate=1970-01-01` |

### Observation Queries

Common parameters for Observation resources:

| Parameter | Description | Example |
|-----------|-------------|---------|
| `code` | Observation code | `code=http://loinc.org|8302-2` |
| `category` | Observation category | `category=vital-signs` |
| `date` | Observation date | `date=ge2021-01-01` |
| `subject` | Subject (patient) reference | `subject=Patient/123` |
| `value-quantity` | Numeric value | `value-quantity=gt100` |

### Condition Queries

Common parameters for Condition resources:

| Parameter | Description | Example |
|-----------|-------------|---------|
| `code` | Condition code | `code=http://snomed.info/sct|44054006` |
| `clinical-status` | Clinical status | `clinical-status=active` |
| `onset-date` | Onset date | `onset-date=ge2021-01-01` |
| `subject` | Subject (patient) reference | `subject=Patient/123` |

## Advanced Query Techniques

### Chained Parameters

Chained parameters allow searching through referenced resources:

```
GET https://fhir.aiotek.ai/fhir/Observation?subject:Patient.name=Smith
```

This searches for observations where the referenced patient has the name "Smith".

### Composite Parameters

Composite parameters combine multiple parameters:

```
GET https://fhir.aiotek.ai/fhir/Observation?component-code-value-quantity=http://loinc.org|8480-6$gt120
```

This searches for observations with a component code of 8480-6 (systolic blood pressure) and a value greater than 120.

## Troubleshooting

### Common Issues and Solutions

| Issue | Possible Cause | Solution |
|-------|---------------|----------|
| No results returned | Incorrect parameter format | Ensure parameters follow FHIR syntax (e.g., `vital-signs` not `vital signs`) |
| Error with `_include` | Incorrect reference format | Use resource-specific format (e.g., `Observation:patient` not `*.patient`) |
| Error with `_sort` for Condition | Invalid sort parameter | Use `onset-date` or `recorded-date` instead of `date` for Condition resources |
| No results for Encounter class filter | Different data structure | Try filtering by `type` instead of `class` for Encounter resources |
| Too many results | Missing filters | Add appropriate filters to narrow results |
| Server error | Complex query | Simplify query or increase server timeout |

## Implementation in Our Application

In our application, FHIR queries are constructed in the `data_exploration.py` file. Here's how we build queries:

1. We collect user inputs through the UI
2. We transform these inputs into FHIR query parameters
3. We construct the full query URL
4. We execute the query and process the results

### Key Implementation Details

- Category values are normalized with hyphens: `selected_category.lower().replace(" ", "-")`
- Resource-specific `_include` parameters are used: `filters["_include"] = "Observation:patient"`
- Resource-specific sort parameters are used: `filters["_sort"] = "-onset-date"` for Condition resources
- Date ranges are properly formatted: `filters["date"] = f"ge{date_from}&date=le{date_to}"`
- Alternative filtering approaches for non-standard resources: Using `type` instead of `class` for Encounter resources

## References

1. [FHIR Search Documentation](https://www.hl7.org/fhir/search.html)
2. [HAPI FHIR Search Parameters](https://hapifhir.io/hapi-fhir/docs/server_jpa/search.html)
3. [FHIR RESTful API](https://www.hl7.org/fhir/http.html)
