"""
Data Loading UI Components for the FHIR Server Demo Streamlit application.

This module provides UI components for the data loading tab of the Streamlit application.
"""

import streamlit as st
import pandas as pd
import logging
import sys
from pathlib import Path

# Add the parent directory to the Python path to import utils
sys.path.append(str(Path(__file__).parent.parent))

# Import utility functions
from utils import (
    get_available_datasets,
    load_data_transaction_bundles,
    load_data_selective
)

# Create a logger for this module
logger = logging.getLogger(__name__)

def render_data_loading_tab():
    """
    Render the Data Loading tab in the Streamlit application.

    This function handles the UI for selecting and loading datasets to the FHIR server.
    """
    st.header("Data Loading")

    # Get available datasets
    datasets = get_available_datasets()
    dataset_names = [ds["name"] for ds in datasets]

    if not datasets or all(ds["type"] == "none" for ds in datasets):
        st.warning("⚠️ No datasets found. Please check the data directory.")
    else:
        # Create a clean, streamlined interface
        col1, col2 = st.columns([2, 1])

        with col1:
            # 1. Dataset selection
            selected_dataset = st.selectbox(
                "1. Dataset:",
                dataset_names
            )
            dataset_path = next((ds["path"] for ds in datasets if ds["name"] == selected_dataset), None)

            # 2. Loading method - simplified labels with tooltip help
            load_method = st.radio(
                "2. Method:",
                ["Fast Loading", "Safe Loading"],
                help="Fast: Transaction bundles (faster, requires permissive mode). Safe: Selective loading with dependency resolution."
            )

        with col2:
            # 3. Configuration - compact layout
            st.markdown("##### 3. Configuration:")

            # Batch size in compact form
            batch_size = st.select_slider(
                "Batch size:",
                options=[100, 200, 300, 400, 500, 600, 700, 800, 900, 1000],
                value=500,
                format_func=lambda x: f"{x}"
            )

            # Use existing bundles option
            use_existing_bundles = st.checkbox(
                "Use existing bundles",
                help="Skip NDJSON conversion step"
            )

        # 4. Load button - full width and prominent
        st.markdown("##### 4. Execute:")
        load_button_col1, load_button_col2 = st.columns([5, 1])

        with load_button_col1:
            load_button = st.button("▶️ Load Data", type="primary", use_container_width=True)

        with load_button_col2:
            if st.button("ℹ️", help="View process details"):
                with st.expander("Process Details", expanded=True):
                    if load_method == "Fast Loading":
                        st.info("**Fast Loading**: Converts NDJSON files to transaction bundles and sends them directly to the server.")
                    else:
                        st.info("**Safe Loading**: Analyzes references between resources and loads only those with satisfied dependencies.")

                    st.markdown("""
                    **Server Requirements:**
                    - Validation disabled
                    - Referential integrity checks disabled
                    - Auto-creation of placeholder references enabled
                    """)

        # Process the load button click
        if load_button and dataset_path:
            _handle_data_loading(dataset_path, selected_dataset, load_method, batch_size, use_existing_bundles)
        elif load_button:
            # Show error if dataset path not found
            logger.error(f"Could not find path for dataset: {selected_dataset}")
            st.error("❌ Could not find the path for the selected dataset")

def _handle_data_loading(dataset_path, selected_dataset, load_method, batch_size, use_existing_bundles):
    """
    Handle the data loading process based on user selections.

    Args:
        dataset_path (str): Path to the selected dataset
        selected_dataset (str): Name of the selected dataset
        load_method (str): Loading method (Fast Loading or Safe Loading)
        batch_size (int): Batch size for loading
        use_existing_bundles (bool): Whether to use existing bundles
    """
    logger.info(f"Loading data from {selected_dataset} using method: {load_method}, batch size: {batch_size}, use_existing_bundles: {use_existing_bundles}")

    # Create placeholders for UI updates
    progress_placeholder = st.empty()
    error_placeholder = st.empty()
    success_placeholder = st.empty()
    stats_placeholder = st.empty()
    details_placeholder = st.empty()

    progress_placeholder.info("⏳ Preparing to load data...")

    with st.spinner(f"Loading data from {selected_dataset}..."):
        # Determine which loading method to use based on simplified UI selection
        if load_method == "Fast Loading":
            logger.info(f"Using Transaction Bundles method with path: {dataset_path}, server: {st.session_state.server_url}")

            # Update progress
            if not use_existing_bundles:
                progress_placeholder.info("⏳ Step 1/2: Converting NDJSON files to transaction bundles...")
            else:
                progress_placeholder.info("⏳ Using existing bundles, skipping conversion step...")

            success, result = load_data_transaction_bundles(
                dataset_path,
                st.session_state.server_url,
                batch_size,
                use_existing_bundles
            )

            # Update progress for step 2
            if success and not use_existing_bundles:
                progress_placeholder.info("⏳ Step 2/2: Loading bundles to FHIR server...")
        else:  # Safe Loading
            logger.info(f"Using Selective Loader method with path: {dataset_path}, server: {st.session_state.server_url}")
            progress_placeholder.info("⏳ Analyzing and loading resources with dependency resolution...")
            success, result = load_data_selective(dataset_path, st.session_state.server_url, batch_size)

        # Clear the progress placeholder
        progress_placeholder.empty()

        # Display results based on success or failure
        _display_loading_results(success, result, error_placeholder, success_placeholder)

def _display_loading_results(success, result, error_placeholder, success_placeholder):
    """
    Display the results of the data loading process.

    Args:
        success (bool): Whether the loading was successful
        result (dict): Result data from the loading process
        error_placeholder (streamlit.empty): Placeholder for error messages
        success_placeholder (streamlit.empty): Placeholder for success messages
    """
    # Function to display report data
    def display_report_data(report):
        # Initialize report sections if they don't exist
        if "resource_breakdown" not in report:
            report["resource_breakdown"] = []
        if "overall_stats" not in report:
            report["overall_stats"] = {}
        if "performance_metrics" not in report:
            report["performance_metrics"] = {}
        if "system_resources" not in report:
            report["system_resources"] = {}
        if "failed_bundles" not in report:
            report["failed_bundles"] = []
        if "top_resources" not in report:
            report["top_resources"] = []
        # Create tabs for different report sections - simplified to most important ones
        report_tabs = st.tabs([
            "Resources",
            "Performance",
            "System Usage",
            "Failed Items",
            "Top Resources"
        ])

        # Tab 1: Resource Breakdown
        with report_tabs[0]:
            st.markdown("##### Resource Type Breakdown")
            if report["resource_breakdown"]:
                # Create DataFrame for resource breakdown
                breakdown_data = []
                for item in report["resource_breakdown"]:
                    # Extract success rate and format it nicely
                    success_rate = item.get("success_rate", "N/A")

                    # Extract resources count and format it
                    resources_str = item.get("resources", "0")
                    resources_display = resources_str

                    # Extract bundles count if available
                    bundles_str = item.get("bundles", "N/A")

                    # Extract processing time if available
                    time_str = item.get("time", "N/A")

                    breakdown_data.append({
                        "Resource Type": item["resource_type"],
                        "Bundles": bundles_str,
                        "Resources": resources_display,
                        "Time": time_str,
                        "Success Rate": success_rate
                    })

                if breakdown_data:
                    # Sort by resource count (descending)
                    try:
                        breakdown_data = sorted(
                            breakdown_data,
                            key=lambda x: int(x["Resources"].split("/")[0]) if "/" in x["Resources"] else int(x["Resources"]) if x["Resources"].isdigit() else 0,
                            reverse=True
                        )
                    except Exception:
                        # If sorting fails, just use the original order
                        pass

                    breakdown_df = pd.DataFrame(breakdown_data)
                    st.dataframe(breakdown_df, use_container_width=True)

                    # Create a bar chart for resource counts
                    try:
                        import plotly.express as px

                        # Extract resource counts
                        chart_data = []
                        for item in breakdown_data:
                            resources_str = item["Resources"]
                            if "/" in resources_str:
                                resources = int(resources_str.split("/")[0])
                            else:
                                try:
                                    resources = int(resources_str) if resources_str.isdigit() else 0
                                except:
                                    resources = 0

                            chart_data.append({
                                "Resource Type": item["Resource Type"],
                                "Count": resources,
                                "Success Rate": item["Success Rate"]
                            })

                        chart_df = pd.DataFrame(chart_data)

                        # Create a bar chart with success rate as color
                        fig = px.bar(
                            chart_df,
                            x="Resource Type",
                            y="Count",
                            color="Success Rate",
                            color_continuous_scale=["red", "yellow", "green"],
                            labels={"Count": "Number of Resources", "Resource Type": "Resource Type"},
                            title="Resource Distribution by Type"
                        )

                        # Improve layout
                        fig.update_layout(
                            xaxis_title="Resource Type",
                            yaxis_title="Number of Resources",
                            legend_title="Success Rate",
                            font=dict(size=12),
                            margin=dict(l=40, r=40, t=40, b=40)
                        )

                        st.plotly_chart(fig, use_container_width=True)
                    except Exception as e:
                        st.warning(f"Could not create chart: {str(e)}")
            else:
                st.info("No resource breakdown data available")

        # Tab 2: Performance Metrics
        with report_tabs[1]:
            st.markdown("##### Performance Metrics")

            # Create two columns for better organization
            col1, col2 = st.columns(2)

            # Column 1: Overall Statistics
            with col1:
                st.markdown("**Overall Statistics**")

                # Extract key metrics from overall stats
                overall_stats = {}
                if report["overall_stats"]:
                    for key, value in report["overall_stats"].items():
                        # Skip empty or header entries
                        if value and not key.isupper() and not key.startswith("==="):
                            overall_stats[key] = value

                # Display key metrics in a more visual way
                if overall_stats:
                    # Total Processing Time
                    if "Total Processing Time" in overall_stats:
                        st.metric("Total Processing Time", overall_stats["Total Processing Time"])

                    # Total Bundles Processed
                    if "Total Bundles Processed" in overall_stats:
                        st.metric("Bundles Processed", overall_stats["Total Bundles Processed"])

                    # Total Resources Processed
                    if "Total Resources Processed" in overall_stats:
                        st.metric("Resources Processed", overall_stats["Total Resources Processed"])

                    # Average Time Per Bundle
                    if "Average Time Per Bundle" in overall_stats:
                        st.metric("Avg Time Per Bundle", overall_stats["Average Time Per Bundle"])
                else:
                    st.info("No overall statistics available")

            # Column 2: Performance Metrics
            with col2:
                st.markdown("**Performance Metrics**")

                # Extract key metrics from performance metrics
                perf_metrics = {}
                if report["performance_metrics"]:
                    for key, value in report["performance_metrics"].items():
                        # Skip empty or header entries
                        if value and not key.isupper() and not key.startswith("==="):
                            perf_metrics[key] = value

                # Display key metrics in a more visual way
                if perf_metrics:
                    # Resources Per Second
                    if "Resources Per Second" in perf_metrics:
                        st.metric("Resources Per Second", perf_metrics["Resources Per Second"])

                    # Bundles Per Second
                    if "Bundles Per Second" in perf_metrics:
                        st.metric("Bundles Per Second", perf_metrics["Bundles Per Second"])

                    # Average Resource Size
                    if "Average Resource Size" in perf_metrics:
                        st.metric("Avg Resource Size", perf_metrics["Average Resource Size"])
                else:
                    st.info("No performance metrics available")

            # Display all metrics in a table for reference
            st.markdown("**All Metrics**")

            # Combine overall stats and performance metrics
            metrics_data = []

            if report["overall_stats"]:
                for key, value in report["overall_stats"].items():
                    # Skip empty or header entries
                    if value and not key.isupper() and not key.startswith("==="):
                        metrics_data.append({
                            "Category": "Overall Statistics",
                            "Metric": key,
                            "Value": value
                        })

            if report["performance_metrics"]:
                for key, value in report["performance_metrics"].items():
                    # Skip empty or header entries
                    if value and not key.isupper() and not key.startswith("==="):
                        metrics_data.append({
                            "Category": "Performance Metrics",
                            "Metric": key,
                            "Value": value
                        })

            if metrics_data:
                metrics_df = pd.DataFrame(metrics_data)
                st.dataframe(metrics_df, use_container_width=True)
            else:
                st.info("No performance metrics available")

        # Tab 3: System Resources
        with report_tabs[2]:
            st.markdown("##### System Resource Usage")
            if report["system_resources"]:
                # Extract system resource data
                sys_resources = {}
                for key, value in report["system_resources"].items():
                    # Skip empty or header entries
                    if value and not key.isupper() and not key.startswith("==="):
                        sys_resources[key] = value

                # Display system information in a more organized way
                if sys_resources:
                    # Create three columns for system info
                    col1, col2, col3 = st.columns(3)

                    # Column 1: CPU Information
                    with col1:
                        st.markdown("**CPU Information**")
                        if "CPU Cores" in sys_resources:
                            st.info(f"**Cores:** {sys_resources['CPU Cores']}")
                        if "Average CPU Usage" in sys_resources:
                            st.info(f"**Avg Usage:** {sys_resources['Average CPU Usage']}")

                    # Column 2: Memory Information
                    with col2:
                        st.markdown("**Memory Information**")
                        if "Total System Memory" in sys_resources:
                            st.info(f"**Total Memory:** {sys_resources['Total System Memory']}")
                        if "Average Memory Usage" in sys_resources:
                            st.info(f"**Avg Usage:** {sys_resources['Average Memory Usage']}")

                    # Column 3: Peak Usage
                    with col3:
                        st.markdown("**Peak Usage**")
                        if "Peak CPU Usage" in sys_resources:
                            st.info(f"**Peak CPU:** {sys_resources['Peak CPU Usage']}")
                        if "Peak Memory Usage" in sys_resources:
                            st.info(f"**Peak Memory:** {sys_resources['Peak Memory Usage']}")

                # Create gauge charts for CPU and Memory usage
                try:
                    import plotly.graph_objects as go
                    from plotly.subplots import make_subplots

                    # Extract CPU and Memory usage
                    cpu_usage = None
                    memory_usage = None
                    avg_cpu_usage = None
                    avg_memory_usage = None

                    for key, value in sys_resources.items():
                        if "Peak CPU Usage" in key:
                            try:
                                cpu_usage = float(value.split("%")[0])
                            except:
                                pass
                        elif "Peak Memory Usage" in key:
                            try:
                                memory_parts = value.split("(")
                                if len(memory_parts) > 1:
                                    memory_pct = memory_parts[1].split("%")[0]
                                    memory_usage = float(memory_pct)
                            except:
                                pass
                        elif "Average CPU Usage" in key:
                            try:
                                avg_cpu_usage = float(value.split("%")[0])
                            except:
                                pass
                        elif "Average Memory Usage" in key:
                            try:
                                # Try to extract percentage if available
                                if "(" in value:
                                    memory_parts = value.split("(")
                                    if len(memory_parts) > 1:
                                        memory_pct = memory_parts[1].split("%")[0]
                                        avg_memory_usage = float(memory_pct)
                            except:
                                pass

                    # Create a single row with all gauges
                    st.markdown("##### Resource Usage Gauges")

                    # Create a unified figure with all gauges in a single row
                    if cpu_usage is not None or memory_usage is not None:
                        # Determine how many gauges we need
                        num_gauges = 0
                        gauge_titles = []
                        gauge_values = []
                        gauge_colors = []

                        if cpu_usage is not None:
                            num_gauges += 1
                            gauge_titles.append("Peak CPU Usage")
                            gauge_values.append(cpu_usage)
                            gauge_colors.append("darkblue")

                        if avg_cpu_usage is not None:
                            num_gauges += 1
                            gauge_titles.append("Average CPU Usage")
                            gauge_values.append(avg_cpu_usage)
                            gauge_colors.append("royalblue")

                        if memory_usage is not None:
                            num_gauges += 1
                            gauge_titles.append("Peak Memory Usage")
                            gauge_values.append(memory_usage)
                            gauge_colors.append("darkblue")

                        if avg_memory_usage is not None:
                            num_gauges += 1
                            gauge_titles.append("Average Memory Usage")
                            gauge_values.append(avg_memory_usage)
                            gauge_colors.append("royalblue")

                        # Create a subplot with all gauges in a single row
                        specs = [[{"type": "indicator"} for _ in range(num_gauges)]]
                        fig = make_subplots(
                            rows=1,
                            cols=num_gauges,
                            specs=specs,
                            column_widths=[1/num_gauges] * num_gauges,
                            subplot_titles=gauge_titles
                        )

                        # Add all gauges to the figure
                        for i in range(num_gauges):
                            fig.add_trace(
                                go.Indicator(
                                    mode="gauge+number",
                                    value=gauge_values[i],
                                    number={"suffix": "%", "font": {"size": 24}},
                                    gauge={
                                        "axis": {"range": [0, 100], "tickwidth": 1},
                                        "bar": {"color": gauge_colors[i]},
                                        "steps": [
                                            {"range": [0, 50], "color": "lightgreen"},
                                            {"range": [50, 75], "color": "yellow"},
                                            {"range": [75, 100], "color": "red"}
                                        ],
                                        "threshold": {
                                            "line": {"color": "red", "width": 4},
                                            "thickness": 0.75,
                                            "value": 90
                                        }
                                    }
                                ),
                                row=1, col=i+1
                            )

                        # Update layout to ensure consistent size and appearance
                        fig.update_layout(
                            height=300,  # Increased height for better visibility
                            margin=dict(l=20, r=20, t=50, b=20),
                            grid={"rows": 1, "columns": num_gauges, "pattern": "independent"},
                            template="plotly_dark"
                        )

                        # Ensure all gauges have the same size and shape
                        fig.update_traces(
                            domain={"row": 0, "column": 0},
                            gauge={
                                "axis": {"range": [0, 100], "tickwidth": 1, "tickcolor": "white"},
                                "bar": {"color": "darkblue"},
                                "bgcolor": "rgba(0,0,0,0)",
                                "borderwidth": 2,
                                "bordercolor": "gray",
                                "steps": [
                                    {"range": [0, 50], "color": "green"},
                                    {"range": [50, 75], "color": "yellow"},
                                    {"range": [75, 100], "color": "red"}
                                ],
                                "threshold": {
                                    "line": {"color": "red", "width": 4},
                                    "thickness": 0.75,
                                    "value": 90
                                }
                            }
                        )

                        # Display the unified figure
                        st.plotly_chart(fig, use_container_width=True)

                    # Display all system resources in a table
                    st.markdown("**All System Resources**")
                    sys_data = []
                    for key, value in sys_resources.items():
                        sys_data.append({
                            "Resource": key,
                            "Value": value
                        })

                    sys_df = pd.DataFrame(sys_data)
                    st.dataframe(sys_df, use_container_width=True)

                except Exception as e:
                    st.warning(f"Could not create resource usage charts: {str(e)}")

                    # Fallback to simple table if charts fail
                    sys_data = []
                    for key, value in sys_resources.items():
                        sys_data.append({
                            "Resource": key,
                            "Value": value
                        })

                    sys_df = pd.DataFrame(sys_data)
                    st.dataframe(sys_df, use_container_width=True)
            else:
                st.info("No system resource data available")

        # Tab 4: Failed Items
        with report_tabs[3]:
            st.markdown("##### Failed Bundles")

            # Check for failed bundles in both the report and the result
            failed_bundles = []

            # First check if we have failed bundles in the report
            if report["failed_bundles"]:
                failed_bundles.extend(report["failed_bundles"])

            # Check if we have failed bundles in the result
            if "failed_bundles" in result and result["failed_bundles"]:
                # Add any failed bundles from the result that aren't already in the list
                for item in result["failed_bundles"]:
                    if not any(b.get("file_name") == item.get("file_name") for b in failed_bundles):
                        failed_bundles.append(item)

            # Check if we have partial success information
            is_partial_success = False
            if "partial_success" in result and result["partial_success"]:
                is_partial_success = True
            elif "summary" in result and "partial_success" in result["summary"] and result["summary"]["partial_success"]:
                is_partial_success = True
            elif "stats" in result and "results" in result["stats"] and "failed" in result["stats"]["results"] and not "0 failed" in result["stats"]["results"]:
                is_partial_success = True

            # Check overall stats for failed bundles information
            failed_bundles_count = 0
            if report["overall_stats"]:
                for key, value in report["overall_stats"].items():
                    if "Failed Bundles" in key:
                        try:
                            failed_bundles_count = int(value)
                        except:
                            pass

            # If we have failed bundles or partial success, show them
            if failed_bundles:
                # Create a clean table for failed bundles
                failed_data = []
                for item in failed_bundles:
                    # Extract resources count if available
                    resources = item.get("resources", "N/A")

                    failed_data.append({
                        "Type": item.get("resource_type", "Unknown"),
                        "File": item.get("file_name", "Unknown"),
                        "Resources": resources,
                        "Error": item.get("error", "Unknown error")
                    })

                # Create a summary card
                st.error(f"**{len(failed_bundles)} bundle(s) failed to load**")

                # Display the failed bundles in a table
                failed_df = pd.DataFrame(failed_data)
                st.dataframe(failed_df, use_container_width=True)

                # Show error details in an expander
                if any("error" in item and item["error"] for item in failed_bundles):
                    with st.expander("Detailed Error Information", expanded=False):
                        for i, item in enumerate(failed_bundles):
                            if "error" in item and item["error"]:
                                st.markdown(f"**Error {i+1}:** {item.get('file_name', 'Unknown file')}")
                                st.code(item["error"])
                                st.markdown("---")
            elif is_partial_success or failed_bundles_count > 0:
                # If we have partial success but no specific failed bundles, show a warning
                st.warning(f"**{failed_bundles_count if failed_bundles_count > 0 else 'Some'} bundle(s) failed but detailed information is not available.**")

                # Try to extract error information from the result
                if "error" in result:
                    st.error(f"**Error:** {result['error']}")

                # Check if we have any error information in the output
                if "output" in result:
                    # Look for error messages in the output
                    error_lines = []
                    for line in result["output"].split("\n"):
                        if "ERROR" in line or "Error" in line or "error" in line:
                            error_lines.append(line)

                    if error_lines:
                        with st.expander("Error details from logs", expanded=False):
                            for line in error_lines:
                                st.text(line)

                    # Try to extract the FAILED BUNDLES section from the output
                    if "FAILED BUNDLES:" in result["output"]:
                        with st.expander("Failed Bundles Section from Report", expanded=True):
                            # Extract the section between FAILED BUNDLES: and the next section
                            output_lines = result["output"].split("\n")
                            failed_section = []
                            in_failed_section = False

                            for line in output_lines:
                                if "FAILED BUNDLES:" in line:
                                    in_failed_section = True
                                    failed_section.append(line)
                                elif in_failed_section and "====" in line:
                                    in_failed_section = False
                                    failed_section.append(line)
                                    break
                                elif in_failed_section:
                                    failed_section.append(line)

                            if failed_section:
                                st.code("\n".join(failed_section))
            else:
                st.success("✅ No failed bundles! All bundles processed successfully.")

        # Tab 5: Top Resources
        with report_tabs[4]:
            st.markdown("##### Top Resource Types by Volume")

            if report["top_resources"]:
                # Create a DataFrame for top resources
                top_data = []
                for item in report["top_resources"]:
                    rank = item.get("rank", "")
                    resource_type = item.get("resource_type", "Unknown")
                    count_info = item.get("count_info", "")

                    # Try to extract the count and percentage
                    count = ""
                    percentage = ""
                    if count_info:
                        # Format: "9878 resources (52.1% of total)"
                        parts = count_info.split("resources")
                        if len(parts) > 0:
                            count = parts[0].strip()

                        # Extract percentage
                        if "(" in count_info and "%" in count_info:
                            pct_part = count_info.split("(")[1].split(")")[0]
                            percentage = pct_part

                    top_data.append({
                        "Rank": rank,
                        "Resource Type": resource_type,
                        "Count": count,
                        "Percentage": percentage
                    })

                # Display as a table
                top_df = pd.DataFrame(top_data)
                st.dataframe(top_df, use_container_width=True)

                # Create a pie chart of the top resources
                try:
                    import plotly.express as px

                    # Extract data for the pie chart
                    pie_data = []
                    for item in top_data:
                        # Extract count as integer
                        count_str = item["Count"]
                        try:
                            count = int(count_str)
                        except:
                            count = 0

                        # Extract percentage as float
                        pct_str = item["Percentage"]
                        try:
                            if "%" in pct_str:
                                pct = float(pct_str.split("%")[0])
                            else:
                                pct = 0
                        except:
                            pct = 0

                        pie_data.append({
                            "Resource Type": item["Resource Type"],
                            "Count": count,
                            "Percentage": pct
                        })

                    # Create the pie chart
                    if pie_data:
                        fig = px.pie(
                            pie_data,
                            values="Count",
                            names="Resource Type",
                            title="Resource Distribution",
                            hover_data=["Percentage"],
                            labels={"Count": "Number of Resources"}
                        )

                        # Improve layout
                        fig.update_traces(
                            textposition='inside',
                            textinfo='percent+label',
                            marker=dict(line=dict(color='#FFFFFF', width=2))
                        )

                        fig.update_layout(
                            uniformtext_minsize=12,
                            uniformtext_mode='hide',
                            margin=dict(l=20, r=20, t=30, b=20)
                        )

                        st.plotly_chart(fig, use_container_width=True)
                except Exception as e:
                    st.warning(f"Could not create pie chart: {str(e)}")
            else:
                st.info("No top resources data available")

    # Handle successful or partially successful data loading
    if success:
        # Check if it's a partial success
        is_partial_success = "partial_success" in result and result["partial_success"]

        if is_partial_success:
            logger.info("Data loaded with partial success")
            success_placeholder.warning("⚠️ Data loaded with partial success - some bundles failed")

            # Show warning about partial success
            if "error" in result:
                error_md = f"⚠️ **Partial Success:** {result['error']}\n\n"
                if "suggestion" in result:
                    error_md += f"**Suggestion:** {result['suggestion']}\n\n"
                error_placeholder.warning(error_md)
        else:
            logger.info("Data loaded successfully")
            success_placeholder.success("✅ Data loaded successfully")

        # Create a consolidated report from all available data sources
        consolidated_report = {
            "resource_breakdown": [],
            "overall_stats": {},
            "performance_metrics": {},
            "system_resources": {},
            "failed_bundles": [],
            "top_resources": []
        }

        # Extract data from the output if available
        if "output" in result:
            # Create a temporary report to extract data from the output
            temp_report = {
                "resource_breakdown": [],
                "overall_stats": {},
                "performance_metrics": {},
                "system_resources": {},
                "failed_bundles": [],
                "top_resources": []
            }

            # Extract data from the output
            output_lines = result["output"].split("\n")

            # Extract resource breakdown
            if "RESOURCE TYPE BREAKDOWN:" in result["output"]:
                resource_breakdown = []
                in_breakdown_section = False
                header_line = None

                for line in output_lines:
                    if "RESOURCE TYPE BREAKDOWN:" in line:
                        in_breakdown_section = True
                        continue
                    elif in_breakdown_section and "OVERALL STATISTICS:" in line:
                        in_breakdown_section = False
                        break
                    elif in_breakdown_section:
                        # Skip header separator lines
                        if "---" in line or "===" in line:
                            continue

                        # Capture the header line to know the column positions
                        if "Resource Type" in line and "Bundles" in line and "Resources" in line:
                            header_line = line
                            continue

                        # Parse data lines
                        if line.strip() and header_line:
                            try:
                                # Split the line by whitespace and reassemble
                                parts = line.split()
                                if len(parts) >= 5:  # At least Resource Type, Bundles, Resources, Time, Success Rate
                                    resource_type = parts[0]
                                    bundles = parts[1]
                                    resources = parts[2]
                                    time = parts[3]
                                    success_rate = parts[4]

                                    resource_breakdown.append({
                                        "resource_type": resource_type,
                                        "bundles": bundles,
                                        "resources": resources,
                                        "time": time,
                                        "success_rate": success_rate
                                    })
                            except:
                                pass

                if resource_breakdown:
                    temp_report["resource_breakdown"] = resource_breakdown

            # Extract overall statistics
            if "OVERALL STATISTICS:" in result["output"]:
                overall_stats = {}
                in_stats_section = False

                for line in output_lines:
                    if "OVERALL STATISTICS:" in line:
                        in_stats_section = True
                        continue
                    elif in_stats_section and "PERFORMANCE METRICS:" in line:
                        in_stats_section = False
                        break
                    elif in_stats_section and ":" in line and not "---" in line and not "===" in line:
                        try:
                            # Split by the first colon
                            parts = line.split(":", 1)
                            if len(parts) == 2:
                                key = parts[0].strip()
                                value = parts[1].strip()
                                overall_stats[key] = value
                        except:
                            pass

                if overall_stats:
                    temp_report["overall_stats"] = overall_stats

            # Extract performance metrics
            if "PERFORMANCE METRICS:" in result["output"]:
                performance_metrics = {}
                in_perf_section = False

                for line in output_lines:
                    if "PERFORMANCE METRICS:" in line:
                        in_perf_section = True
                        continue
                    elif in_perf_section and "SYSTEM RESOURCE USAGE:" in line:
                        in_perf_section = False
                        break
                    elif in_perf_section and ":" in line and not "---" in line and not "===" in line:
                        try:
                            # Split by the first colon
                            parts = line.split(":", 1)
                            if len(parts) == 2:
                                key = parts[0].strip()
                                value = parts[1].strip()
                                performance_metrics[key] = value
                        except:
                            pass

                if performance_metrics:
                    temp_report["performance_metrics"] = performance_metrics

            # Extract system resources
            if "SYSTEM RESOURCE USAGE:" in result["output"]:
                system_resources = {}
                in_sys_section = False

                for line in output_lines:
                    if "SYSTEM RESOURCE USAGE:" in line:
                        in_sys_section = True
                        continue
                    elif in_sys_section and "TOP RESOURCE TYPES BY VOLUME:" in line:
                        in_sys_section = False
                        break
                    elif in_sys_section and ":" in line and not "---" in line and not "===" in line:
                        try:
                            # Split by the first colon
                            parts = line.split(":", 1)
                            if len(parts) == 2:
                                key = parts[0].strip()
                                value = parts[1].strip()
                                system_resources[key] = value
                        except:
                            pass

                if system_resources:
                    temp_report["system_resources"] = system_resources

            # Extract top resources
            if "TOP RESOURCE TYPES BY VOLUME:" in result["output"]:
                top_resources = []
                in_top_section = False

                for line in output_lines:
                    if "TOP RESOURCE TYPES BY VOLUME:" in line:
                        in_top_section = True
                        continue
                    elif in_top_section and "FAILED BUNDLES:" in line:
                        in_top_section = False
                        break
                    elif in_top_section and line.strip() and not "---" in line and not "===" in line:
                        # Parse lines like: 1. Observation: 9878 resources (52.1% of total)
                        try:
                            parts = line.split(".")
                            if len(parts) >= 2:
                                rank = parts[0].strip()
                                rest = ".".join(parts[1:]).strip()

                                type_parts = rest.split(":")
                                if len(type_parts) >= 2:
                                    resource_type = type_parts[0].strip()
                                    count_info = ":".join(type_parts[1:]).strip()

                                    top_resources.append({
                                        "rank": rank,
                                        "resource_type": resource_type,
                                        "count_info": count_info
                                    })
                        except:
                            pass

                if top_resources:
                    temp_report["top_resources"] = top_resources

            # Extract failed bundles
            if "FAILED BUNDLES:" in result["output"]:
                failed_bundles = []
                in_failed_section = False
                header_line = None

                for line in output_lines:
                    if "FAILED BUNDLES:" in line:
                        in_failed_section = True
                        continue
                    elif in_failed_section and "====" in line and "INFO" in line:
                        in_failed_section = False
                        break
                    elif in_failed_section:
                        # Skip header separator lines
                        if "---" in line or "===" in line:
                            continue

                        # Capture the header line to know the column positions
                        if "Resource Type" in line and "File" in line and "Resources" in line and "Error" in line:
                            header_line = line
                            continue

                        # Parse data lines
                        if line.strip() and header_line and not "No failed bundles" in line:
                            try:
                                # For failed bundles, we need to be careful with the error column which might contain spaces
                                # First, identify the resource type and file
                                parts = line.split()
                                if len(parts) >= 3:
                                    resource_type = parts[0]
                                    file_name = parts[1]

                                    # The rest is either resources and error, or just error
                                    rest = " ".join(parts[2:])

                                    # Try to extract resources if present
                                    resources = "N/A"
                                    error = rest

                                    # If we can identify a number at the start of rest, it's likely the resources count
                                    import re
                                    match = re.match(r'^\s*(\d+)\s+(.+)$', rest)
                                    if match:
                                        resources = match.group(1)
                                        error = match.group(2)

                                    failed_bundles.append({
                                        "resource_type": resource_type,
                                        "file_name": file_name,
                                        "resources": resources,
                                        "error": error
                                    })
                            except:
                                pass

                if failed_bundles:
                    temp_report["failed_bundles"] = failed_bundles

        # Merge data from all sources, prioritizing the most detailed information

        # 1. Use report data if available
        if "report" in result:
            report = result["report"]
            for section in consolidated_report:
                if section in report and report[section]:
                    consolidated_report[section] = report[section]

        # 2. Fill in any missing sections from the temp_report (extracted from output)
        for section in consolidated_report:
            if not consolidated_report[section] and section in temp_report and temp_report[section]:
                consolidated_report[section] = temp_report[section]

        # 3. Add any additional data from stats and summary
        if "stats" in result:
            stats = result["stats"]
            # Extract key metrics from stats and add to overall_stats if not already present
            for key, value in stats.items():
                if key not in consolidated_report["overall_stats"]:
                    consolidated_report["overall_stats"][key] = value

        if "summary" in result:
            summary = result["summary"]
            # Extract key metrics from summary
            if "resources_processed" in summary and "Total Resources Processed" not in consolidated_report["overall_stats"]:
                consolidated_report["overall_stats"]["Total Resources Processed"] = summary["resources_processed"]

            if "processing_time" in summary and "Total Processing Time" not in consolidated_report["overall_stats"]:
                consolidated_report["overall_stats"]["Total Processing Time"] = summary["processing_time"]

            if "resources_per_second" in summary and "Resources Per Second" not in consolidated_report["performance_metrics"]:
                consolidated_report["performance_metrics"]["Resources Per Second"] = summary["resources_per_second"]

        # 4. Add failed bundles from result if available
        if "failed_bundles" in result and result["failed_bundles"]:
            # Add any failed bundles from the result that aren't already in the list
            for item in result["failed_bundles"]:
                if not any(b.get("file_name") == item.get("file_name") for b in consolidated_report["failed_bundles"]):
                    consolidated_report["failed_bundles"].append(item)

        # Display a compact summary of key metrics at the top
        st.markdown("### Loading Results")

        # Create a row of key metrics using Streamlit metrics
        col1, col2, col3, col4 = st.columns(4)

        # Extract key metrics for the summary
        total_resources = "N/A"
        success_rate = "N/A"
        processing_time = "N/A"
        resources_per_second = "N/A"

        # Try to get total resources from various sources
        if "Total Resources Processed" in consolidated_report["overall_stats"]:
            total_resources = consolidated_report["overall_stats"]["Total Resources Processed"]
        elif "resources" in stats:
            total_resources = stats["resources"]
        elif "resources_processed" in summary:
            total_resources = summary["resources_processed"]

        # Try to get success rate
        if "Total Bundles Processed" in consolidated_report["overall_stats"]:
            success_rate = consolidated_report["overall_stats"]["Total Bundles Processed"]
            if "%" in success_rate:
                success_rate = success_rate.split("(")[1].split(")")[0]

        # Try to get processing time
        if "Total Processing Time" in consolidated_report["overall_stats"]:
            processing_time = consolidated_report["overall_stats"]["Total Processing Time"]
        elif "time" in stats:
            processing_time = stats["time"]
        elif "processing_time" in summary:
            processing_time = summary["processing_time"]

        # Try to get resources per second
        if "Resources Per Second" in consolidated_report["performance_metrics"]:
            resources_per_second = consolidated_report["performance_metrics"]["Resources Per Second"]
        elif "throughput" in stats:
            resources_per_second = stats["throughput"].replace("Resources Per Second: ", "")
        elif "resources_per_second" in summary:
            resources_per_second = f"{summary['resources_per_second']}/sec"

        # Display the metrics
        with col1:
            st.metric("Resources", total_resources)

        with col2:
            st.metric("Success Rate", success_rate)

        with col3:
            st.metric("Processing Time", processing_time)

        with col4:
            st.metric("Resources/sec", resources_per_second)

        # Show status (success, partial success, or failure)
        is_partial_success = False
        if "partial_success" in result and result["partial_success"]:
            is_partial_success = True
        elif "summary" in result and "partial_success" in result["summary"] and result["summary"]["partial_success"]:
            is_partial_success = True

        # Display status message
        if is_partial_success:
            st.warning("⚠️ **Partial Success**: Some bundles failed to load. See the Failed Items tab for details.")
        elif consolidated_report["failed_bundles"]:
            st.warning(f"⚠️ **Partial Success**: {len(consolidated_report['failed_bundles'])} bundle(s) failed to load.")
        else:
            st.success("✅ **Success**: All bundles loaded successfully.")

        # Display the detailed report
        display_report_data(consolidated_report)

        # Option to view complete output in a compact expander (only once)
        if "output" in result:
            with st.expander("View complete details", expanded=False):
                st.code(result["output"])
    else:
        logger.error(f"Error loading data: {result}")

        # Display a more user-friendly error message
        if isinstance(result, dict) and "error" in result:
            error_md = f"❌ **Error loading data:** {result['error']}\n\n"

            if "details" in result:
                error_md += f"**Details:** {result['details']}\n\n"

            if "suggestion" in result:
                error_md += f"**Suggestion:** {result['suggestion']}\n\n"

            error_placeholder.error(error_md)

            # Create a consolidated report from all available data sources
            consolidated_report = {
                "resource_breakdown": [],
                "overall_stats": {},
                "performance_metrics": {},
                "system_resources": {},
                "failed_bundles": [],
                "top_resources": []
            }

            # Extract data from the output if available
            if "output" in result:
                # Create a temporary report to extract data from the output
                temp_report = {
                    "resource_breakdown": [],
                    "overall_stats": {},
                    "performance_metrics": {},
                    "system_resources": {},
                    "failed_bundles": [],
                    "top_resources": []
                }

                # Extract data from the output (same code as in the success case)
                output_lines = result["output"].split("\n")

                # Extract resource breakdown
                if "RESOURCE TYPE BREAKDOWN:" in result["output"]:
                    # (Same extraction code as in the success case)
                    # ... (omitted for brevity)
                    pass

                # Extract other sections similarly
                # ... (omitted for brevity)

            # Use report data if available
            if "report" in result:
                report = result["report"]
                for section in consolidated_report:
                    if section in report and report[section]:
                        consolidated_report[section] = report[section]

            # Show report data if available even in case of error
            if "report" in result or "output" in result:
                st.markdown("##### Loading Report")
                st.info("Even though there was an error, some data might have been processed. Here's the report:")

                # If we have a report, use it
                if "report" in result:
                    display_report_data(result["report"])
                # Otherwise, try to extract information from the output
                elif "output" in result:
                    # Create a temporary report from the output
                    temp_report = {
                        "resource_breakdown": [],
                        "overall_stats": {},
                        "performance_metrics": {},
                        "system_resources": {},
                        "failed_bundles": [],
                        "top_resources": []
                    }

                    # Extract data from the output
                    # (This would be the same extraction code as in the success case)
                    # ... (omitted for brevity)

                    display_report_data(temp_report)

            # Show complete output if available (only once)
            if "output" in result or "stdout" in result:
                output_text = result.get("output", result.get("stdout", ""))
                with st.expander("View complete output", expanded=False):
                    st.code(output_text)
