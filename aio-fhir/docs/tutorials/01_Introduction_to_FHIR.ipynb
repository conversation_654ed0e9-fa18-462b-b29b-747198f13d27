{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Introduction to FHIR (Fast Healthcare Interoperability Resources)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "## Overview\n", "\n", "This tutorial introduces the fundamentals of FHIR (Fast Healthcare Interoperability Resources), the modern standard for healthcare data exchange. By the end of this tutorial, you'll understand:\n", "\n", "- What FHIR is and why it was developed\n", "- The core components of the FHIR standard\n", "- How FHIR resources are structured\n", "- How to work with FHIR data using Python\n", "- How to query FHIR servers using REST APIs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "## Prerequisites\n", "\n", "- Basic Python programming knowledge\n", "- Familiarity with healthcare concepts\n", "- Understanding of JSON data structures\n", "\n", "Let's begin by importing the libraries we'll need for this tutorial:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import requests\n", "import json\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import os\n", "from pprint import pprint\n", "\n", "\n", "# Set up visualization style\n", "plt.style.use('ggplot')\n", "sns.set(style=\"whitegrid\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. What is FHIR?\n", "\n", "FHIR (Fast Healthcare Interoperability Resources) is a standard for healthcare data exchange, developed by HL7 International. It's designed to address the challenges of sharing healthcare information across different systems and organizations."]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "### Key Characteristics of FHIR:\n", "\n", "- **Modern web standards**: FHIR uses RESTful APIs, JSON, XML, and other web technologies\n", "- **Resource-based architecture**: Healthcare data is organized into modular \"resources\"\n", "- **Human readability**: Resources are designed to be understandable by both humans and machines\n", "- **Implementation focus**: Emphasis on practical implementation and real-world use cases\n", "- **Flexibility**: Supports both simple and complex healthcare data scenarios\n", "\n", "### Why FHIR Was Developed\n", "\n", "Prior to FHIR, healthcare data exchange relied on standards like HL7 v2 (messaging) and HL7 v3 (document-centric), which had limitations:\n", "\n", "- Complex implementation requirements\n", "- Steep learning curve\n", "- Limited compatibility with modern web technologies\n", "- Challenges with partial data exchange\n", "\n", "FHIR was developed to address these limitations while leveraging modern web technologies and development approaches.\n", "\n", "### FHIR Versions\n", "\n", "FHIR has evolved through several versions:\n", "\n", "- DSTU1 (Draft Standard for Trial Use 1): Initial release\n", "- DSTU2: Added more resources and refined existing ones\n", "- STU3 (Standard for Trial Use 3): Further refinements and additions\n", "- R4 (Release 4): First normative content, current stable version\n", "- R5: Latest version with additional resources and capabilities\n", "\n", "This tutorial focuses on FHIR R4, which is widely implemented and includes normative (stable) content."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. FHIR Resources\n", "\n", "FHIR organizes healthcare data into **resources**, which are the building blocks of the standard. Each resource represents a discrete unit of healthcare information, such as a patient, an observation, or a medication.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "### Resource Structure\n", "\n", "All FHIR resources share a common structure:\n", "\n", "- **Resource Type**: Identifies the type of resource (e.g., Patient, Observation)\n", "- **Metadata**: Information about the resource itself (e.g., id, version)\n", "- **Content**: The actual healthcare data specific to the resource type\n", "\n", "### Common Resource Elements\n", "\n", "Every FHIR resource includes these elements:\n", "\n", "- **id**: A logical identifier for the resource\n", "- **meta**: Metadata about the resource (version ID, last updated timestamp, etc.)\n", "- **resourceType**: The type of resource\n", "- **text**: A human-readable narrative of the resource content\n", "\n", "### Core FHIR Resources\n", "\n", "FHIR defines over 140 different resource types, but some of the most commonly used include:\n", "\n", "- **Patient**: Demographic information about an individual receiving healthcare services\n", "- **Encounter**: A healthcare encounter (e.g., office visit, hospitalization)\n", "- **Observation**: A measurement or assertion about a patient (e.g., vital signs, lab results)\n", "- **Condition**: A clinical condition, problem, diagnosis, or other event\n", "- **MedicationRequest**: An order for medication to be dispensed and administered\n", "- **Procedure**: A procedure performed on a patient\n", "\n", "####  Complete list of all FHIR resources: [FHIR Resource List](https://www.hl7.org/fhir/resourcelist.html)\n", "\n", "\n", "Let's look at an example of a FHIR Patient resource:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "   \"resourceType\": \"Patient\",\n", "   \"id\": \"example\",\n", "   \"meta\": {\n", "      \"versionId\": \"1\",\n", "      \"lastUpdated\": \"2022-01-01T12:00:00Z\"\n", "   },\n", "   \"text\": {\n", "      \"status\": \"generated\",\n", "      \"div\": \"<div xmlns=\\\"http://www.w3.org/1999/xhtml\\\"><PERSON></div>\"\n", "   },\n", "   \"identifier\": [\n", "      {\n", "         \"system\": \"http://hospital.example.org/identifiers/patients\",\n", "         \"value\": \"12345\"\n", "      }\n", "   ],\n", "   \"active\": true,\n", "   \"name\": [\n", "      {\n", "         \"use\": \"official\",\n", "         \"family\": \"<PERSON>\",\n", "         \"given\": [\n", "            \"<PERSON>\",\n", "            \"<PERSON>\"\n", "         ]\n", "      }\n", "   ],\n", "   \"telecom\": [\n", "      {\n", "         \"system\": \"phone\",\n", "         \"value\": \"************\",\n", "         \"use\": \"home\"\n", "      },\n", "      {\n", "         \"system\": \"email\",\n", "         \"value\": \"<EMAIL>\"\n", "      }\n", "   ],\n", "   \"gender\": \"male\",\n", "   \"birthDate\": \"1974-12-25\",\n", "   \"address\": [\n", "      {\n", "         \"use\": \"home\",\n", "         \"line\": [\n", "            \"123 Main St\"\n", "         ],\n", "         \"city\": \"Anytown\",\n", "         \"state\": \"CA\",\n", "         \"postalCode\": \"12345\",\n", "         \"country\": \"USA\"\n", "      }\n", "   ]\n", "}\n"]}], "source": ["# Example of a FHIR Patient resource\n", "patient_example = {\n", "    \"resourceType\": \"Patient\",\n", "    \"id\": \"example\",\n", "    \"meta\": {\n", "        \"versionId\": \"1\",\n", "        \"lastUpdated\": \"2022-01-01T12:00:00Z\"\n", "        },\n", "    \"text\": {\n", "        \"status\": \"generated\",\n", "        \"div\": \"<div xmlns=\\\"http://www.w3.org/1999/xhtml\\\"><PERSON></div>\"\n", "        },\n", "    \"identifier\": [\n", "        {\n", "            \"system\": \"http://hospital.example.org/identifiers/patients\",\n", "            \"value\": \"12345\"\n", "        }\n", "        ],\n", "    \"active\": True,\n", "    \"name\": [\n", "        {\n", "            \"use\": \"official\",\n", "            \"family\": \"<PERSON>\",\n", "            \"given\": [\"<PERSON>\", \"<PERSON>\"]\n", "            }\n", "        ],\n", "    \"telecom\": [\n", "        {\n", "            \"system\": \"phone\",\n", "            \"value\": \"************\",\n", "            \"use\": \"home\"\n", "        },\n", "        {\n", "            \"system\": \"email\",\n", "            \"value\": \"<EMAIL>\"\n", "        }\n", "        ],\n", "    \"gender\": \"male\",\n", "    \"birthDate\": \"1974-12-25\",\n", "    \"address\": [\n", "        {\n", "            \"use\": \"home\",\n", "            \"line\": [\"123 Main St\"],\n", "            \"city\": \"Anytown\",\n", "            \"state\": \"CA\",\n", "            \"postalCode\": \"12345\",\n", "            \"country\": \"USA\"\n", "        }\n", "    ]\n", "}\n", "\n", "# Pretty print the Patient resource\n", "print(json.dumps(patient_example, indent=3))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Resource References\n", "\n", "FHIR resources can reference other resources, creating a web of interconnected data. For example, an Observation resource might reference the Patient it pertains to and the Encounter during which it was recorded.\n", "\n", "Here's an example of a FHIR Observation resource that references a Patient:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"resourceType\": \"Observation\",\n", "  \"id\": \"blood-pressure\",\n", "  \"meta\": {\n", "    \"versionId\": \"1\",\n", "    \"lastUpdated\": \"2022-01-01T12:30:00Z\"\n", "  },\n", "  \"status\": \"final\",\n", "  \"category\": [\n", "    {\n", "      \"coding\": [\n", "        {\n", "          \"system\": \"http://terminology.hl7.org/CodeSystem/observation-category\",\n", "          \"code\": \"vital-signs\",\n", "          \"display\": \"Vital Signs\"\n", "        }\n", "      ]\n", "    }\n", "  ],\n", "  \"code\": {\n", "    \"coding\": [\n", "      {\n", "        \"system\": \"http://loinc.org\",\n", "        \"code\": \"85354-9\",\n", "        \"display\": \"Blood pressure panel with all children optional\"\n", "      }\n", "    ],\n", "    \"text\": \"Blood pressure systolic & diastolic\"\n", "  },\n", "  \"subject\": {\n", "    \"reference\": \"Patient/example\",\n", "    \"display\": \"<PERSON>\"\n", "  },\n", "  \"encounter\": {\n", "    \"reference\": \"Encounter/example-encounter\"\n", "  },\n", "  \"effectiveDateTime\": \"2022-01-01T12:15:00Z\",\n", "  \"component\": [\n", "    {\n", "      \"code\": {\n", "        \"coding\": [\n", "          {\n", "            \"system\": \"http://loinc.org\",\n", "            \"code\": \"8480-6\",\n", "            \"display\": \"Systolic blood pressure\"\n", "          }\n", "        ]\n", "      },\n", "      \"valueQuantity\": {\n", "        \"value\": 120,\n", "        \"unit\": \"mmHg\",\n", "        \"system\": \"http://unitsofmeasure.org\",\n", "        \"code\": \"mm[Hg]\"\n", "      }\n", "    },\n", "    {\n", "      \"code\": {\n", "        \"coding\": [\n", "          {\n", "            \"system\": \"http://loinc.org\",\n", "            \"code\": \"8462-4\",\n", "            \"display\": \"Diastolic blood pressure\"\n", "          }\n", "        ]\n", "      },\n", "      \"valueQuantity\": {\n", "        \"value\": 80,\n", "        \"unit\": \"mmHg\",\n", "        \"system\": \"http://unitsofmeasure.org\",\n", "        \"code\": \"mm[Hg]\"\n", "      }\n", "    }\n", "  ]\n", "}\n"]}], "source": ["# Example of a FHIR Observation resource with references\n", "observation_example = {\n", "    \"resourceType\": \"Observation\",\n", "    \"id\": \"blood-pressure\",\n", "    \"meta\": {\n", "        \"versionId\": \"1\",\n", "        \"lastUpdated\": \"2022-01-01T12:30:00Z\"\n", "    },\n", "    \"status\": \"final\",\n", "    \"category\": [\n", "        {\n", "            \"coding\": [\n", "                {\n", "                    \"system\": \"http://terminology.hl7.org/CodeSystem/observation-category\",\n", "                    \"code\": \"vital-signs\",\n", "                    \"display\": \"Vital Signs\"\n", "                }\n", "            ]\n", "        }\n", "    ],\n", "    \"code\": {\n", "        \"coding\": [\n", "            {\n", "                \"system\": \"http://loinc.org\",\n", "                \"code\": \"85354-9\",\n", "                \"display\": \"Blood pressure panel with all children optional\"\n", "            }\n", "        ],\n", "        \"text\": \"Blood pressure systolic & diastolic\"\n", "    },\n", "    \"subject\": {\n", "        \"reference\": \"Patient/example\",\n", "        \"display\": \"<PERSON>\"\n", "    },\n", "    \"encounter\": {\n", "        \"reference\": \"Encounter/example-encounter\"\n", "    },\n", "    \"effectiveDateTime\": \"2022-01-01T12:15:00Z\",\n", "    \"component\": [\n", "        {\n", "            \"code\": {\n", "                \"coding\": [\n", "                    {\n", "                        \"system\": \"http://loinc.org\",\n", "                        \"code\": \"8480-6\",\n", "                        \"display\": \"Systolic blood pressure\"\n", "                    }\n", "                ]\n", "            },\n", "            \"valueQuantity\": {\n", "                \"value\": 120,\n", "                \"unit\": \"mmHg\",\n", "                \"system\": \"http://unitsofmeasure.org\",\n", "                \"code\": \"mm[Hg]\"\n", "            }\n", "        },\n", "        {\n", "            \"code\": {\n", "                \"coding\": [\n", "                    {\n", "                        \"system\": \"http://loinc.org\",\n", "                        \"code\": \"8462-4\",\n", "                        \"display\": \"Diastolic blood pressure\"\n", "                    }\n", "                ]\n", "            },\n", "            \"valueQuantity\": {\n", "                \"value\": 80,\n", "                \"unit\": \"mmHg\",\n", "                \"system\": \"http://unitsofmeasure.org\",\n", "                \"code\": \"mm[Hg]\"\n", "            }\n", "        }\n", "    ]\n", "}\n", "\n", "# Pretty print the Observation resource\n", "print(json.dumps(observation_example, indent=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. FHIR Data Types\n", "\n", "FHIR defines a set of data types that are used across different resources. Understanding these data types is essential for working with FHIR data.\n", "\n", "### Primitive Data Types\n", "\n", "- **boolean**: true or false\n", "- **integer**: A signed integer\n", "- **string**: A sequence of Unicode characters\n", "- **decimal**: A rational number\n", "- **uri**: A Uniform Resource Identifier\n", "- **url**: A Uniform Resource Locator\n", "- **canonical**: A URI that refers to a resource by its canonical URL\n", "- **base64Binary**: Base64-encoded binary content\n", "- **instant**: An instant in time, known at least to the second\n", "- **date**: A date, or partial date (e.g., year, year + month)\n", "- **dateTime**: A date, date + time, or partial date (e.g., year, year + month)\n", "- **time**: A time during the day\n", "- **code**: A string which has a code value from a controlled vocabulary\n", "- **oid**: An OID represented as a URI\n", "- **id**: A string that identifies a resource\n", "- **markdown**: A FHIR-defined markdown subset\n", "- **unsignedInt**: An unsigned integer\n", "- **positiveInt**: A positive integer\n", "\n", "### Complex Data Types\n", "\n", "- **Address**: A postal address\n", "- **Attachment**: Content such as a photo, document, or other media\n", "- **CodeableConcept**: A concept from a controlled terminology with text\n", "- **Coding**: A reference to a code in a terminology system\n", "- **ContactPoint**: Contact details (phone, email, etc.)\n", "- **Human<PERSON>ame**: A human name\n", "- **Identifier**: A business identifier\n", "- **Period**: A time period with start and end dates\n", "- **Quantity**: A measured or measurable amount\n", "- **Range**: A range of values\n", "- **Ratio**: A ratio of two quantities\n", "- **Reference**: A reference to another resource\n", "- **SampledData**: A series of measurements sampled from a signal\n", "- **Signature**: A digital signature\n", "- **Timing**: A timing schedule\n", "\n", "Let's look at some examples of these data types in FHIR resources:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["HumanName Example:\n", "{\n", "  \"use\": \"official\",\n", "  \"family\": \"<PERSON>\",\n", "  \"given\": [\n", "    \"<PERSON>\",\n", "    \"<PERSON>\"\n", "  ],\n", "  \"prefix\": [\n", "    \"Dr\"\n", "  ],\n", "  \"suffix\": [\n", "    \"PhD\"\n", "  ]\n", "}\n", "\n", "CodeableConcept Example:\n", "{\n", "  \"coding\": [\n", "    {\n", "      \"system\": \"http://snomed.info/sct\",\n", "      \"code\": \"73211009\",\n", "      \"display\": \"Diabetes mellitus\"\n", "    },\n", "    {\n", "      \"system\": \"http://loinc.org\",\n", "      \"code\": \"45392-8\",\n", "      \"display\": \"Diabetes mellitus\"\n", "    }\n", "  ],\n", "  \"text\": \"Diabetes\"\n", "}\n", "\n", "Quantity Example:\n", "{\n", "  \"value\": 8.3,\n", "  \"unit\": \"%\",\n", "  \"system\": \"http://unitsofmeasure.org\",\n", "  \"code\": \"%\"\n", "}\n", "\n", "Reference Example:\n", "{\n", "  \"reference\": \"Patient/123\",\n", "  \"display\": \"<PERSON>\"\n", "}\n", "\n", "Period Example:\n", "{\n", "  \"start\": \"2022-01-01\",\n", "  \"end\": \"2022-01-05\"\n", "}\n"]}], "source": ["# Examples of FHIR data types\n", "\n", "# HumanName data type\n", "human_name_example = {\n", "    \"use\": \"official\",\n", "    \"family\": \"<PERSON>\",\n", "    \"given\": [\"<PERSON>\", \"<PERSON>\"],\n", "    \"prefix\": [\"Dr\"],\n", "    \"suffix\": [\"PhD\"]\n", "}\n", "\n", "# CodeableConcept data type\n", "codeable_concept_example = {\n", "    \"coding\": [\n", "        {\n", "            \"system\": \"http://snomed.info/sct\",\n", "            \"code\": \"73211009\",\n", "            \"display\": \"Diabetes mellitus\"\n", "        },\n", "        {\n", "            \"system\": \"http://loinc.org\",\n", "            \"code\": \"45392-8\",\n", "            \"display\": \"Diabetes mellitus\"\n", "        }\n", "    ],\n", "    \"text\": \"Diabetes\"\n", "}\n", "\n", "# Quantity data type\n", "quantity_example = {\n", "    \"value\": 8.3,\n", "    \"unit\": \"%\",\n", "    \"system\": \"http://unitsofmeasure.org\",\n", "    \"code\": \"%\"\n", "}\n", "\n", "# Reference data type\n", "reference_example = {\n", "    \"reference\": \"Patient/123\",\n", "    \"display\": \"<PERSON>\"\n", "}\n", "\n", "# Period data type\n", "period_example = {\n", "    \"start\": \"2022-01-01\",\n", "    \"end\": \"2022-01-05\"\n", "}\n", "\n", "# Print examples\n", "print(\"HumanName Example:\")\n", "print(json.dumps(human_name_example, indent=2))\n", "print(\"\\nCodeableConcept Example:\")\n", "print(json.dumps(codeable_concept_example, indent=2))\n", "print(\"\\nQuantity Example:\")\n", "print(json.dumps(quantity_example, indent=2))\n", "print(\"\\nReference Example:\")\n", "print(json.dumps(reference_example, indent=2))\n", "print(\"\\nPeriod Example:\")\n", "print(json.dumps(period_example, indent=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. FHIR Extensions\n", "\n", "FHIR is designed to be extensible, allowing for customization to meet specific needs while maintaining interoperability. Extensions are used when the base FHIR resources don't include all the data elements needed for a particular use case.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "### Extension Structure\n", "\n", "Extensions have a URL that identifies the extension and a value that contains the extension data. The URL should point to a definition of the extension.\n", "\n", "Here's an example of a Patient resource with an extension for hair color:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"resourceType\": \"Patient\",\n", "  \"id\": \"patient-with-extensions\",\n", "  \"extension\": [\n", "    {\n", "      \"url\": \"http://example.org/fhir/StructureDefinition/patient-hairColor\",\n", "      \"valueString\": \"brown\"\n", "    },\n", "    {\n", "      \"url\": \"http://example.org/fhir/StructureDefinition/patient-preferredLanguage\",\n", "      \"valueCodeableConcept\": {\n", "        \"coding\": [\n", "          {\n", "            \"system\": \"urn:ietf:bcp:47\",\n", "            \"code\": \"en-US\",\n", "            \"display\": \"English (United States)\"\n", "          }\n", "        ],\n", "        \"text\": \"English\"\n", "      }\n", "    }\n", "  ],\n", "  \"name\": [\n", "    {\n", "      \"use\": \"official\",\n", "      \"family\": \"<PERSON>\",\n", "      \"given\": [\n", "        \"Jane\"\n", "      ]\n", "    }\n", "  ],\n", "  \"gender\": \"female\",\n", "  \"birthDate\": \"1982-05-15\"\n", "}\n"]}], "source": ["# Example of a FHIR resource with extensions\n", "patient_with_extension = {\n", "    \"resourceType\": \"Patient\",\n", "    \"id\": \"patient-with-extensions\",\n", "    \"extension\": [\n", "        {\n", "            \"url\": \"http://example.org/fhir/StructureDefinition/patient-hairColor\",\n", "            \"valueString\": \"brown\"\n", "        },\n", "        {\n", "            \"url\": \"http://example.org/fhir/StructureDefinition/patient-preferredLanguage\",\n", "            \"valueCodeableConcept\": {\n", "                \"coding\": [\n", "                    {\n", "                        \"system\": \"urn:ietf:bcp:47\",\n", "                        \"code\": \"en-US\",\n", "                        \"display\": \"English (United States)\"\n", "                    }\n", "                ],\n", "                \"text\": \"English\"\n", "            }\n", "        }\n", "    ],\n", "    \"name\": [\n", "        {\n", "            \"use\": \"official\",\n", "            \"family\": \"<PERSON>\",\n", "            \"given\": [\"<PERSON>\"]\n", "        }\n", "    ],\n", "    \"gender\": \"female\",\n", "    \"birthDate\": \"1982-05-15\"\n", "}\n", "\n", "# Pretty print the Patient resource with extensions\n", "print(json.dumps(patient_with_extension, indent=2))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["birthDate: '1982-05-15'\n", "extension:\n", "- url: http://example.org/fhir/StructureDefinition/patient-hairColor\n", "  valueString: brown\n", "- url: http://example.org/fhir/StructureDefinition/patient-preferredLanguage\n", "  valueCodeableConcept:\n", "    coding:\n", "    - code: en-US\n", "      display: English (United States)\n", "      system: urn:ietf:bcp:47\n", "    text: English\n", "gender: female\n", "id: patient-with-extensions\n", "name:\n", "- family: <PERSON>\n", "  given:\n", "  - <PERSON>\n", "  use: official\n", "resourceType: Patient\n", "\n"]}], "source": ["import yaml\n", "print(yaml.dump(patient_with_extension, default_flow_style=False))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'rich'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[8], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01<PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28<PERSON><PERSON>t\u001b[39m \u001b[38;5;28;01mas\u001b[39;00m rprint\n\u001b[1;32m      2\u001b[0m rprint(patient_with_extension)\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'rich'"]}], "source": ["from rich import print as rprint\n", "rprint(patient_with_extension)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "\n", "## 5. Working with FHIR Data in Python\n", "\n", "Now that we understand the basics of FHIR, let's explore how to work with FHIR data using Python. We'll use the `requests` library to interact with FHIR servers and process the returned data.\n", "\n", "### Accessing a FHIR Server\n", "\n", "FHIR servers expose RESTful APIs that allow clients to create, read, update, and delete resources. Let's see how to access a public FHIR server:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define a function to access a FHIR server\n", "def access_fhir_server(base_url, resource_type, params=None):\n", "    \"\"\"\n", "    Access a FHIR server and return the response.\n", "    \n", "    Parameters:\n", "    - base_url: The base URL of the FHIR server\n", "    - resource_type: The type of resource to retrieve (e.g., 'Patient', 'Observation')\n", "    - params: Optional query parameters\n", "    \n", "    Returns:\n", "    - The JSON response from the server\n", "    \"\"\"\n", "    url = f\"{base_url}/{resource_type}\"\n", "    \n", "    try:\n", "        response = requests.get(url, params=params)\n", "        response.raise_for_status()  # Raise an exception for HTTP errors\n", "        return response.json()\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"Error accessing FHIR server: {e}\")\n", "        return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Example: Access the public HAPI FHIR server\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Retrieved 10 Patient resources\n", "\n", "First Patient Details:\n", "ID: 596859\n", "Name: FHU45 FHU45\n", "Gender: male\n", "Birth Date: 1974-12-25\n", "\n", "==== Full Patient Resource:====\n", "birthDate: '1974-12-25'\n", "gender: male\n", "id: '596859'\n", "identifier:\n", "- value: FHU45\n", "meta:\n", "  lastUpdated: '2020-02-03T07:28:45.898+00:00'\n", "  source: '#Hn5zBfZXnNwLc0nQ'\n", "  versionId: '1'\n", "name:\n", "- family: FHU45\n", "  given:\n", "  - FHU45\n", "resourceType: Patient\n", "text:\n", "  div: <div xmlns=\"http://www.w3.org/1999/xhtml\"><div class=\"hapiHeaderText\">FHU45\n", "    <b>FHU45 </b></div><table class=\"hapiPropertyTable\"><tbody><tr><td>Identifier</td><td>FHU45</td></tr><tr><td>Date\n", "    of birth</td><td><span>25 December 1974</span></td></tr></tbody></table></div>\n", "  status: generated\n", "\n"]}], "source": ["# Example: Access the public HAPI FHIR server\n", "# Note: This is a public test server, so data may change or be reset\n", "hapi_fhir_base_url = \"http://hapi.fhir.org/baseR4\"\n", "\n", "# Let's try to get some Patient resources\n", "patients_response = access_fhir_server(hapi_fhir_base_url, \"Patient\", {\"_count\": 10})\n", "\n", "# Check if we got a valid response\n", "if patients_response and \"entry\" in patients_response:\n", "    print(f\"Retrieved {len(patients_response['entry'])} Patient resources\")\n", "    \n", "    # Print the first patient's details\n", "    if len(patients_response['entry']) > 0:\n", "        first_patient = patients_response['entry'][0]['resource']\n", "        print(\"\\nFirst Patient Details:\")\n", "        print(f\"ID: {first_patient.get('id')}\")\n", "        \n", "        # Extract and print the patient's name\n", "        if 'name' in first_patient and len(first_patient['name']) > 0:\n", "            name = first_patient['name'][0]\n", "            family = name.get('family', '')\n", "            given = ' '.join(name.get('given', []))\n", "            print(f\"Name: {given} {family}\")\n", "        \n", "        # Extract and print the patient's gender and birth date\n", "        print(f\"Gender: {first_patient.get('gender', 'Unknown')}\")\n", "        print(f\"Birth Date: {first_patient.get('birthDate', 'Unknown')}\")\n", "\n", "        # Print the whole patient resource with yaml\n", "        print(\"\\n==== Full Patient Resource:====\")\n", "        print(yaml.dump(first_patient))\n", "else:\n", "    # If we couldn't connect to the server, use a mock response for demonstration\n", "    print(\"Could not connect to FHIR server. Using mock data for demonstration.\")\n", "    \n", "    # Create a mock patient bundle\n", "    mock_patients = {\n", "        \"resourceType\": \"Bundle\",\n", "        \"type\": \"searchset\",\n", "        \"total\": 2,\n", "        \"entry\": [\n", "            {\n", "                \"resource\": {\n", "                    \"resourceType\": \"Patient\",\n", "                    \"id\": \"mock-patient-1\",\n", "                    \"name\": [\n", "                        {\n", "                            \"family\": \"Doe\",\n", "                            \"given\": [\"<PERSON>\"]\n", "                        }\n", "                    ],\n", "                    \"gender\": \"male\",\n", "                    \"birthDate\": \"1970-01-01\"\n", "                }\n", "            },\n", "            {\n", "                \"resource\": {\n", "                    \"resourceType\": \"Patient\",\n", "                    \"id\": \"mock-patient-2\",\n", "                    \"name\": [\n", "                        {\n", "                            \"family\": \"<PERSON>\",\n", "                            \"given\": [\"<PERSON>\"]\n", "                        }\n", "                    ],\n", "                    \"gender\": \"female\",\n", "                    \"birthDate\": \"1980-05-15\"\n", "                }\n", "            }\n", "        ]\n", "    }\n", "    \n", "    print(f\"Using {len(mock_patients['entry'])} mock Patient resources\")\n", "    \n", "    # Print the first mock patient's details\n", "    first_patient = mock_patients['entry'][0]['resource']\n", "    print(\"\\nFirst Patient Details:\")\n", "    print(f\"ID: {first_patient.get('id')}\")\n", "    \n", "    # Extract and print the patient's name\n", "    if 'name' in first_patient and len(first_patient['name']) > 0:\n", "        name = first_patient['name'][0]\n", "        family = name.get('family', '')\n", "        given = ' '.join(name.get('given', []))\n", "        print(f\"Name: {given} {family}\")\n", "    \n", "    # Extract and print the patient's gender and birth date\n", "    print(f\"Gender: {first_patient.get('gender', 'Unknown')}\")\n", "    print(f\"Birth Date: {first_patient.get('birthDate', 'Unknown')}\")\n", "    \n", "    # Use the mock data for further examples\n", "    patients_response = mock_patients"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Processing FHIR Data with Python\n", "\n", "Now let's see how to process FHIR data using Python. We'll extract data from the patient resources and convert it to a pandas DataFrame for analysis:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Patient Data DataFrame:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>gender</th>\n", "      <th>birth_date</th>\n", "      <th>active</th>\n", "      <th>family_name</th>\n", "      <th>given_names</th>\n", "      <th>city</th>\n", "      <th>state</th>\n", "      <th>country</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>596859</td>\n", "      <td>male</td>\n", "      <td>1974-12-25</td>\n", "      <td></td>\n", "      <td>FHU45</td>\n", "      <td>FHU45</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>596860</td>\n", "      <td>male</td>\n", "      <td>1974-12-25</td>\n", "      <td></td>\n", "      <td>FHU45</td>\n", "      <td>FHU45</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>596866</td>\n", "      <td>male</td>\n", "      <td>1974-12-25</td>\n", "      <td></td>\n", "      <td>FHU45</td>\n", "      <td>FHU45</td>\n", "      <td>PleasantVille</td>\n", "      <td>Vic</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>597038</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>597041</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>597063</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>597065</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>597067</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>597068</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>597069</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       id gender  birth_date active  family_name          given_names  \\\n", "0  596859   male  1974-12-25               FHU45                FHU45   \n", "1  596860   male  1974-12-25               FHU45                FHU45   \n", "2  596866   male  1974-12-25               FHU45                FHU45   \n", "3  597038                                                               \n", "4  597041                                                               \n", "5  597063                               <PERSON><PERSON><PERSON>   \n", "6  597065                            <PERSON><PERSON><PERSON><PERSON>   \n", "7  597067                      True                                     \n", "8  597068                            <PERSON><PERSON><PERSON><PERSON>   \n", "9  597069                                                               \n", "\n", "            city state country  \n", "0                               \n", "1                               \n", "2  PleasantVille   Vic      US  \n", "3                               \n", "4                               \n", "5                               \n", "6                               \n", "7                               \n", "8                               \n", "9                               "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Function to extract patient data from FHIR resources\n", "def extract_patient_data(patient_bundle):\n", "    \"\"\"\n", "    Extract patient data from a FHIR Bundle containing Patient resources.\n", "    \n", "    Parameters:\n", "    - patient_bundle: A FHIR Bundle containing Patient resources\n", "    \n", "    Returns:\n", "    - A list of dictionaries with extracted patient data\n", "    \"\"\"\n", "    patients_data = []\n", "    \n", "    # Check if the bundle has entries\n", "    if \"entry\" not in patient_bundle:\n", "        return patients_data\n", "    \n", "    # Process each patient resource\n", "    for entry in patient_bundle[\"entry\"]:\n", "        resource = entry[\"resource\"]\n", "        \n", "        # Skip if not a Patient resource\n", "        if resource[\"resourceType\"] != \"Patient\":\n", "            continue\n", "        \n", "        # Extract basic patient information\n", "        patient_info = {\n", "            \"id\": resource.get(\"id\", \"\"),\n", "            \"gender\": resource.get(\"gender\", \"\"),\n", "            \"birth_date\": resource.get(\"birthDate\", \"\"),\n", "            \"active\": resource.get(\"active\", \"\")\n", "        }\n", "        \n", "        # Extract name (using the first name in the list if available)\n", "        if \"name\" in resource and len(resource[\"name\"]) > 0:\n", "            name = resource[\"name\"][0]\n", "            patient_info[\"family_name\"] = name.get(\"family\", \"\")\n", "            patient_info[\"given_names\"] = \" \".join(name.get(\"given\", []))\n", "        else:\n", "            patient_info[\"family_name\"] = \"\"\n", "            patient_info[\"given_names\"] = \"\"\n", "        \n", "        # Extract the first address if available\n", "        if \"address\" in resource and len(resource[\"address\"]) > 0:\n", "            address = resource[\"address\"][0]\n", "            patient_info[\"city\"] = address.get(\"city\", \"\")\n", "            patient_info[\"state\"] = address.get(\"state\", \"\")\n", "            patient_info[\"country\"] = address.get(\"country\", \"\")\n", "        else:\n", "            patient_info[\"city\"] = \"\"\n", "            patient_info[\"state\"] = \"\"\n", "            patient_info[\"country\"] = \"\"\n", "        \n", "        # Add to the list\n", "        patients_data.append(patient_info)\n", "    \n", "    return patients_data\n", "\n", "# Extract patient data from the response\n", "patient_data = extract_patient_data(patients_response)\n", "\n", "# Convert to DataFrame\n", "patient_df = pd.DataFrame(patient_data)\n", "\n", "# Display the DataFrame\n", "print(\"Patient Data DataFrame:\")\n", "display(patient_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Working with FHIR Data in Python (continued)\n", "\n", "### Visualizing FHIR Data\n", "\n", "Now that we have our patient data in a DataFrame, we can create visualizations to better understand the data:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Patient Data with Age:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>gender</th>\n", "      <th>birth_date</th>\n", "      <th>active</th>\n", "      <th>family_name</th>\n", "      <th>given_names</th>\n", "      <th>city</th>\n", "      <th>state</th>\n", "      <th>country</th>\n", "      <th>age</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>596859</td>\n", "      <td>male</td>\n", "      <td>1974-12-25</td>\n", "      <td></td>\n", "      <td>FHU45</td>\n", "      <td>FHU45</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>50.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>596860</td>\n", "      <td>male</td>\n", "      <td>1974-12-25</td>\n", "      <td></td>\n", "      <td>FHU45</td>\n", "      <td>FHU45</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>50.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>596866</td>\n", "      <td>male</td>\n", "      <td>1974-12-25</td>\n", "      <td></td>\n", "      <td>FHU45</td>\n", "      <td>FHU45</td>\n", "      <td>PleasantVille</td>\n", "      <td>Vic</td>\n", "      <td>US</td>\n", "      <td>50.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>597038</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>597041</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>597063</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>597065</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>597067</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>597068</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>597069</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       id gender  birth_date active  family_name          given_names  \\\n", "0  596859   male  1974-12-25               FHU45                FHU45   \n", "1  596860   male  1974-12-25               FHU45                FHU45   \n", "2  596866   male  1974-12-25               FHU45                FHU45   \n", "3  597038                                                               \n", "4  597041                                                               \n", "5  597063                               <PERSON><PERSON><PERSON>   \n", "6  597065                            <PERSON><PERSON><PERSON><PERSON>   \n", "7  597067                      True                                     \n", "8  597068                            <PERSON><PERSON><PERSON><PERSON>   \n", "9  597069                                                               \n", "\n", "            city state country   age  \n", "0                               50.0  \n", "1                               50.0  \n", "2  PleasantVille   Vic      US  50.0  \n", "3                                NaN  \n", "4                                NaN  \n", "5                                NaN  \n", "6                                NaN  \n", "7                                NaN  \n", "8                                NaN  \n", "9                                NaN  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Calculate age from birth date\n", "def calculate_age(birth_date):\n", "    if not birth_date:\n", "        return None\n", "    \n", "    try:\n", "        born = datetime.strptime(birth_date, \"%Y-%m-%d\")\n", "        today = datetime.now()\n", "        age = today.year - born.year - ((today.month, today.day) < (born.month, born.day))\n", "        return age\n", "    except ValueError:\n", "        return None\n", "\n", "# Add age column\n", "patient_df[\"age\"] = patient_df[\"birth_date\"].apply(calculate_age)\n", "\n", "# Display the updated DataFrame\n", "print(\"\\nPatient Data with Age:\")\n", "display(patient_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1600x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create visualizations of patient data with subplots side by side\n", "if not patient_df.empty and 'gender' in patient_df.columns:\n", "    # Create a figure with two subplots side by side\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))\n", "    \n", "    # Count patients by gender\n", "    gender_counts = patient_df['gender'].value_counts()\n", "    \n", "    # Create a pie chart in the first subplot\n", "    ax1.pie(gender_counts, labels=gender_counts.index, autopct='%1.1f%%', \n", "            startangle=90, colors=sns.color_palette('pastel'))\n", "    ax1.set_title('Patient Gender Distribution')\n", "    ax1.axis('equal')  # Equal aspect ratio ensures that pie is drawn as a circle\n", "    \n", "    # Create a bar chart of age distribution by gender in the second subplot\n", "    if 'age' in patient_df.columns:\n", "        sns.histplot(data=patient_df, x='age', hue='gender', multiple='stack', \n", "                    bins=10, ax=ax2)\n", "        ax2.set_title('Patient Age Distribution by Gender')\n", "        ax2.set_xlabel('Age')\n", "        ax2.set_ylabel('Count')\n", "    else:\n", "        ax2.text(0.5, 0.5, 'Age data not available', \n", "                ha='center', va='center', fontsize=12)\n", "        ax2.axis('off')\n", "    \n", "    # Adjust layout\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "else:\n", "    print(\"Not enough data to create visualizations with real data. Using mock data...\")\n", "    \n", "    # Create a figure with two subplots side by side\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))\n", "    \n", "    # Create mock data for demonstration\n", "    mock_gender_data = pd.DataFrame({\n", "        'gender': ['male', 'female', 'male', 'female', 'female', 'male', 'female', 'unknown', 'male', 'female']\n", "    })\n", "    \n", "    # Count patients by gender\n", "    gender_counts = mock_gender_data['gender'].value_counts()\n", "    \n", "    # Create a pie chart in the first subplot\n", "    ax1.pie(gender_counts, labels=gender_counts.index, autopct='%1.1f%%', \n", "            startangle=90, colors=sns.color_palette('pastel'))\n", "    ax1.set_title('Mock Patient Gender Distribution')\n", "    ax1.axis('equal')\n", "    \n", "    # Create mock age data\n", "    mock_age_data = pd.DataFrame({\n", "        'age': [25, 32, 45, 67, 18, 55, 72, 41, 36, 29],\n", "        'gender': ['male', 'female', 'male', 'female', 'female', 'male', 'female', 'unknown', 'male', 'female']\n", "    })\n", "    \n", "    # Create a bar chart of age distribution by gender in the second subplot\n", "    sns.histplot(data=mock_age_data, x='age', hue='gender', multiple='stack', \n", "                bins=5, ax=ax2)\n", "    ax2.set_title('Mock Patient Age Distribution by Gender')\n", "    ax2.set_xlabel('Age')\n", "    ax2.set_ylabel('Count')\n", "    \n", "    # Adjust layout\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. FHIR Search\n", "\n", "FHIR servers support a powerful search mechanism that allows clients to find resources that match specific criteria. Let's explore how to use FHIR search parameters:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Response [400]>"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["requests.get(hapi_fhir_base_url, search_params)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 10 female patients\n", "\n", "Female Patients:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>gender</th>\n", "      <th>birth_date</th>\n", "      <th>active</th>\n", "      <th>family_name</th>\n", "      <th>given_names</th>\n", "      <th>city</th>\n", "      <th>state</th>\n", "      <th>country</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5</td>\n", "      <td>female</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>13</td>\n", "      <td>female</td>\n", "      <td>1981-08-09</td>\n", "      <td></td>\n", "      <td>Agus<PERSON><PERSON></td>\n", "      <td>Mercedes</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>37</td>\n", "      <td>female</td>\n", "      <td>2017-09-05T22:00:00.000Z</td>\n", "      <td></td>\n", "      <td>test_fn</td>\n", "      <td>Test</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>109</td>\n", "      <td>female</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>134</td>\n", "      <td>female</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>739</td>\n", "      <td>female</td>\n", "      <td>1980-10-24</td>\n", "      <td></td>\n", "      <td><PERSON></td>\n", "      <td>Ken</td>\n", "      <td>Spock</td>\n", "      <td></td>\n", "      <td>Germany</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>1009</td>\n", "      <td>female</td>\n", "      <td>2003-01-12</td>\n", "      <td></td>\n", "      <td>刘</td>\n", "      <td>永好</td>\n", "      <td>成都市</td>\n", "      <td>四川省</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>1059</td>\n", "      <td>female</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>1194</td>\n", "      <td>female</td>\n", "      <td>1984-11-11</td>\n", "      <td></td>\n", "      <td>Phạm</td>\n", "      <td><PERSON><PERSON><PERSON>ăm HL7</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td></td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>1208</td>\n", "      <td>female</td>\n", "      <td>1989-04-03</td>\n", "      <td></td>\n", "      <td>PIPPO</td>\n", "      <td>TONIA</td>\n", "      <td>Roma</td>\n", "      <td></td>\n", "      <td>IT</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     id  gender                birth_date active family_name  \\\n", "0     5  female                                                \n", "1    13  female                1981-08-09            Agust<PERSON>   \n", "2    37  female  2017-09-05T22:00:00.000Z            test_fn   \n", "3   109  female                                                \n", "4   134  female                                                \n", "5   739  female                1980-10-24              <PERSON>   \n", "6  1009  female                2003-01-12                  刘   \n", "7  1059  female                                                \n", "8  1194  female                1984-11-11               <PERSON><PERSON><PERSON>   \n", "9  1208  female                1989-04-03              PIPPO   \n", "\n", "           given_names                   city state  country  \n", "0                                                             \n", "1             Mercedes                                        \n", "2                 Test                                        \n", "3                                                             \n", "4                                                             \n", "5                  <PERSON>  \n", "6                   永好                    成都市   四川省           \n", "7                                                             \n", "8  Thị Tám Bảy Năm HL7  Thành Phố Hồ Chí <PERSON>             US  \n", "9                TONIA                   Roma             IT  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Function to search for FHIR resources\n", "def search_fhir_resources(base_url, resource_type, search_params):\n", "    \"\"\"\n", "    Search for FHIR resources using search parameters.\n", "    \n", "    Parameters:\n", "    - base_url: The base URL of the FHIR server\n", "    - resource_type: The type of resource to search for\n", "    - search_params: A dictionary of search parameters\n", "    \n", "    Returns:\n", "    - The search results as a JSON object\n", "    \"\"\"\n", "    url = f\"{base_url}/{resource_type}\"\n", "    \n", "    try:\n", "        response = requests.get(url, params=search_params)\n", "        response.raise_for_status()\n", "        return response.json()\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"Error searching FHIR resources: {e}\")\n", "        return None\n", "\n", "# Example: Search for patients by gender\n", "search_params = {\n", "    \"gender\": \"female\",\n", "    \"_count\": 10\n", "}\n", "\n", "search_results = search_fhir_resources(hapi_fhir_base_url, \"Patient\", search_params)\n", "\n", "# Check if we got valid search results\n", "if search_results and \"entry\" in search_results:\n", "    print(f\"Found {len(search_results['entry'])} female patients\")\n", "    \n", "    # Extract and display the search results\n", "    female_patients = extract_patient_data(search_results)\n", "    female_patients_df = pd.DataFrame(female_patients)\n", "    \n", "    print(\"\\nFemale Patients:\")\n", "    display(female_patients_df)\n", "else:\n", "    print(\"Could not perform search or no results found. Using mock data for demonstration.\")\n", "    \n", "    # Create mock search results\n", "    mock_female_patients = [\n", "        {\n", "            \"id\": \"mock-female-1\",\n", "            \"gender\": \"female\",\n", "            \"birth_date\": \"1980-05-15\",\n", "            \"active\": True,\n", "            \"family_name\": \"<PERSON>\",\n", "            \"given_names\": \"<PERSON>\",\n", "            \"city\": \"Boston\",\n", "            \"state\": \"MA\",\n", "            \"country\": \"USA\"\n", "        },\n", "        {\n", "            \"id\": \"mock-female-2\",\n", "            \"gender\": \"female\",\n", "            \"birth_date\": \"1992-11-30\",\n", "            \"active\": True,\n", "            \"family_name\": \"<PERSON>\",\n", "            \"given_names\": \"<PERSON>\",\n", "            \"city\": \"Chicago\",\n", "            \"state\": \"IL\",\n", "            \"country\": \"USA\"\n", "        }\n", "    ]\n", "    \n", "    female_patients_df = pd.DataFrame(mock_female_patients)\n", "    \n", "    print(f\"Using {len(mock_female_patients)} mock female patients\")\n", "    print(\"\\nMock Female Patients:\")\n", "    display(female_patients_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Common FHIR Search Parameters\n", "\n", "FHIR defines a set of common search parameters that can be used across different resource types:\n", "\n", "- **_id**: Search by resource id\n", "- **_lastUpdated**: Search by last updated date\n", "- **_tag**: Search by tag\n", "- **_profile**: Search by profile\n", "- **_security**: Search by security label\n", "- **_text**: Search in narrative text\n", "- **_content**: Search in resource content\n", "- **_list**: Search for resources in a specific list\n", "- **_has**: Search for resources that have a specific reference\n", "- **_type**: Search for resources of a specific type\n", "- **_sort**: Sort the results\n", "- **_count**: Limit the number of results\n", "- **_include**: Include referenced resources\n", "- **_revinclude**: Include resources that reference the matched resources\n", "\n", "Each resource type also has specific search parameters. For example, Patient resources can be searched by:\n", "\n", "- **name**: <PERSON><PERSON>'s name\n", "- **family**: <PERSON><PERSON>'s family name\n", "- **given**: <PERSON><PERSON>'s given name\n", "- **identifier**: Patient's identifier\n", "- **gender**: Patient's gender\n", "- **birthdate**: <PERSON><PERSON>'s birth date\n", "- **address**: <PERSON><PERSON>'s address\n", "- **email**: <PERSON><PERSON>'s email address\n", "- **phone**: <PERSON><PERSON>'s phone number\n", "- **organization**: Organization managing the patient\n", "- **language**: <PERSON><PERSON>'s preferred language\n", "\n", "Let's try a more complex search:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 5 patients named <PERSON> born after 1980\n", "\n", "<PERSON>ients Born After 1980:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>gender</th>\n", "      <th>birth_date</th>\n", "      <th>active</th>\n", "      <th>family_name</th>\n", "      <th>given_names</th>\n", "      <th>city</th>\n", "      <th>state</th>\n", "      <th>country</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>45057517</td>\n", "      <td>male</td>\n", "      <td>1981-01-01</td>\n", "      <td></td>\n", "      <td><PERSON></td>\n", "      <td>John</td>\n", "      <td>Berkeley</td>\n", "      <td>CA</td>\n", "      <td>USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1175071</td>\n", "      <td>female</td>\n", "      <td>1981-01-01</td>\n", "      <td></td>\n", "      <td>SMITH</td>\n", "      <td>AMANDA</td>\n", "      <td>CRYSTAL</td>\n", "      <td>MN</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>45057518</td>\n", "      <td>male</td>\n", "      <td>1981-01-01</td>\n", "      <td></td>\n", "      <td><PERSON></td>\n", "      <td>John</td>\n", "      <td>Berkeley</td>\n", "      <td>CA</td>\n", "      <td>USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>45144917</td>\n", "      <td>unknown</td>\n", "      <td>1981-01-23</td>\n", "      <td></td>\n", "      <td><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1385833</td>\n", "      <td>female</td>\n", "      <td>1981-07-12</td>\n", "      <td></td>\n", "      <td><PERSON></td>\n", "      <td>Jane</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         id   gender  birth_date active family_name given_names      city  \\\n", "0  45057517     male  1981-01-01              <PERSON>   \n", "1   1175071   female  1981-01-01              SMITH      AMANDA   CRYSTAL   \n", "2  45057518     male  1981-01-01              <PERSON>   \n", "3  45144917  unknown  1981-01-23              <PERSON>             \n", "4   1385833   female  1981-07-12              <PERSON>             \n", "\n", "  state country  \n", "0    CA     USA  \n", "1    MN          \n", "2    CA     USA  \n", "3                \n", "4                "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Example: Search for patients with a specific family name and birth date range\n", "complex_search_params = {\n", "    \"family\": \"<PERSON>\",\n", "    \"birthdate\": \"ge1980\",  # Born on or after 1980\n", "    \"_sort\": \"birthdate\",   # Sort by birth date\n", "    \"_count\": 5             # Limit to 5 results\n", "}\n", "\n", "complex_search_results = search_fhir_resources(hapi_fhir_base_url, \"Patient\", complex_search_params)\n", "\n", "# Check if we got valid search results\n", "if complex_search_results and \"entry\" in complex_search_results:\n", "    print(f\"Found {len(complex_search_results['entry'])} patients named <PERSON> born after 1980\")\n", "    \n", "    # Extract and display the search results\n", "    smith_patients = extract_patient_data(complex_search_results)\n", "    smith_patients_df = pd.DataFrame(smith_patients)\n", "    \n", "    print(\"\\nSmith Patients Born After 1980:\")\n", "    display(smith_patients_df)\n", "else:\n", "    print(\"Could not perform complex search or no results found. Using mock data for demonstration.\")\n", "    \n", "    # Create mock search results\n", "    mock_smith_patients = [\n", "        {\n", "            \"id\": \"mock-smith-1\",\n", "            \"gender\": \"male\",\n", "            \"birth_date\": \"1985-03-22\",\n", "            \"active\": True,\n", "            \"family_name\": \"<PERSON>\",\n", "            \"given_names\": \"<PERSON>\",\n", "            \"city\": \"New York\",\n", "            \"state\": \"NY\",\n", "            \"country\": \"USA\"\n", "        },\n", "        {\n", "            \"id\": \"mock-smith-2\",\n", "            \"gender\": \"female\",\n", "            \"birth_date\": \"1992-07-14\",\n", "            \"active\": True,\n", "            \"family_name\": \"<PERSON>\",\n", "            \"given_names\": \"<PERSON>\",\n", "            \"city\": \"Los Angeles\",\n", "            \"state\": \"CA\",\n", "            \"country\": \"USA\"\n", "        }\n", "    ]\n", "    \n", "    smith_patients_df = pd.DataFrame(mock_smith_patients)\n", "    \n", "    print(f\"Using {len(mock_smith_patients)} mock Smith patients born after 1980\")\n", "    print(\"\\nMock Smith Pat<PERSON> Born After 1980:\")\n", "    display(smith_patients_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. FHIR Operations\n", "\n", "In addition to the standard CRUD (Create, Read, Update, Delete) operations, FHIR defines special operations that can be performed on resources. These operations are invoked using the `$` prefix.\n", "\n", "Some common FHIR operations include:\n", "\n", "- **$validate**: Validate a resource against profiles\n", "- **$meta**: Access or modify metadata\n", "- **$meta-add**: Add metadata\n", "- **$meta-delete**: Delete metadata\n", "- **$document**: Generate a document\n", "- **$everything**: Retrieve all information about a resource\n", "- **$expand**: Expand a value set\n", "- **$lookup**: Look up a code\n", "- **$translate**: Translate a code from one value set to another\n", "\n", "Let's see an example of using the `$everything` operation to retrieve all information about a patient:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Retrieved 5 resources related to patient 596859\n", "\n", "Resource Types:\n", "Patient: 1\n", "Observation: 4\n"]}], "source": ["# Function to perform a FHIR operation\n", "def perform_fhir_operation(base_url, resource_type, resource_id, operation, params=None):\n", "    \"\"\"\n", "    Perform a FHIR operation on a resource.\n", "    \n", "    Parameters:\n", "    - base_url: The base URL of the FHIR server\n", "    - resource_type: The type of resource\n", "    - resource_id: The ID of the resource\n", "    - operation: The operation to perform (without the $ prefix)\n", "    - params: Optional parameters for the operation\n", "    \n", "    Returns:\n", "    - The operation results as a JSON object\n", "    \"\"\"\n", "    url = f\"{base_url}/{resource_type}/{resource_id}/${operation}\"\n", "    \n", "    try:\n", "        response = requests.get(url, params=params)\n", "        response.raise_for_status()\n", "        return response.json()\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"Error performing FHIR operation: {e}\")\n", "        return None\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Example: Use the $everything operation on a patient\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Retrieved 5 resources related to patient 596859\n", "\n", "Resource Types:\n", "Patient: 1\n", "Observation: 4\n"]}], "source": ["# Example: Use the $everything operation on a patient\n", "# First, let's get a patient ID from our previous results\n", "patient_id = None\n", "\n", "if not patient_df.empty and 'id' in patient_df.columns and not patient_df['id'].empty:\n", "    patient_id = patient_df['id'].iloc[0]\n", "    \n", "    # Perform the $everything operation\n", "    everything_results = perform_fhir_operation(hapi_fhir_base_url, \"Patient\", patient_id, \"everything\")\n", "    \n", "    if everything_results and \"entry\" in everything_results:\n", "        print(f\"Retrieved {len(everything_results['entry'])} resources related to patient {patient_id}\")\n", "        \n", "        # Count resources by type\n", "        resource_types = {}\n", "        for entry in everything_results[\"entry\"]:\n", "            resource = entry[\"resource\"]\n", "            resource_type = resource[\"resourceType\"]\n", "            resource_types[resource_type] = resource_types.get(resource_type, 0) + 1\n", "        \n", "        print(\"\\nResource Types:\")\n", "        for resource_type, count in resource_types.items():\n", "            print(f\"{resource_type}: {count}\")\n", "    else:\n", "        print(f\"Could not perform $everything operation for patient {patient_id} or no results found.\")\n", "else:\n", "    print(\"No patient ID available for $everything operation. Using mock data for demonstration.\")\n", "    \n", "    # Create mock $everything results\n", "    mock_everything = {\n", "        \"resourceType\": \"Bundle\",\n", "        \"type\": \"searchset\",\n", "        \"total\": 8,\n", "        \"entry\": [\n", "            {\"resource\": {\"resourceType\": \"Patient\", \"id\": \"mock-patient\"}},\n", "            {\"resource\": {\"resourceType\": \"Observation\", \"id\": \"obs-1\"}},\n", "            {\"resource\": {\"resourceType\": \"Observation\", \"id\": \"obs-2\"}},\n", "            {\"resource\": {\"resourceType\": \"Condition\", \"id\": \"cond-1\"}},\n", "            {\"resource\": {\"resourceType\": \"Encounter\", \"id\": \"enc-1\"}},\n", "            {\"resource\": {\"resourceType\": \"Encounter\", \"id\": \"enc-2\"}},\n", "            {\"resource\": {\"resourceType\": \"MedicationRequest\", \"id\": \"med-1\"}},\n", "            {\"resource\": {\"resourceType\": \"Procedure\", \"id\": \"proc-1\"}}\n", "        ]\n", "    }\n", "    \n", "    print(f\"Using mock $everything results with {len(mock_everything['entry'])} resources\")\n", "    \n", "    # Count resources by type\n", "    resource_types = {}\n", "    for entry in mock_everything[\"entry\"]:\n", "        resource = entry[\"resource\"]\n", "        resource_type = resource[\"resourceType\"]\n", "        resource_types[resource_type] = resource_types.get(resource_type, 0) + 1\n", "    \n", "    print(\"\\nMock Resource Types:\")\n", "    for resource_type, count in resource_types.items():\n", "        print(f\"{resource_type}: {count}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. FHIR Profiles and Implementation Guides\n", "\n", "FHIR is designed to be flexible, but this flexibility can lead to inconsistencies in how it's implemented. To address this, FHIR defines **profiles** and **implementation guides** that constrain and extend the base FHIR resources for specific use cases.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### FHIR Profiles\n", "\n", "A FHIR profile is a set of constraints on a FHIR resource or data type that describes how the resource is used for a particular purpose. Profiles can:\n", "\n", "- Make optional elements required\n", "- Restrict the cardinality of elements\n", "- Constrain the allowed values for elements\n", "- Add extensions for additional data elements\n", "\n", "### Implementation Guides\n", "\n", "An implementation guide (IG) is a collection of profiles, extensions, value sets, and other FHIR artifacts that define how FHIR should be used for a specific purpose or in a specific context. Implementation guides are published by various organizations, including:\n", "\n", "- **US Core**: Defines the minimum set of constraints on FHIR resources for use in the US healthcare system\n", "- **International Patient Summary**: Defines a minimal and non-exhaustive patient summary for use across borders\n", "- **SMART App Launch**: Defines how apps can integrate with EHRs using FHIR\n", "- **Argonaut**: Defines a set of FHIR profiles for use in the US healthcare system\n", "- **Da Vinci**: Defines FHIR profiles for payer-provider exchange\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "### Example: US Core Patient Profile\n", "\n", "The US Core Patient profile is a constraint on the base FHIR Patient resource for use in the US healthcare system. It requires:\n", "\n", "- At least one patient name\n", "- A gender element\n", "- A birth date element (if known)\n", "- Support for race, ethnicity, and birth sex extensions\n", "\n", "Let's create an example of a patient resource that conforms to the US Core Patient profile:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["address:\n", "- city: Anytown\n", "  country: USA\n", "  line:\n", "  - 123 Main St\n", "  postalCode: '12345'\n", "  state: CA\n", "birthDate: '1974-12-25'\n", "extension:\n", "- extension:\n", "  - url: ombCategory\n", "    valueCoding:\n", "      code: 2106-3\n", "      display: White\n", "      system: urn:oid:2.16.840.1.113883.6.238\n", "  - url: text\n", "    valueString: White\n", "  url: http://hl7.org/fhir/us/core/StructureDefinition/us-core-race\n", "- extension:\n", "  - url: ombCategory\n", "    valueCoding:\n", "      code: 2186-5\n", "      display: Not Hispanic or Latino\n", "      system: urn:oid:2.16.840.1.113883.6.238\n", "  - url: text\n", "    valueString: Not Hispanic or Latino\n", "  url: http://hl7.org/fhir/us/core/StructureDefinition/us-core-ethnicity\n", "- url: http://hl7.org/fhir/us/core/StructureDefinition/us-core-birthsex\n", "  valueCode: M\n", "gender: male\n", "id: us-core-patient-example\n", "identifier:\n", "- system: http://hospital.example.org/identifiers/patients\n", "  value: MRN12345\n", "meta:\n", "  profile:\n", "  - http://hl7.org/fhir/us/core/StructureDefinition/us-core-patient\n", "name:\n", "- family: <PERSON>\n", "  given:\n", "  - <PERSON>\n", "  - <PERSON>\n", "resourceType: Patient\n", "telecom:\n", "- system: phone\n", "  use: home\n", "  value: ************\n", "\n"]}], "source": ["# Example of a patient resource conforming to the US Core Patient profile\n", "us_core_patient = {\n", "    \"resourceType\": \"Patient\",\n", "    \"id\": \"us-core-patient-example\",\n", "    \"meta\": {\n", "        \"profile\": [\n", "            \"http://hl7.org/fhir/us/core/StructureDefinition/us-core-patient\"\n", "        ]\n", "    },\n", "    \"extension\": [\n", "        {\n", "            \"url\": \"http://hl7.org/fhir/us/core/StructureDefinition/us-core-race\",\n", "            \"extension\": [\n", "                {\n", "                    \"url\": \"ombCategory\",\n", "                    \"valueCoding\": {\n", "                        \"system\": \"urn:oid:2.16.840.1.113883.6.238\",\n", "                        \"code\": \"2106-3\",\n", "                        \"display\": \"White\"\n", "                    }\n", "                },\n", "                {\n", "                    \"url\": \"text\",\n", "                    \"valueString\": \"White\"\n", "                }\n", "            ]\n", "        },\n", "        {\n", "            \"url\": \"http://hl7.org/fhir/us/core/StructureDefinition/us-core-ethnicity\",\n", "            \"extension\": [\n", "                {\n", "                    \"url\": \"ombCategory\",\n", "                    \"valueCoding\": {\n", "                        \"system\": \"urn:oid:2.16.840.1.113883.6.238\",\n", "                        \"code\": \"2186-5\",\n", "                        \"display\": \"Not Hispanic or Latino\"\n", "                    }\n", "                },\n", "                {\n", "                    \"url\": \"text\",\n", "                    \"valueString\": \"Not Hispanic or Latino\"\n", "                }\n", "            ]\n", "        },\n", "        {\n", "            \"url\": \"http://hl7.org/fhir/us/core/StructureDefinition/us-core-birthsex\",\n", "            \"valueCode\": \"M\"\n", "        }\n", "    ],\n", "    \"identifier\": [\n", "        {\n", "            \"system\": \"http://hospital.example.org/identifiers/patients\",\n", "            \"value\": \"MRN12345\"\n", "        }\n", "    ],\n", "    \"name\": [\n", "        {\n", "            \"family\": \"<PERSON>\",\n", "            \"given\": [\"<PERSON>\", \"<PERSON>\"]\n", "        }\n", "    ],\n", "    \"telecom\": [\n", "        {\n", "            \"system\": \"phone\",\n", "            \"value\": \"************\",\n", "            \"use\": \"home\"\n", "        }\n", "    ],\n", "    \"gender\": \"male\",\n", "    \"birthDate\": \"1974-12-25\",\n", "    \"address\": [\n", "        {\n", "            \"line\": [\"123 Main St\"],\n", "            \"city\": \"Anytown\",\n", "            \"state\": \"CA\",\n", "            \"postalCode\": \"12345\",\n", "            \"country\": \"USA\"\n", "        }\n", "    ]\n", "}\n", "\n", "# Pretty print the US Core Patient resource\n", "# print(json.dumps(us_core_patient, indent=2))\n", "print(yaml.dump(us_core_patient))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. FHIR and Interoperability\n", "\n", "FHIR is designed to support interoperability in healthcare, allowing different systems to exchange and use healthcare data. Let's explore some key aspects of FHIR's role in interoperability:\n", "\n", "### FHIR and APIs\n", "\n", "FHIR's RESTful API approach makes it well-suited for modern web development and integration. FHIR APIs can be used to:\n", "\n", "- Retrieve patient data from EHRs\n", "- Submit clinical data to registries\n", "- Exchange data between healthcare organizations\n", "- Integrate with mobile health apps\n", "- Support clinical decision support systems\n", "\n", "### SMART on FHIR\n", "\n", "SMART (Substitutable Medical Applications, Reusable Technologies) on FHIR is a set of open specifications that integrate apps with EHRs, portals, health information exchanges, and other health IT systems. SMART on FHIR provides:\n", "\n", "- A standard app launch framework\n", "- OAuth 2.0-based authorization\n", "- OpenID Connect-based authentication\n", "- UI integration through HTML5\n", "\n", "### FHIR and Public Health\n", "\n", "FHIR is increasingly being used for public health reporting and surveillance:\n", "\n", "- Electronic case reporting\n", "- Immunization registries\n", "- Syndromic surveillance\n", "- Cancer registries\n", "- Vital records\n", "\n", "### FHIR and Research\n", "\n", "FHIR is also being used to support clinical research:\n", "\n", "- Clinical trial recruitment\n", "- Electronic data capture\n", "- Real-world evidence generation\n", "- Patient-reported outcomes\n", "- Data sharing between research networks\n", "\n", "### FHIR Implementation Challenges\n", "\n", "Despite its advantages, implementing FHIR can present challenges:\n", "\n", "- Varying levels of FHIR adoption across systems\n", "- Different FHIR versions in use\n", "- Inconsistent implementation of profiles\n", "- Limited support for complex workflows\n", "- Security and privacy considerations\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "## 10. Conc<PERSON>\n", "\n", "In this tutorial, we've explored the fundamentals of FHIR, including:\n", "\n", "- The structure and purpose of FHIR resources\n", "- FHIR data types and extensions\n", "- Working with FHIR data using Python\n", "- Searching for FHIR resources\n", "- FHIR operations\n", "- FHIR profiles and implementation guides\n", "- FHIR's role in healthcare interoperability\n", "\n", "FHIR provides a modern, flexible, and powerful framework for healthcare data exchange. By understanding FHIR, you're better equipped to work with healthcare data and build interoperable healthcare applications.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "## 11. Exercises\n", "\n", "To reinforce your understanding of FHIR, try these exercises:\n", "\n", "1. Create a FHIR Condition resource for a patient with diabetes\n", "2. Write a function to extract all Observation resources from a FHIR Bundle\n", "3. Create a visualization of patient conditions by age group\n", "4. Write a function to search for all Observations for a specific patient\n", "5. Create a FHIR MedicationRequest resource that conforms to a specific profile\n", "\n", "## 12. Additional Resources\n", "\n", "- [HL7 FHIR Documentation](https://hl7.org/fhir/)\n", "- [FHIR for Developers](https://www.hl7.org/fhir/modules.html)\n", "- [SMART on FHIR](https://docs.smarthealthit.org/)\n", "- [HAPI FHIR Server](https://hapifhir.io/)\n", "- [FHIR Implementation Guides](https://registry.fhir.org/guides)"]}], "metadata": {"kernelspec": {"display_name": "fhir-omop", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}