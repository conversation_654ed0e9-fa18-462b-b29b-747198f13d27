# OMOP CDM Database - Fundamentals Guide
## Your First Interaction: From Basic Concepts to Complete Mastery

**Date:** July 3, 2025  
**Your Database:** 33.7 million records loaded and operational  
**Goal:** Understand everything from the basics to full mastery  

---

## 🎯 **INCREMENTAL LEARNING INDEX**

### **🏁 LEVEL 1: The Fundamentals** (Start here)
1. [What is OMOP CDM?](#what-is-omop-cdm)
2. [Basic Architecture](#basic-architecture)
3. [The Central Concept: "Concept"](#central-concept)

### **🚀 LEVEL 2: The Structure**
4. [Medical Vocabularies](#medical-vocabularies)
5. [How Data is Related](#how-data-is-related)
6. [Medical Domains](#medical-domains)

### **🎓 LEVEL 3: The Complete System**
7. [Hierarchies and Navigation](#hierarchies-navigation)
8. [Mapping and Standardization](#mapping-standardization)
9. [Real Use Cases](#real-use-cases)

### **🏆 LEVEL 4: Expert Domain**
10. [Advanced Analytics](#advanced-analytics)
11. [Troubleshooting](#troubleshooting)
12. [Next Steps](#next-steps)

---

## 🏁 **LEVEL 1: THE FUNDAMENTALS**

### What is OMOP CDM? {#what-is-omop-cdm}

**OMOP CDM** = **O**bservational **M**edical **O**utcomes **P**artnership **C**ommon **D**ata **M**odel

**In simple terms:**
- 🏥 **A standard way** to organize medical data
- 🌍 **A universal language** so hospitals, researchers, and pharmacies "speak the same"
- 📊 **A database designed** for large-scale health analytics

**Practical analogy:**
Imagine each hospital has its own language for describing diseases:
- Hospital A: "Heart attack"
- Hospital B: "Myocardial infarction"  
- Hospital C: "Ischemic heart disease"

OMOP CDM acts as a **universal translator** that converts all of these to a unique code: `concept_id = 4329847` for "Myocardial infarction"

### Basic Architecture {#basic-architecture}

Your database has **3 types of tables**:

#### **1. 🔥 VOCABULARY TABLES** (The ones that are filled)
```
concept           → 3.28M records → The "universal medical dictionary"
concept_relationship → 15.83M records → How concepts are related
concept_ancestor  → 11.76M records → Medical hierarchies
```

#### **2. ❌ CLINICAL DATA TABLES** (The ones that are empty)
```
person            → 0 records → Patients
condition_occurrence → 0 records → Diagnoses
drug_exposure     → 0 records → Prescribed medications
```

#### **3. 🟡 CONFIGURATION TABLES** (Small but important)
```
vocabulary        → 48 records → The available "dictionaries"
domain           → 50 records → Medical categories
```

**Why this structure?**
- **Vocabularies** = The "brain" that understands medical terminology
- **Clinical data** = The actual patient data (to be loaded later)
- **Configuration** = The "rules of the game"

### The Central Concept: "Concept" {#central-concept}

**The `concept` table is the heart of everything.**

Each record in `concept` represents **a standardized medical term**:

```sql
-- Example of a concept
SELECT concept_id, concept_name, vocabulary_id, domain_id 
FROM concept 
WHERE concept_name = 'Myocardial infarction';

-- Result:
-- concept_id: 4329847
-- concept_name: Myocardial infarction  
-- vocabulary_id: SNOMED
-- domain_id: Condition
```

**What does this mean?**
- `concept_id = 4329847` = The global unique ID for "heart attack"
- `concept_name = 'Myocardial infarction'` = The standard name
- `vocabulary_id = 'SNOMED'` = Comes from the SNOMED CT vocabulary
- `domain_id = 'Condition'` = It's a medical condition

**Your 3.28 million concepts include:**
- 💊 1.82M drugs
- 🏥 266.4K conditions/diseases  
- 🧪 191.6K lab measurements
- ⚕️ 108.2K medical procedures

---

## 🚀 **LEVEL 2: THE STRUCTURE**

### Medical Vocabularies {#medical-vocabularies}

**What are vocabularies?**
They are official "medical dictionaries" created by health organizations.

**Your 48 main vocabularies:**

#### **🩺 SNOMED CT (1.09M concepts)**
- **What it is:** International clinical terminology
- **Created by:** IHTSDO (International Health Terminology Standards Development Organisation)
- **Example:** "Diabetes mellitus type 2" = concept_id 44054006
- **Use:** Diagnoses, procedures, anatomy

#### **💊 NDC (1.25M concepts)**
- **What it is:** National Drug Code (FDA)
- **Created by:** FDA (Food and Drug Administration)
- **Example:** "Metformin 500mg Tablet" = specific concept_id
- **Use:** Prescriptions, pharmacies, drug tracking

#### **🧪 LOINC (274.9K concepts)**
- **What it is:** Logical Observation Identifiers Names and Codes
- **Created by:** Regenstrief Institute
- **Example:** "Hemoglobin A1c" = concept_id 4184637
- **Use:** Labs, blood tests, measurements

#### **📋 ICD-10-CM (99.4K concepts)**
- **What it is:** International Classification of Diseases
- **Created by:** WHO + CMS
- **Example:** "E11.9 - Type 2 diabetes" = specific concept_id
- **Use:** Billing, epidemiology, official reporting

### How Data is Related {#how-data-is-related}

**The `concept_relationship` table (15.83M records) connects everything:**

```sql
-- Example: Which drugs treat diabetes?
SELECT c2.concept_name as medication
FROM concept_relationship cr
JOIN concept c1 ON cr.concept_id_1 = c1.concept_id
JOIN concept c2 ON cr.concept_id_2 = c2.concept_id
WHERE c1.concept_name = 'Diabetes mellitus type 2'
    AND cr.relationship_id = 'Has_treatment';
```

**Main relationship types:**
- **"Maps to"** (4.40M) = Automatic code conversion
- **"Is a"** (3.33M) = Hierarchies (e.g., "Type 2 diabetes" → "Diabetes")
- **"Subsumes"** = Parent-child categories
- **"Has_treatment"** = Drugs for conditions

### Medical Domains {#medical-domains}

**The 5 main domains organize medicine:**

#### **💊 Drug (1.82M concepts - 55.4%)**
```sql
-- All drugs for diabetes
SELECT concept_name FROM concept 
WHERE domain_id = 'Drug' 
    AND concept_name ILIKE '%diabetes%';
```

#### **🏥 Condition (266.4K concepts - 8.1%)**
```sql
-- All cardiovascular diseases
SELECT concept_name FROM concept 
WHERE domain_id = 'Condition' 
    AND concept_name ILIKE '%heart%';
```

#### **🧪 Measurement (191.6K concepts - 5.8%)**
```sql
-- All lab tests
SELECT concept_name FROM concept 
WHERE domain_id = 'Measurement' 
    AND concept_name ILIKE '%blood%';
```

#### **⚕️ Procedure (108.2K concepts - 3.3%)**
```sql
-- All surgical procedures
SELECT concept_name FROM concept 
WHERE domain_id = 'Procedure' 
    AND concept_name ILIKE '%surgery%';
```

#### **📊 Observation (374.2K concepts - 11.4%)**
```sql
-- Clinical and social observations
SELECT concept_name FROM concept 
WHERE domain_id = 'Observation' 
    AND concept_name ILIKE '%smoking%';
```

---

## 🎓 **LEVEL 3: THE COMPLETE SYSTEM**

### Hierarchies and Navigation {#hierarchies-navigation}

**The `concept_ancestor` table (11.76M records) creates hierarchies:**

```sql
-- Example: All types of diabetes
SELECT 
        c.concept_name,
        ca.min_levels_of_separation as levels
FROM concept_ancestor ca
JOIN concept c ON ca.descendant_concept_id = c.concept_id
WHERE ca.ancestor_concept_id = (
        SELECT concept_id FROM concept 
        WHERE concept_name = 'Diabetes mellitus'
)
ORDER BY ca.min_levels_of_separation;
```

**Expected result:**
```
Diabetes mellitus                    → Level 0 (root)
├── Diabetes mellitus type 1         → Level 1
├── Diabetes mellitus type 2         → Level 1
│   ├── Diabetes with nephropathy    → Level 2
│   └── Diabetes with retinopathy    → Level 2
└── Gestational diabetes             → Level 1
```

### Mapping and Standardization {#mapping-standardization}

**The problem OMOP solves:**
- Hospital A uses ICD-10 code: "E11.9"
- Hospital B uses a local code: "DM2"  
- Hospital C uses SNOMED: "44054006"

**All mean:** "Diabetes mellitus type 2"

**The OMOP solution:**
```sql
-- Convert any code to standard
SELECT 
        source_code,
        source_vocabulary_id,
        target_concept_id,
        target_concept_name
FROM source_to_concept_map
WHERE source_code IN ('E11.9', 'DM2', '44054006');
```

**Result:**
```
E11.9    → ICD-10-CM → 201826 → Diabetes mellitus type 2
DM2      → Local     → 201826 → Diabetes mellitus type 2  
44054006 → SNOMED    → 201826 → Diabetes mellitus type 2
```

### Real Use Cases {#real-use-cases}

#### **🔍 Case 1: Smart Search**
```sql
-- Find all concepts related to "heart"
SELECT DISTINCT c.concept_name, c.vocabulary_id, c.domain_id
FROM concept c
LEFT JOIN concept_synonym cs ON c.concept_id = cs.concept_id
WHERE c.concept_name ILIKE '%heart%' 
     OR cs.concept_synonym_name ILIKE '%heart%'
     OR c.concept_name ILIKE '%cardiac%'
     OR c.concept_name ILIKE '%cardio%'
ORDER BY c.domain_id, c.concept_name;
```

#### **🧬 Case 2: Pharmacological Analysis**
```sql
-- Drugs with their dosages for hypertension
SELECT 
        c.concept_name as medication,
        ds.amount_value,
        ds.amount_unit_concept_id,
        u.concept_name as unit
FROM concept c
JOIN drug_strength ds ON c.concept_id = ds.drug_concept_id
JOIN concept u ON ds.amount_unit_concept_id = u.concept_id
WHERE c.concept_name ILIKE '%amlodipine%';
```

#### **🏥 Case 3: Epidemiological Analysis**
```sql
-- Complete hierarchy of cardiovascular diseases
WITH cardiovascular_tree AS (
        SELECT 
                ancestor_concept_id,
                descendant_concept_id,
                min_levels_of_separation
        FROM concept_ancestor
        WHERE ancestor_concept_id = (
                SELECT concept_id FROM concept 
                WHERE concept_name = 'Cardiovascular disease'
        )
)
SELECT 
        c.concept_name,
        c.vocabulary_id,
        ct.min_levels_of_separation as level
FROM cardiovascular_tree ct
JOIN concept c ON ct.descendant_concept_id = c.concept_id
ORDER BY ct.min_levels_of_separation, c.concept_name;
```

---

## 🏆 **LEVEL 4: EXPERT DOMAIN**

### Advanced Analytics {#advanced-analytics}

#### **📊 Vocabulary Analytics**
```sql
-- Coverage analysis by vocabulary
SELECT 
        v.vocabulary_name,
        COUNT(*) as total_concepts,
        COUNT(CASE WHEN c.standard_concept = 'S' THEN 1 END) as standard_concepts,
        ROUND(
                COUNT(CASE WHEN c.standard_concept = 'S' THEN 1 END) * 100.0 / COUNT(*), 
                2
        ) as percent_standard
FROM concept c
JOIN vocabulary v ON c.vocabulary_id = v.vocabulary_id
GROUP BY v.vocabulary_name
ORDER BY total_concepts DESC;
```

#### **🔗 Relationship Analysis**
```sql
-- Relationship density by domain
SELECT 
        d.domain_name,
        COUNT(DISTINCT c.concept_id) as concepts,
        COUNT(cr.concept_id_1) as total_relationships,
        ROUND(
                COUNT(cr.concept_id_1) * 1.0 / COUNT(DISTINCT c.concept_id), 
                2
        ) as avg_relationships_per_concept
FROM concept c
JOIN domain d ON c.domain_id = d.domain_id
LEFT JOIN concept_relationship cr ON c.concept_id = cr.concept_id_1
GROUP BY d.domain_name
ORDER BY avg_relationships_per_concept DESC;
```

### Troubleshooting {#troubleshooting}

#### **🔍 Common Problems and Solutions**

**Problem 1: "I can't find a concept"**
```sql
-- Broad search with synonyms
SELECT 
        c.concept_id,
        c.concept_name,
        c.vocabulary_id,
        'Main name' as source
FROM concept c
WHERE c.concept_name ILIKE '%your_term%'

UNION ALL

SELECT 
        c.concept_id,
        c.concept_name,
        c.vocabulary_id,
        'Synonym' as source
FROM concept c
JOIN concept_synonym cs ON c.concept_id = cs.concept_id
WHERE cs.concept_synonym_name ILIKE '%your_term%';
```

**Problem 2: "Which standard code should I use?"**
```sql
-- Find the standard concept for a term
SELECT 
        c.concept_id,
        c.concept_name,
        c.standard_concept,
        c.vocabulary_id
FROM concept c
WHERE c.concept_name ILIKE '%your_term%'
    AND c.standard_concept = 'S'  -- Only standard concepts
    AND c.invalid_reason IS NULL;  -- Only valid concepts
```

**Problem 3: "How do I convert local codes?"**
```sql
-- Map local codes to standard
SELECT 
        scm.source_code,
        scm.source_vocabulary_id,
        c.concept_id as target_concept_id,
        c.concept_name as target_concept_name
FROM source_to_concept_map scm
JOIN concept c ON scm.target_concept_id = c.concept_id
WHERE scm.source_code = 'your_local_code';
```

### Next Steps {#next-steps}

#### **🎯 Current Phase: Vocabularies Loaded ✅**
Your database is in the perfect state to start analysis.

#### **🚀 Next Level: Load Clinical Data**
```bash
# Use the production Abu Dhabi Claims ETL
abu-dhabi-etl

# Or run directly
cd src/fhir_omop/etl/abu_dhabi_claims_mvp
./quick_start.sh clean-run
```

#### **📊 Future: Advanced Analytics**
- Interactive dashboards
- Machine learning on medical data
- Regulatory reporting
- Clinical research

---

## 🎉 **SUMMARY OF YOUR ACHIEVED DOMAIN**

### **✅ You Now Understand:**
1. **What OMOP CDM is** and why it's revolutionary
2. **The architecture** of vocabularies vs clinical data
3. **The 48 vocabularies** and their specific purposes
4. **The 5 medical domains** and how to navigate them
5. **Hierarchies** and how to roll up data
6. **Automatic mapping** of local to standard codes
7. **Complex queries** for real-world analysis

### **💡 Your Competitive Advantage:**
- **Knowledge base**: 3.28M medical concepts
- **Interoperability**: 48 standard vocabularies
- **Scalability**: Globally proven architecture
- **Analysis ready**: Optimized queries available

### **🔥 Immediate Use Cases:**
- Smart search of medical terminology
- Pharmacological analysis with dosages
- Mapping local codes to international standards
- Hierarchical navigation of medical conditions
- Vocabulary coverage analytics

---

## 📚 **ADDITIONAL RESOURCES**

### **📖 Supplementary Documentation:**
- `docs/guides/omop/` - Detailed technical guides
- `docs/tutorials/` - Step-by-step tutorials and learning materials
- `docs/research/` - Research materials and implementation analysis
- `docs/mappings/` - Specific mappings by vocabulary

### **🔧 Reference Scripts:**
- `create_database.py` - How your database was created
- `load_vocabularies.py` - How vocabularies were loaded
- `database_checker.py` - Integrity checks

### **🌐 Community:**
- [OHDSI Forums](https://forums.ohdsi.org/) - Global community
- [OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/) - Official documentation

---

**Congratulations!** 🎉 You have gone from beginner to solid mastery of OMOP CDM. Your database with 33.7 million records is ready for any health analysis you can imagine.

**Next step?** Ask me about any specific concept or let's start exploring practical use cases with your data.

*Document created July 3, 2025*  
*Your OMOP CDM database: Fully operational and ready for analysis*
