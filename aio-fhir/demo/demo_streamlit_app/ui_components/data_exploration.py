"""
Data Exploration UI Components for the FHIR Server Demo Streamlit application.

This module provides comprehensive UI components for exploring and visualizing FHIR data
through the Streamlit application. It includes multiple exploration approaches:

1. Basic Query: Execute predefined or custom FHIR queries with tabular and JSON results
2. Patient Explorer: Patient-centric exploration with demographics and clinical data
3. Clinical Data Explorer: Advanced clinical data exploration with specialized visualizations

Features:
- Resource-specific query builders with clinical context
- Interactive data filtering by category, code, date range, and status
- Tabular data presentation with download capabilities
- Data visualizations for different resource types:
  - Observation: Time series, histograms, and specialized blood pressure charts
  - Condition: Distribution analysis, clinical status breakdown, and temporal trends
- Export functionality for further analysis
- Comprehensive query help and guidance

The module is designed to support clinical data scientists and researchers in exploring
FHIR data with a focus on usability and meaningful data presentation.
"""

import streamlit as st
import logging
import sys
from pathlib import Path
import pandas as pd  # Used in various functions for data manipulation

# Add the parent directory to the Python path to import utils
sys.path.append(str(Path(__file__).parent.parent))

# Import utility functions
from utils import (
    execute_fhir_query,
    format_fhir_bundle_as_dataframe,
    export_query_results
)

# Create a logger for this module
logger = logging.getLogger(__name__)

def render_data_exploration_tab():
    """
    Render the Data Exploration tab in the Streamlit application.

    This function handles the UI for exploring FHIR data through API queries.
    """
    st.header("Data Exploration via API")
    st.markdown("Execute FHIR API queries to explore the loaded data.")

    # Create tabs for different exploration approaches
    exploration_tabs = st.tabs([
        "Basic Query",
        "Patient Explorer",
        "Clinical Data"
    ])

    # Tab 1: Basic Query (similar to original functionality but enhanced)
    with exploration_tabs[0]:
        render_basic_query_section()

    # Tab 2: Patient Explorer
    with exploration_tabs[1]:
        render_patient_explorer_section()

    # Tab 3: Clinical Data
    with exploration_tabs[2]:
        render_clinical_data_section()

def render_basic_query_section():
    """
    Render the basic query section with enhanced predefined queries.
    """
    # Enhanced predefined queries with more clinical relevance
    predefined_queries = {
        "List all patients": "Patient",
        "Count patients": "Patient?_summary=count",
        "Male patients": "Patient?gender=male",
        "Female patients": "Patient?gender=female",
        "Patients born after 2000": "Patient?birthdate=gt2000-01-01",
        "Patients with recent observations": "Observation?_sort=-date&_count=20",
        "Blood pressure measurements": "Observation?code=http://loinc.org|85354-9",
        "Heart rate measurements": "Observation?code=http://loinc.org|8867-4",
        "Body weight measurements": "Observation?code=http://loinc.org|29463-7",
        "Body height measurements": "Observation?code=http://loinc.org|8302-2",
        "BMI measurements": "Observation?code=http://loinc.org|39156-5",
        "Active conditions": "Condition?clinical-status=active",
        "Recent encounters": "Encounter?date=gt2020-01-01&_sort=-date",
        "Medication requests": "MedicationRequest?status=active"
    }

    # Query options
    query_option = st.radio(
        "Query type:",
        ["Predefined query", "Custom query"]
    )

    if query_option == "Predefined query":
        selected_query = st.selectbox(
            "Select query:",
            list(predefined_queries.keys())
        )
        query_path = predefined_queries[selected_query]
    else:
        query_path = st.text_input(
            "FHIR query path:",
            placeholder="Example: Patient?gender=female&_count=10"
        )

        # Add a help section for custom queries
        with st.expander("FHIR Query Help"):
            st.markdown("""
            ### Common FHIR Search Parameters

            - **_id**: Search by resource id
            - **_count**: Limit the number of results
            - **_sort**: Sort the results (prefix with '-' for descending)
            - **_include**: Include referenced resources

            ### Resource-specific Parameters

            **Patient**:
            - `gender`: Patient gender (male, female, other, unknown)
            - `birthdate`: Patient birth date (e.g., eq2000-01-01, gt1990, lt2005)
            - `name`: Patient name (supports partial matches)
            - `family`: Family name
            - `given`: Given name

            **Observation**:
            - `patient`: Reference to patient (e.g., Patient/123)
            - `code`: Observation code (e.g., http://loinc.org|85354-9)
            - `date`: Observation date
            - `value-quantity`: Numeric value with optional comparator

            **Condition**:
            - `patient`: Reference to patient
            - `clinical-status`: Status (active, recurrence, relapse, inactive, remission, resolved)
            - `code`: Condition code
            - `onset-date`: When condition started

            **Encounter**:
            - `patient`: Reference to patient
            - `date`: Encounter date
            - `status`: Encounter status
            """)

    # Execute query button
    if st.button("Execute Query", type="primary"):
        if query_path:
            logger.info(f"Executing query: {query_path} on server: {st.session_state.server_url}")
            with st.spinner(f"Executing query: {query_path}..."):
                success, result = execute_fhir_query(st.session_state.server_url, query_path)

            if success:
                logger.info("Query executed successfully")
                st.success("✅ Query executed successfully")

                # Show result count if it's a bundle
                if "resourceType" in result and result["resourceType"] == "Bundle":
                    total = result.get("total", None)
                    entry_count = len(result.get("entry", []))
                    logger.info(f"Query results: {entry_count} resources found (total: {total if total else 'not provided'})")

                    # Create columns for metrics with more useful information
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("Resources Found", entry_count)

                    # Instead of showing "unknown" total, show something more useful
                    with col2:
                        # Get unique resource types
                        resource_types = set()
                        if "entry" in result:
                            for entry in result["entry"]:
                                if "resource" in entry and "resourceType" in entry["resource"]:
                                    resource_types.add(entry["resource"]["resourceType"])

                        # Show the resource type if there's only one type
                        if len(resource_types) == 1:
                            resource_type = next(iter(resource_types))
                            st.metric("Resource Type", resource_type)
                        else:
                            st.metric("Resource Types", len(resource_types))

                    # Add a third metric with useful information
                    with col3:
                        # For observations, count unique patients
                        unique_patients = set()
                        if "entry" in result:
                            for entry in result["entry"]:
                                if "resource" in entry and "subject" in entry["resource"]:
                                    subject_ref = entry["resource"]["subject"].get("reference", "")
                                    if subject_ref.startswith("Patient/"):
                                        unique_patients.add(subject_ref)

                        if unique_patients:
                            st.metric("Unique Patients", len(unique_patients))
                        elif total is not None:
                            # Only show total if it's actually provided by the server
                            st.metric("Total in Database", total)

                    # Try to convert to DataFrame for tabular visualization
                    if "entry" in result and result["entry"]:
                        logger.info("Converting bundle to DataFrame for tabular view")
                        df = format_fhir_bundle_as_dataframe(result)
                        if not df.empty:
                            st.subheader("Tabular View")
                            st.dataframe(df, use_container_width=True)

                            # Add download button for the dataframe
                            csv = df.to_csv(index=False).encode('utf-8')
                            st.download_button(
                                "Download as CSV",
                                csv,
                                "query_results.csv",
                                "text/csv",
                                key='download-csv'
                            )

                # Show complete JSON
                with st.expander("JSON Response"):
                    st.json(result)

                # Export options
                st.subheader("Export Results")
                export_format = st.radio("Export format:", ["CSV", "JSON"])

                if st.button("Export Results"):
                    logger.info(f"Exporting results in {export_format} format")
                    export_success, filename = export_query_results(result, format=export_format.lower())

                    if export_success:
                        logger.info(f"Data exported successfully to: {filename}")
                        st.success(f"✅ Data exported successfully to: {filename}")
                    else:
                        logger.error(f"Error exporting data: {filename}")
                        st.error(f"❌ Error exporting data: {filename}")
            else:
                logger.error(f"Error executing query: {result}")
                st.error(f"❌ Error executing query: {result}")
        else:
            logger.warning("No valid query path provided")
            st.warning("⚠️ Please enter a valid query path")

def render_patient_explorer_section():
    """
    Render the patient explorer section for patient-centric analysis.
    """
    st.subheader("Patient Explorer")
    st.markdown("Explore patient data and related clinical information.")

    # First, get a list of patients
    with st.spinner("Loading patient list..."):
        success, result = execute_fhir_query(st.session_state.server_url, "Patient?_count=100")

    if not success or "entry" not in result:
        st.error("❌ Failed to load patient list. Please check server connection.")
        return

    # Extract patient information for the selector
    patients = []
    for entry in result.get("entry", []):
        resource = entry.get("resource", {})
        patient_id = resource.get("id", "")

        # Get patient name
        name = "Unknown"
        if "name" in resource and resource["name"]:
            name_obj = resource["name"][0]
            family = name_obj.get("family", "")
            given = " ".join(name_obj.get("given", []))
            name = f"{given} {family}".strip()
            if not name:
                name = "Unknown"

        # Get gender and birth date
        gender = resource.get("gender", "unknown")
        birth_date = resource.get("birthDate", "Unknown")

        patients.append({
            "id": patient_id,
            "name": name,
            "gender": gender,
            "birthDate": birth_date,
            "display": f"{name} (ID: {patient_id}, {gender}, DOB: {birth_date})"
        })

    # Create patient selector
    selected_patient = st.selectbox(
        "Select a patient:",
        options=[p["display"] for p in patients],
        index=0
    )

    # Get the selected patient ID
    selected_idx = [p["display"] for p in patients].index(selected_patient)
    patient_id = patients[selected_idx]["id"]

    # Display patient information
    st.markdown(f"### Selected Patient: {patients[selected_idx]['name']}")

    # Create tabs for different patient data
    patient_data_tabs = st.tabs([
        "Demographics",
        "Observations",
        "Conditions",
        "Encounters"
    ])

    # Tab 1: Demographics
    with patient_data_tabs[0]:
        st.markdown("#### Patient Demographics")

        # Get full patient data
        success, patient_data = execute_fhir_query(
            st.session_state.server_url,
            f"Patient/{patient_id}"
        )

        if success:
            # Display key demographics in a more structured way
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**Basic Information**")
                st.markdown(f"**ID:** {patient_data.get('id', 'Unknown')}")
                st.markdown(f"**Gender:** {patient_data.get('gender', 'Unknown')}")
                st.markdown(f"**Birth Date:** {patient_data.get('birthDate', 'Unknown')}")

                # Check if patient is deceased
                if "deceasedBoolean" in patient_data and patient_data["deceasedBoolean"]:
                    st.markdown("**Status:** Deceased")
                elif "deceasedDateTime" in patient_data:
                    st.markdown(f"**Status:** Deceased on {patient_data['deceasedDateTime']}")
                else:
                    st.markdown("**Status:** Living")

            with col2:
                st.markdown("**Contact Information**")

                # Display telecom information if available
                if "telecom" in patient_data and patient_data["telecom"]:
                    for telecom in patient_data["telecom"]:
                        system = telecom.get("system", "unknown")
                        value = telecom.get("value", "")
                        st.markdown(f"**{system.capitalize()}:** {value}")
                else:
                    st.markdown("No contact information available")

                # Display address if available
                if "address" in patient_data and patient_data["address"]:
                    address = patient_data["address"][0]
                    address_parts = []

                    if "line" in address:
                        address_parts.extend(address["line"])
                    if "city" in address:
                        address_parts.append(address["city"])
                    if "state" in address:
                        address_parts.append(address["state"])
                    if "postalCode" in address:
                        address_parts.append(address["postalCode"])

                    st.markdown(f"**Address:** {', '.join(address_parts)}")
                else:
                    st.markdown("No address information available")

            # Display full patient data in JSON format
            with st.expander("View Complete Patient Data"):
                st.json(patient_data)
        else:
            st.error(f"❌ Error retrieving patient data: {patient_data}")

def render_clinical_data_section():
    """
    Render the clinical data section for exploring clinical data across patients.

    This function provides a comprehensive interface for exploring and visualizing
    different types of clinical data:

    - Observation: Supports filtering by category, code, and date range with
      visualizations including time series, histograms, and specialized blood
      pressure charts.

    - Condition: Supports filtering by type, clinical status, and date range with
      visualizations showing condition distribution, clinical status breakdown,
      and temporal trends.

    - Encounter: Supports filtering by type, date range, and status.

    - Procedure and MedicationRequest: Basic filtering and tabular display.

    Each resource type has specialized query builders and visualization options
    tailored to the clinical context of the data.
    """
    st.subheader("Clinical Data Explorer")
    st.markdown("Explore clinical data across all patients with a focus on clinical analysis.")

    # Create a container for the main query builder
    with st.container():
        # Step 1: Select Resource Type (Required)
        st.markdown("### Step 1: Select Data Type (Required)")

        # Resource type selection with clinical descriptions
        resource_options = {
            "Observation": "Lab results, vital signs, and other measurements",
            "Condition": "Diagnoses, problems, and health conditions",
            "Encounter": "Patient visits and hospitalizations",
            "Procedure": "Clinical procedures and interventions",
            "MedicationRequest": "Prescribed medications and treatments"
        }

        resource_type = st.selectbox(
            "Select clinical data type:",
            list(resource_options.keys()),
            format_func=lambda x: f"{x} - {resource_options[x]}"
        )

        st.markdown("---")

    # Add filters based on resource type
    filters = {}

    if resource_type == "Observation":
        # Step 2: Select Clinical Measurement Type
        st.markdown("### Step 2: Select Measurement Type")

        # Create tabs for different ways to filter
        filter_tab1, filter_tab2 = st.tabs(["📊 By Category (Recommended)", "🔍 By Code/Description"])

        with filter_tab1:
            # Predefined clinical observation categories
            observation_categories = [
                "Select a category...",
                "Vital Signs",
                "Laboratory",
                "Imaging",
                "Social History",
                "Exam",
                "Survey"
            ]

            selected_category = st.selectbox("Observation category:", observation_categories)

            if selected_category != "Select a category...":
                # Ensure category uses hyphens instead of spaces for FHIR compatibility
                category_value = selected_category.lower().replace(" ", "-")
                filters["category"] = category_value
                st.success(f"✅ Filter applied: Category = {selected_category}")

            # Common clinical codes based on category
            common_codes = {}

            if selected_category == "Vital Signs":
                common_codes = {
                    "Select a measurement...": "",
                    "Blood Pressure": "85354-9",
                    "Heart Rate": "8867-4",
                    "Respiratory Rate": "9279-1",
                    "Temperature": "8310-5",
                    "Oxygen Saturation": "59408-5",
                    "Body Weight": "29463-7",
                    "Body Height": "8302-2",
                    "BMI": "39156-5"
                }
                st.info("💡 Vital signs include measurements like blood pressure, heart rate, and body measurements.")
            elif selected_category == "Laboratory":
                common_codes = {
                    "Select a test...": "",
                    "Complete Blood Count": "58410-2",
                    "Glucose": "2339-0",
                    "Hemoglobin A1c": "4548-4",
                    "Lipid Panel": "57698-3",
                    "Liver Function": "57247-9",
                    "Creatinine": "2160-0",
                    "Sodium": "2951-2",
                    "Potassium": "2823-3"
                }
                st.info("💡 Laboratory tests include blood tests, chemistry panels, and other diagnostic measurements.")

            if common_codes:
                selected_code_name = st.selectbox("Select specific measurement:", list(common_codes.keys()))
                selected_code = common_codes[selected_code_name]

                if selected_code:
                    filters["code"] = f"http://loinc.org|{selected_code}"
                    st.success(f"✅ Filter applied: Measurement = {selected_code_name} (LOINC: {selected_code})")

        with filter_tab2:
            st.markdown("Enter a LOINC code or description to search")

            # Custom code filter
            custom_code = st.text_input(
                "Enter LOINC code or description:",
                placeholder="e.g., 29463-7, 8302-2, glucose, weight"
            )

            if custom_code:
                # Check if it looks like a LOINC code (numbers with a dash)
                import re
                if re.match(r'^\d+-\d+$', custom_code):
                    filters["code"] = custom_code
                    st.success(f"✅ Filter applied: LOINC Code = {custom_code}")
                else:
                    filters["code:contains"] = custom_code
                    st.success(f"✅ Filter applied: Description contains '{custom_code}'")

            st.info("💡 LOINC codes are standardized identifiers for laboratory and clinical observations. Examples: 8302-2 (Height), 29463-7 (Weight), 8867-4 (Heart rate)")

        # Step 3: Date Range (Optional)
        st.markdown("### Step 3: Date Range (Optional)")

        date_col1, date_col2 = st.columns(2)
        with date_col1:
            date_from = st.date_input("Date from:", value=None)
            if date_from:
                filters["date"] = f"ge{date_from}"
                st.success(f"✅ Filter applied: Date from {date_from}")

        with date_col2:
            date_to = st.date_input("Date to:", value=None)
            if date_to and date_from:
                filters["date"] = f"ge{date_from}&date=le{date_to}"
                st.success(f"✅ Filter applied: Date range {date_from} to {date_to}")
            elif date_to:
                filters["date"] = f"le{date_to}"
                st.success(f"✅ Filter applied: Date until {date_to}")

    elif resource_type == "Condition":
        # Step 2: Select Condition Type
        st.markdown("### Step 2: Select Condition Type")

        # Create tabs for different ways to filter
        filter_tab1, filter_tab2 = st.tabs(["📊 By Category (Recommended)", "🔍 By Code/Description"])

        with filter_tab1:
            # Common clinical condition categories
            condition_categories = [
                "Select a category...",
                "Cardiovascular",
                "Respiratory",
                "Endocrine",
                "Gastrointestinal",
                "Musculoskeletal",
                "Neurological",
                "Mental Health",
                "Infectious Disease"
            ]

            selected_category = st.selectbox("Condition category:", condition_categories)

            if selected_category != "Select a category...":
                st.success(f"✅ Filter applied: Category = {selected_category}")

            # Common clinical codes based on category
            common_codes = {}

            if selected_category == "Cardiovascular":
                common_codes = {
                    "Select a condition...": "",
                    "Hypertension": "59621000",
                    "Myocardial Infarction": "22298006",
                    "Heart Failure": "84114007",
                    "Atrial Fibrillation": "49436004"
                }
                st.info("💡 Cardiovascular conditions include heart and blood vessel disorders.")
            elif selected_category == "Endocrine":
                common_codes = {
                    "Select a condition...": "",
                    "Diabetes Type 2": "44054006",
                    "Diabetes Type 1": "46635009",
                    "Hypothyroidism": "40930008",
                    "Obesity": "414916001"
                }
                st.info("💡 Endocrine conditions include disorders of the hormone-producing glands.")

            if common_codes:
                selected_code_name = st.selectbox("Select specific condition:", list(common_codes.keys()))
                selected_code = common_codes[selected_code_name]

                if selected_code:
                    filters["code"] = f"http://snomed.info/sct|{selected_code}"
                    st.success(f"✅ Filter applied: Condition = {selected_code_name} (SNOMED: {selected_code})")

        with filter_tab2:
            st.markdown("Enter a SNOMED code or condition description to search")

            # Custom code filter
            custom_code = st.text_input(
                "Enter SNOMED code or description:",
                placeholder="e.g., diabetes, hypertension, asthma"
            )

            if custom_code:
                # Check if it looks like a SNOMED code (all digits)
                import re
                if re.match(r'^\d+$', custom_code):
                    filters["code"] = f"http://snomed.info/sct|{custom_code}"
                    st.success(f"✅ Filter applied: SNOMED Code = {custom_code}")
                else:
                    filters["code:contains"] = custom_code
                    st.success(f"✅ Filter applied: Description contains '{custom_code}'")

            st.info("💡 SNOMED CT codes are standardized identifiers for clinical conditions. Examples: 44054006 (Diabetes Type 2), 59621000 (Hypertension)")

        # Step 3: Clinical Status (Optional)
        st.markdown("### Step 3: Clinical Status (Optional)")
        status_options = ["active", "recurrence", "relapse", "inactive", "remission", "resolved"]
        selected_status = st.multiselect("Status:", status_options)
        if selected_status:
            filters["clinical-status"] = ",".join(selected_status)
            st.success(f"✅ Filter applied: Clinical Status = {', '.join(selected_status)}")

    elif resource_type == "Encounter":
        # Step 2: Select Encounter Type
        st.markdown("### Step 2: Select Encounter Type")

        # Common encounter types
        encounter_types = [
            "Select a type...",
            "Inpatient",
            "Outpatient",
            "Emergency",
            "Ambulatory",
            "Home Visit",
            "Virtual"
        ]

        selected_type = st.selectbox("Encounter type:", encounter_types)

        if selected_type != "Select a type...":
            # Map to SNOMED codes
            type_codes = {
                "Inpatient": "183452005",
                "Outpatient": "270427003",
                "Emergency": "50849002",
                "Ambulatory": "270398006",
                "Home Visit": "439708006",
                "Virtual": "185317003"
            }

            if selected_type in type_codes:
                # First try a simpler approach that might work better with our data
                filters["type"] = selected_type
                st.success(f"✅ Filter applied: Encounter Type = {selected_type}")
                st.info(f"💡 Note: Encounter filtering is based on the type field, not the class field as in standard FHIR")

        # Step 3: Encounter Status (Optional)
        st.markdown("### Step 3: Encounter Status (Optional)")
        status_options = ["planned", "arrived", "triaged", "in-progress", "onleave", "finished", "cancelled"]
        selected_status = st.multiselect("Status:", status_options)
        if selected_status:
            filters["status"] = ",".join(selected_status)
            st.success(f"✅ Filter applied: Encounter Status = {', '.join(selected_status)}")

        # Step 4: Date Range (Optional)
        st.markdown("### Step 4: Date Range (Optional)")
        date_col1, date_col2 = st.columns(2)
        with date_col1:
            date_from = st.date_input("Date from:", value=None, key="encounter_date_from")
            if date_from:
                filters["date"] = f"ge{date_from}"
                st.success(f"✅ Filter applied: Date from {date_from}")

        with date_col2:
            date_to = st.date_input("Date to:", value=None, key="encounter_date_to")
            if date_to and date_from:
                filters["date"] = f"ge{date_from}&date=le{date_to}"
                st.success(f"✅ Filter applied: Date range {date_from} to {date_to}")
            elif date_to:
                filters["date"] = f"le{date_to}"
                st.success(f"✅ Filter applied: Date until {date_to}")

    # Query Options (Step 4 for most resource types, Step 5 for Encounter)
    step_number = 5 if resource_type == "Encounter" else 4
    st.markdown(f"### Step {step_number}: Query Options")

    option_col1, option_col2 = st.columns(2)

    with option_col1:
        limit = st.slider("Maximum results:", min_value=10, max_value=500, value=50, step=10)
        filters["_count"] = str(limit)

    with option_col2:
        # Add sort option
        sort_by = st.selectbox(
            "Sort by:",
            ["Most recent first", "Oldest first", "ID"]
        )

        # Use resource-specific sort parameters
        if resource_type == "Condition":
            if sort_by == "Most recent first":
                filters["_sort"] = "-onset-date"
            elif sort_by == "Oldest first":
                filters["_sort"] = "onset-date"
            elif sort_by == "ID":
                filters["_sort"] = "_id"
        else:
            # Default sort parameters for other resource types
            if sort_by == "Most recent first":
                filters["_sort"] = "-date"
            elif sort_by == "Oldest first":
                filters["_sort"] = "date"
            elif sort_by == "ID":
                filters["_sort"] = "_id"

    # Include patient data option
    include_patient = st.checkbox("Include patient details", value=True,
                                 help="Includes patient information with each resource")
    if include_patient:
        # Use the correct _include format based on resource type
        if resource_type == "Observation":
            filters["_include"] = "Observation:patient"
        elif resource_type == "Condition":
            filters["_include"] = "Condition:patient"
        elif resource_type == "Encounter":
            filters["_include"] = "Encounter:patient"
        elif resource_type == "Procedure":
            filters["_include"] = "Procedure:patient"
        elif resource_type == "MedicationRequest":
            filters["_include"] = "MedicationRequest:patient"
        else:
            # Fallback for other resource types
            filters["_include"] = f"{resource_type}:patient"

    # Build the query string
    query = resource_type
    if filters:
        query_params = "&".join([f"{k}={v}" for k, v in filters.items()])
        query = f"{resource_type}?{query_params}"

    # Display the query
    with st.expander("View Generated FHIR Query"):
        st.code(query)

    # Summary of applied filters
    st.markdown("### Summary of Applied Filters")

    if len(filters) <= 1:  # Only _count filter
        st.warning("⚠️ No specific filters applied. This will return all resources of the selected type.")
    else:
        filter_summary = []
        for k, v in filters.items():
            if k != "_count" and k != "_sort" and k != "_include":
                filter_summary.append(f"- **{k}**: {v}")

        if filter_summary:
            st.success("✅ Filters applied:\n" + "\n".join(filter_summary))

    # Execute query button
    execute_step = 6 if resource_type == "Encounter" else 5
    st.markdown(f"### Step {execute_step}: Execute Query")
    if st.button("Execute Clinical Query", type="primary", use_container_width=True):
        with st.spinner(f"Executing query: {query}..."):
            success, result = execute_fhir_query(st.session_state.server_url, query)

        if success:
            # Show result count if it's a bundle
            if "resourceType" in result and result["resourceType"] == "Bundle":
                total = result.get("total", None)
                entry_count = len(result.get("entry", []))

                # Create columns for metrics with more useful information
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Resources Found", entry_count)

                # Get unique resource types
                resource_types = set()
                if "entry" in result:
                    for entry in result["entry"]:
                        if "resource" in entry and "resourceType" in entry["resource"]:
                            resource_types.add(entry["resource"]["resourceType"])

                with col2:
                    # Show the resource type if there's only one type
                    if len(resource_types) == 1:
                        resource_type_name = next(iter(resource_types))
                        st.metric("Resource Type", resource_type_name)
                    else:
                        st.metric("Resource Types", len(resource_types))

                with col3:
                    # Count unique patients
                    unique_patients = set()
                    if "entry" in result:
                        for entry in result["entry"]:
                            if "resource" in entry and "subject" in entry["resource"]:
                                subject_ref = entry["resource"]["subject"].get("reference", "")
                                if subject_ref.startswith("Patient/"):
                                    unique_patients.add(subject_ref)

                    if unique_patients:
                        st.metric("Unique Patients", len(unique_patients))
                    elif total is not None:
                        # Only show total if it's actually provided by the server
                        st.metric("Total in Database", total)

                # Try to convert to DataFrame for tabular visualization
                if "entry" in result and result["entry"]:
                    logger.info("Converting bundle to DataFrame for tabular view")
                    df = format_fhir_bundle_as_dataframe(result)
                    if not df.empty:
                        st.subheader("Tabular View")
                        st.dataframe(df, use_container_width=True)

                        # Add visualization based on resource type
                        # Initialize variables for blood pressure visualization
                        bp_columns = []

                        if resource_type == "Observation" and len(df) > 0:
                            # Import visualization libraries
                            import plotly.express as px
                            import re

                            try:
                                # Check if this is a blood pressure observation
                                # Look for blood pressure component columns
                                for col in df.columns:
                                    if col.lower() in ["systolic_blood_pressure", "diastolic_blood_pressure"]:
                                        bp_columns.append(col)
                            except Exception as e:
                                st.warning(f"Could not initialize visualization: {str(e)}")

                        elif resource_type == "Condition" and "entry" in result:
                            # Extract Condition resources for visualization
                            condition_resources = []
                            for entry in result["entry"]:
                                if "resource" in entry and entry["resource"]["resourceType"] == "Condition":
                                    condition_resources.append(entry["resource"])

                            if condition_resources:
                                # Define the visualization function at the top of the file
                                def visualize_condition_distribution(resources):
                                    """Create visualizations for Condition resources."""
                                    try:
                                        import plotly.express as px
                                        from collections import Counter
                                        import pandas as pd
                                        from datetime import datetime

                                        st.subheader("Condition Distribution Analysis")

                                        # Extract condition data
                                        conditions = []
                                        clinical_statuses = []
                                        onset_years = []

                                        for resource in resources:
                                            # Extract condition name
                                            condition_name = "Unknown"
                                            condition_code = "Unknown"
                                            if "code" in resource and "coding" in resource["code"] and len(resource["code"]["coding"]) > 0:
                                                condition_name = resource["code"]["coding"][0].get("display", "Unknown")
                                                condition_code = resource["code"]["coding"][0].get("code", "Unknown")

                                            # Extract clinical status
                                            clinical_status = "Unknown"
                                            if "clinicalStatus" in resource and "coding" in resource["clinicalStatus"] and len(resource["clinicalStatus"]["coding"]) > 0:
                                                clinical_status = resource["clinicalStatus"]["coding"][0].get("code", "Unknown")

                                            # Extract onset year
                                            onset_year = None
                                            if "onsetDateTime" in resource:
                                                try:
                                                    onset_date = datetime.fromisoformat(resource["onsetDateTime"].replace('Z', '+00:00'))
                                                    onset_year = onset_date.year
                                                except (ValueError, TypeError):
                                                    pass

                                            conditions.append((condition_name, condition_code))
                                            clinical_statuses.append(clinical_status)
                                            if onset_year:
                                                onset_years.append(onset_year)

                                        # Create tabs for different visualizations
                                        tab1, tab2, tab3 = st.tabs(["Condition Types", "Clinical Status", "Temporal Trends"])

                                        with tab1:
                                            # Count condition occurrences
                                            condition_counts = Counter(conditions)

                                            # Convert to DataFrame for visualization
                                            condition_df = pd.DataFrame([
                                                {"Condition": name, "Code": code, "Count": count}
                                                for (name, code), count in condition_counts.most_common(15)
                                            ])

                                            if not condition_df.empty:
                                                # Create bar chart of top conditions
                                                fig = px.bar(
                                                    condition_df,
                                                    x="Count",
                                                    y="Condition",
                                                    title="Top 15 Conditions",
                                                    orientation='h',
                                                    labels={"Count": "Number of Patients", "Condition": "Condition Name"}
                                                )

                                                # Improve layout
                                                fig.update_layout(
                                                    yaxis={'categoryorder':'total ascending'},
                                                    hovermode="y unified"
                                                )

                                                st.plotly_chart(fig, use_container_width=True)

                                                # Add a data table with the counts
                                                st.markdown("#### Condition Counts")
                                                st.dataframe(condition_df, use_container_width=True)
                                            else:
                                                st.info("No condition data available for visualization.")

                                        with tab2:
                                            # Count clinical status occurrences
                                            status_counts = Counter(clinical_statuses)

                                            # Convert to DataFrame for visualization
                                            status_df = pd.DataFrame([
                                                {"Status": status, "Count": count}
                                                for status, count in status_counts.most_common()
                                            ])

                                            if not status_df.empty:
                                                # Create pie chart of clinical statuses
                                                fig = px.pie(
                                                    status_df,
                                                    values="Count",
                                                    names="Status",
                                                    title="Distribution of Clinical Statuses",
                                                    hole=0.4
                                                )

                                                # Improve layout
                                                fig.update_layout(
                                                    legend_title="Clinical Status"
                                                )

                                                st.plotly_chart(fig, use_container_width=True)

                                                # Add a data table with the counts
                                                st.markdown("#### Clinical Status Counts")
                                                st.dataframe(status_df, use_container_width=True)
                                            else:
                                                st.info("No clinical status data available for visualization.")

                                        with tab3:
                                            if onset_years:
                                                # Count onset years
                                                year_counts = Counter(onset_years)

                                                # Convert to DataFrame for visualization
                                                year_df = pd.DataFrame([
                                                    {"Year": year, "Count": count}
                                                    for year, count in sorted(year_counts.items())
                                                ])

                                                # Create line chart of onset years
                                                fig = px.line(
                                                    year_df,
                                                    x="Year",
                                                    y="Count",
                                                    title="Condition Onset Trends Over Time",
                                                    markers=True,
                                                    labels={"Count": "Number of New Conditions", "Year": "Year"}
                                                )

                                                # Improve layout
                                                fig.update_layout(
                                                    hovermode="x unified"
                                                )

                                                st.plotly_chart(fig, use_container_width=True)

                                                # Add a data table with the counts
                                                st.markdown("#### Condition Onset by Year")
                                                st.dataframe(year_df, use_container_width=True)
                                            else:
                                                st.info("No temporal data available for visualization.")

                                    except Exception as e:
                                        st.warning(f"Could not create condition visualizations: {str(e)}")
                                        import traceback
                                        st.error(traceback.format_exc())

                                # Call the visualization function
                                visualize_condition_distribution(condition_resources)

                                # Check if dataframe has value column for visualization
                                if "value" in df.columns:
                                    try:
                                        # Handle regular observations with a single value
                                        # Try to extract numeric values for visualization
                                        numeric_values = []
                                        for val in df["value"]:
                                            if isinstance(val, str):
                                                # Try to extract numeric part from strings like "120 mmHg"
                                                match = re.search(r'(\d+(\.\d+)?)', val)
                                                if match:
                                                    numeric_values.append(float(match.group(1)))
                                                else:
                                                    numeric_values.append(None)
                                            elif isinstance(val, (int, float)):
                                                numeric_values.append(float(val))
                                            else:
                                                numeric_values.append(None)

                                        df["numeric_value"] = numeric_values

                                        # Filter out non-numeric values
                                        plot_df = df.dropna(subset=["numeric_value"])

                                        if len(plot_df) > 1:
                                            st.subheader("Data Visualization")

                                            if "effectiveDateTime" in plot_df.columns:
                                                # Time series plot
                                                fig = px.line(
                                                    plot_df,
                                                    x="effectiveDateTime",
                                                    y="numeric_value",
                                                    title=f"Trend of {resource_type} Values Over Time",
                                                    labels={"numeric_value": "Value", "effectiveDateTime": "Date"}
                                                )
                                                st.plotly_chart(fig, use_container_width=True)

                                            # Distribution plot
                                            fig = px.histogram(
                                                plot_df,
                                                x="numeric_value",
                                                title=f"Distribution of {resource_type} Values",
                                                labels={"numeric_value": "Value"}
                                            )
                                            st.plotly_chart(fig, use_container_width=True)
                                    except Exception as viz_error:
                                        st.warning(f"Could not create visualization: {str(viz_error)}")

                        # Add download button for the dataframe
                        csv = df.to_csv(index=False).encode('utf-8')
                        st.download_button(
                            "Download as CSV",
                            csv,
                            f"{resource_type.lower()}_results.csv",
                            "text/csv",
                            key='download-clinical-csv'
                        )

                # Show complete JSON
                with st.expander("JSON Response"):
                    st.json(result)
        else:
            st.error(f"❌ Error executing query: {result}")
