# OMOP Vocabulary Management Guide

This comprehensive guide covers everything you need to know about OMOP vocabularies, from understanding their purpose to loading them into your database.

## 🔗 Prerequisites

Before working with vocabularies, ensure you have completed the database setup:

> **📋 Required**: [PostgreSQL OMOP Database Setup](../database/postgresql_setup.md) must be completed first.

Your database should have:
- ✅ 39 OMOP CDM tables created
- ✅ User `omop` with proper permissions  
- ✅ Environment variables configured
- ✅ Connection verified

## 📚 Documentation Structure

This vocabulary guide is organized into focused sections:

### 1. [Overview](overview.md) - Understanding OMOP Vocabularies
- **What are vocabularies** and why they're critical
- **Vocabulary tables** and their relationships
- **Standard vs source concepts** explained
- **Domains and concept classes** overview

### 2. [Athena Setup](athena_setup.md) - Downloading Vocabularies  
- **Account registration** on OHDSI Athena
- **Vocabulary selection** for your use case
- **Download process** and file formats
- **Licensing considerations** for different vocabularies

### 3. [Loading Guide](loading.md) - Database Integration
- **Automated loading** with Python scripts
- **Manual loading** with PostgreSQL commands  
- **Verification procedures** to ensure success
- **Troubleshooting** common issues

## 🚀 **Quick Start Workflow**

For users who have completed the PostgreSQL setup:

### Step 1: Download Vocabularies from Athena
Follow the detailed guide: [Athena Setup](athena_setup.md)
- Register at https://athena.ohdsi.org/
- Select required vocabularies (SNOMED, LOINC, RxNorm, CPT4, etc.)
- Download and extract to `data/vocabulary/omop_v5_YYYYMMDD/`

### Step 2: Reconstitute CPT4 (if selected)
```bash
# Navigate to vocabulary directory
cd data/vocabulary/omop_v5_20250630  # Use your actual date

# Run CPT4 reconstitution with your UMLS API key
chmod +x cpt.sh
./cpt.sh YOUR_UMLS_API_KEY

# Verify CPT4 concepts added
grep -c "CPT4" CONCEPT.csv  # Should show ~17,750
```

### Step 3: Load Vocabularies Using Official OHDSI Method
```bash
# Run the official OHDSI vocabulary loading script
python scripts/load_vocabularies.py
```

**Expected Results**: ~33M records loaded in 7-15 minutes using the official OHDSI method.

### Step 4: Verify Installation
```bash
# Check vocabulary distribution
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT vocabulary_id, COUNT(*) FROM concept GROUP BY vocabulary_id ORDER BY COUNT(*) DESC LIMIT 10;"
```

## 📊 Expected Results

After successful vocabulary loading (v20250630), you should have:

| Vocabulary | Concept Count | Purpose |
|------------|---------------|---------|
| **NDC** | 1,254,857 | National Drug Code (FDA drug identifiers) |
| **SNOMED** | 1,089,088 | Clinical conditions, procedures, findings |
| **RxNorm** | 311,332 | Normalized medication names |
| **LOINC** | 274,904 | Laboratory tests, measurements, observations |
| **ICD10CM** | 99,421 | Clinical modification diagnoses |
| **CPT4** | 17,749 | Current Procedural Terminology (reconstituted) |
| **ICD10** | 16,638 | International disease classification |
| **ATC** | 7,223 | Anatomical Therapeutic Chemical classification |
| **Gender** | ~10 | Patient gender values |
| **Race** | ~50 | Patient race/ethnicity values |

**Total Statistics:**
- **Total Concepts**: ~3,280,000
- **Total Relationships**: ~15,800,000
- **Total Ancestors**: ~11,700,000
- **Database Size**: ~2-3 GB (with indexes)

## 🔄 **Integration with FHIR-to-OMOP**

Once vocabularies are loaded, your database is ready for:

- **FHIR resource mapping** using standard concepts
- **ETL processes** that transform source codes to OMOP concepts
- **Analytics queries** across standardized healthcare data
- **Interoperability** with other OMOP databases worldwide

### Current Implementation
The project includes functional FHIR-to-OMOP mappers:
- `src/fhir_omop/mappers/patient_mapper.py` - Patient → Person
- `src/fhir_omop/mappers/encounter_mapper.py` - Encounter → Visit_Occurrence
- `src/fhir_omop/mappers/condition_mapper.py` - Condition → Condition_Occurrence
- `src/fhir_omop/mappers/observation_mapper.py` - Observation → Measurement/Observation

### ETL Pipeline Example
- `src/fhir_omop/etl/abu_dhabi_claims_mvp/` - Complete claims-to-OMOP ETL pipeline

## 📖 **Additional Resources**

- **[OHDSI Athena](https://athena.ohdsi.org/)** - Official vocabulary portal
- **[OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/)** - Complete specification
- **[OHDSI Forums](https://forums.ohdsi.org/)** - Community support
- **[Vocabulary Tables Overview PDF](OMOP-Vocabulary-Tables-Overview_lowres_2022-11-02.pdf)** - Visual reference

## 🆘 **Troubleshooting**

If you encounter issues:

1. **Check Prerequisites**: Ensure PostgreSQL setup is complete
2. **Review Script Output**: Check for specific error messages
3. **Verify Files**: Ensure Athena files are properly downloaded and extracted
4. **Environment**: Confirm `.env` configuration is correct
5. **Database Permissions**: Ensure user has DDL privileges for constraint management

**Common Issues**: See [Vocabulary Loading Guide](loading.md#troubleshooting) for detailed troubleshooting.

---

> **Next Steps**: After vocabulary loading, explore the ETL pipeline in `src/fhir_omop/etl/abu_dhabi_claims_mvp/` to see FHIR-to-OMOP transformation in action.
