# Technical Requirements for FHIR to OMOP Transformation Pipeline

This document outlines the technical requirements for implementing a FHIR to OMOP CDM transformation pipeline, including software, hardware, and configuration specifications.

> **Implementation Status Legend:**
> - ✅ **IMPLEMENTED** – Functional and production-ready
> - 🚧 **IN DEVELOPMENT** – Currently being implemented  
> - 📋 **PLANNED** – Scheduled for future implementation

## 1. Database Requirements ✅

This project supports two database options for the OMOP CDM: PostgreSQL (recommended for production) and SQLite (recommended for development). For detailed setup instructions, see our [OMOP Database Setup Guides](../guides/omop/database/overview.md).

### 1.1 PostgreSQL Configuration for OMOP CDM ✅

#### Software Requirements
- **PostgreSQL Version**: 14.0 or higher (currently implemented)
- **Extensions**:
  - `pg_trgm` for trigram-based text search
  - `btree_gin` for GIN index support
  - `tablefunc` for crosstab functions
  - `pgcrypto` for cryptographic functions

#### Recommended Configuration
```ini
# Memory Configuration
shared_buffers = 4GB                  # 25% of available RAM, adjust accordingly
work_mem = 256MB                      # For complex queries
maintenance_work_mem = 1GB            # For maintenance operations
effective_cache_size = 12GB           # 75% of available RAM, adjust accordingly

# Query Optimization
random_page_cost = 1.1                # For SSD storage
effective_io_concurrency = 200        # For SSD storage
max_parallel_workers_per_gather = 4   # Adjust based on CPU cores
max_parallel_workers = 8              # Adjust based on CPU cores
max_worker_processes = 8              # Adjust based on CPU cores

# Write Ahead Log
wal_buffers = 16MB                    # Recommended for high write loads
checkpoint_completion_target = 0.9    # Spread out checkpoint writes
max_wal_size = 2GB                    # Adjust based on storage capacity

# Vacuum and Autovacuum
autovacuum = on
autovacuum_vacuum_scale_factor = 0.05 # More aggressive for large tables
autovacuum_analyze_scale_factor = 0.025
```

#### OMOP CDM Schema Setup ✅
- OMOP CDM v5.4.2 schema scripts available at: https://github.com/OHDSI/CommonDataModel
- Recommended indexes for optimized query performance
- Partitioning strategy for large tables (e.g., OBSERVATION, MEASUREMENT) 📋

#### Database Sizing Guidelines 📋
- **Base Storage**: Minimum 500GB for medium-sized datasets
- **Growth Factor**: Plan for 2-3x growth from raw FHIR data to OMOP CDM
- **Vocabulary Storage**: Approximately 30GB for full OMOP vocabulary set
- **Temp Space**: At least 100GB for ETL operations

### 1.2 SQLite Configuration for OMOP CDM (Development) ✅

#### Software Requirements
- **SQLite Version**: 3.8.0 or higher
- **Python**: 3.8+ or higher with sqlite3 module (currently implemented with 3.11)

#### Advantages for Development
- No installation required (file-based database)
- Portable (single file that can be easily shared)
- Minimal configuration and administration
- Perfect for local development and testing

#### Limitations
- Not officially supported by OHDSI
- Performance limitations with large datasets
- Not suitable for multi-user environments
- Limited advanced database features

#### Recommended Use Cases
- Early development stages
- Learning the OMOP CDM structure
- Testing ETL logic with small datasets
- Portable development environment

#### Database Sizing Guidelines
- **Base Storage**: Minimum 1GB for small test datasets
- **Growth Factor**: Similar to PostgreSQL, plan for 2-3x growth
- **Vocabulary Storage**: Consider using a subset of vocabularies (1-5GB)
- **Performance Consideration**: Database performance degrades significantly above 10GB

### 1.3 Vocabulary Service Configuration ✅

#### Software Requirements ✅
- **Athena Download**: Latest vocabulary set from https://athena.ohdsi.org/
- **Usagi**: For custom mapping development (Java 8+) 📋

#### Vocabulary Loading Scripts ✅
```sql
-- Example script for loading vocabularies
CREATE SCHEMA IF NOT EXISTS vocabulary;

-- Create tables based on OMOP CDM vocabulary schema
CREATE TABLE vocabulary.concept (...);
CREATE TABLE vocabulary.concept_relationship (...);
-- Additional tables as per OMOP CDM vocabulary schema

-- Load data from vocabulary files
COPY vocabulary.concept FROM '/path/to/vocabulary/CONCEPT.csv' WITH CSV HEADER DELIMITER E'\t';
COPY vocabulary.concept_relationship FROM '/path/to/vocabulary/CONCEPT_RELATIONSHIP.csv' WITH CSV HEADER DELIMITER E'\t';
-- Load remaining vocabulary tables

-- Create indexes
CREATE INDEX idx_concept_concept_id ON vocabulary.concept (concept_id);
CREATE INDEX idx_concept_code ON vocabulary.concept (concept_code);
-- Additional indexes for performance
```

## 2. Application Server Requirements 🚧

### 2.1 ETL Application Server 🚧

#### Software Requirements 🚧
- **Operating System**: Linux (Ubuntu 20.04 LTS or higher recommended)
- **Java**: OpenJDK 11 or higher 📋
- **Python**: 3.8 or higher ✅ (currently using 3.11 with conda)
- **Node.js**: 14.x or higher (if using JavaScript-based tools) 📋
- **Docker**: 20.10.x or higher ✅
- **Docker Compose**: 2.x or higher ✅

#### Hardware Recommendations 📋
- **CPU**: 8+ cores for medium workloads
- **Memory**: 32GB+ RAM
- **Storage**: 500GB+ SSD
- **Network**: 1Gbps+ for data transfer between components

#### Python Environment Setup ✅
```bash
### 5.2 Development Environment Commands 🚧

#### Environment Setup ✅
```bash
# 1. Create conda environment (already implemented) ✅
conda env create -f environment.yml
conda activate fhir-omop

# 2. Start FHIR Server (already implemented) ✅
cd servers/fhir-server
./start-fhir-server.sh

# 3. Start OMOP Database (already implemented) ✅
cd ../omop-database
./manage-omop-database.sh start
```

#### Required Python Packages 📋
```
# The following packages are planned for future ETL implementation
# Current environment defined in environment.yml at project root
fhir.resources>=6.0.0
sqlalchemy>=1.4.0
psycopg2-binary>=2.9.0
pandas>=1.3.0
numpy>=1.20.0
requests>=2.25.0
pydantic>=1.8.0
pytest>=6.2.0
python-dotenv>=0.19.0
tqdm>=4.62.0
loguru>=0.5.0
fastapi>=0.68.0 📋
uvicorn>=0.15.0 📋
```

### 2.2 FHIR Server Configuration ✅

#### Software Options ✅
- **HAPI FHIR Server**: ✅ (currently implemented)
  - Java 11+
  - PostgreSQL 14 database ✅
  - 8GB+ RAM
  - Configuration for bulk data export ✅

- **IBM FHIR Server**: 📋 (alternative option)
  - Java 11+
  - Liberty profile
  - 8GB+ RAM
  - Support for FHIR R4

#### Docker Configuration for HAPI FHIR Server ✅
```yaml
# docker-compose.yml excerpt for HAPI FHIR Server
version: '3.8'
services:
  hapi-fhir-server:
    image: hapiproject/hapi:latest
    ports:
      - "8080:8080"
    environment:
      - hapi.fhir.default_encoding=json
      - hapi.fhir.bulk_export_enabled=true
      - hapi.fhir.subscription.resthook_enabled=true
      - hapi.fhir.implementationguides.core_enabled=true
      - spring.datasource.url=**********************************************
      - spring.datasource.username=admin
      - spring.datasource.password=admin
      - spring.datasource.driverClassName=org.postgresql.Driver
    volumes:
      - hapi-data:/data/hapi
    depends_on:
      - hapi-fhir-postgres
    restart: unless-stopped

  hapi-fhir-postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=hapi
      - POSTGRES_USER=admin
      - POSTGRES_PASSWORD=admin
    volumes:
      - hapi-postgres-data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  hapi-data:
  hapi-postgres-data:
```

## 3. ETL Middleware Requirements 📋

### 3.1 Apache Airflow Configuration 📋

#### Software Requirements 📋
- **Airflow Version**: 2.3.0 or higher
- **Executor**: CeleryExecutor for distributed processing
- **Database Backend**: PostgreSQL
- **Message Broker**: Redis or RabbitMQ

#### Docker Configuration
```yaml
# docker-compose.yml excerpt for Airflow
version: '3.8'
services:
  postgres:
    image: postgres:13
    environment:
      - POSTGRES_USER=airflow
      - POSTGRES_PASSWORD=airflow
      - POSTGRES_DB=airflow
    volumes:
      - postgres-db-volume:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "airflow"]
      interval: 5s
      retries: 5
    restart: always

  redis:
    image: redis:latest
    expose:
      - 6379
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 30s
      retries: 50
    restart: always

  airflow-webserver:
    image: apache/airflow:2.3.0
    depends_on:
      - postgres
      - redis
    environment:
      - AIRFLOW__CORE__EXECUTOR=CeleryExecutor
      - AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=postgresql+psycopg2://airflow:airflow@postgres/airflow
      - AIRFLOW__CELERY__RESULT_BACKEND=db+*********************************************
      - AIRFLOW__CELERY__BROKER_URL=redis://:@redis:6379/0
    volumes:
      - ./dags:/opt/airflow/dags
      - ./plugins:/opt/airflow/plugins
      - airflow-logs-volume:/opt/airflow/logs
    ports:
      - "8080:8080"
    command: webserver
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8080/health"]
      interval: 10s
      timeout: 10s
      retries: 5
    restart: always

  airflow-scheduler:
    image: apache/airflow:2.3.0
    depends_on:
      - postgres
      - redis
    environment:
      - AIRFLOW__CORE__EXECUTOR=CeleryExecutor
      - AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=postgresql+psycopg2://airflow:airflow@postgres/airflow
      - AIRFLOW__CELERY__RESULT_BACKEND=db+*********************************************
      - AIRFLOW__CELERY__BROKER_URL=redis://:@redis:6379/0
    volumes:
      - ./dags:/opt/airflow/dags
      - ./plugins:/opt/airflow/plugins
      - airflow-logs-volume:/opt/airflow/logs
    command: scheduler
    restart: always

  airflow-worker:
    image: apache/airflow:2.3.0
    depends_on:
      - postgres
      - redis
    environment:
      - AIRFLOW__CORE__EXECUTOR=CeleryExecutor
      - AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=postgresql+psycopg2://airflow:airflow@postgres/airflow
      - AIRFLOW__CELERY__RESULT_BACKEND=db+*********************************************
      - AIRFLOW__CELERY__BROKER_URL=redis://:@redis:6379/0
    volumes:
      - ./dags:/opt/airflow/dags
      - ./plugins:/opt/airflow/plugins
      - airflow-logs-volume:/opt/airflow/logs
    command: celery worker
    restart: always

volumes:
  postgres-db-volume:
  airflow-logs-volume:
```

### 3.2 Spring Batch Configuration (Alternative) 📋

#### Software Requirements 📋
- **Java**: OpenJDK 11 or higher
- **Spring Boot**: 2.6.x or higher
- **Spring Batch**: 4.3.x or higher
- **Database**: PostgreSQL for job repository

#### Application Properties
```properties
# application.properties
spring.datasource.url=*****************************************
spring.datasource.username=batch_user
spring.datasource.password=batch_password
spring.datasource.driver-class-name=org.postgresql.Driver

spring.batch.job.enabled=false
spring.batch.jdbc.initialize-schema=always

# Batch configuration
spring.batch.chunk-size=1000
spring.batch.max-workers=4

# FHIR server configuration
fhir.server.base-url=http://localhost:8080/fhir
fhir.server.username=fhir_user
fhir.server.password=fhir_password

# OMOP database configuration
omop.datasource.url=*****************************************
omop.datasource.username=omop_user
omop.datasource.password=omop_password
omop.datasource.driver-class-name=org.postgresql.Driver
```

## 4. Monitoring and Logging Requirements 📋

### 4.1 ELK Stack Configuration 📋

#### Software Requirements 📋
- **Elasticsearch**: 7.x or higher
- **Logstash**: 7.x or higher
- **Kibana**: 7.x or higher
- **Filebeat**: 7.x or higher (for log shipping)

#### Docker Configuration
```yaml
# docker-compose.yml excerpt for ELK Stack
version: '3.8'
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.14.0
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms1g -Xmx1g
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    restart: unless-stopped

  logstash:
    image: docker.elastic.co/logstash/logstash:7.14.0
    volumes:
      - ./logstash/pipeline:/usr/share/logstash/pipeline
    ports:
      - "5044:5044"
    depends_on:
      - elasticsearch
    restart: unless-stopped

  kibana:
    image: docker.elastic.co/kibana/kibana:7.14.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    restart: unless-stopped

volumes:
  elasticsearch-data:
```

### 4.2 Prometheus and Grafana Configuration 📋

#### Software Requirements 📋
- **Prometheus**: 2.30.x or higher
- **Grafana**: 8.x or higher
- **Node Exporter**: For system metrics
- **PostgreSQL Exporter**: For database metrics

#### Docker Configuration
```yaml
# docker-compose.yml excerpt for Prometheus and Grafana
version: '3.8'
services:
  prometheus:
    image: prom/prometheus:v2.30.3
    volumes:
      - ./prometheus:/etc/prometheus
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    ports:
      - "9090:9090"
    restart: unless-stopped

  node-exporter:
    image: prom/node-exporter:latest
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.ignored-mount-points=^/(sys|proc|dev|host|etc)($$|/)'
    ports:
      - "9100:9100"
    restart: unless-stopped

  postgres-exporter:
    image: wrouesnel/postgres_exporter:latest
    environment:
      - DATA_SOURCE_NAME=********************************************/postgres?sslmode=disable
    ports:
      - "9187:9187"
    restart: unless-stopped

  grafana:
    image: grafana/grafana:8.2.0
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    restart: unless-stopped

volumes:
  prometheus-data:
  grafana-data:
```

## 5. Development and Testing Environment 🚧

### 5.1 Complete Docker Compose Configuration 🚧

The following Docker Compose configuration sets up a complete development and testing environment for the FHIR to OMOP transformation pipeline:

```yaml
# docker-compose.yml for development environment
# Note: Currently implemented: FHIR Server + OMOP Database
# Future: ETL App + Airflow integration
version: '3.8'
services:
  # FHIR Server ✅
  hapi-fhir-server:
    image: hapiproject/hapi:latest
    ports:
      - "8080:8080"
    environment:
      - hapi.fhir.default_encoding=json
      - hapi.fhir.bulk_export_enabled=true
      - spring.datasource.url=**********************************************
      - spring.datasource.username=admin
      - spring.datasource.password=admin
    volumes:
      - hapi-data:/data/hapi
    depends_on:
      - hapi-fhir-postgres
    restart: unless-stopped

  hapi-fhir-postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=hapi
      - POSTGRES_USER=admin
      - POSTGRES_PASSWORD=admin
    volumes:
      - hapi-postgres-data:/var/lib/postgresql/data
    restart: unless-stopped

  # OMOP CDM Database ✅
  omop-postgres:
    image: postgres:13
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=omop_cdm
      - POSTGRES_USER=omop_admin
      - POSTGRES_PASSWORD=omop_password
    volumes:
      - omop-postgres-data:/var/lib/postgresql/data
      - ./sql/omop_cdm_schema.sql:/docker-entrypoint-initdb.d/01_omop_cdm_schema.sql
      - ./sql/omop_vocabulary_load.sql:/docker-entrypoint-initdb.d/02_omop_vocabulary_load.sql
    restart: unless-stopped

  # ETL Application (Future Implementation) 📋
  etl-app:
    build:
      context: ./etl-app
      dockerfile: Dockerfile
    volumes:
      - ./etl-app:/app
      - ./data:/data
    environment:
      - FHIR_SERVER_URL=http://hapi-fhir-server:8080/fhir
      - OMOP_DB_URL=*********************************************
      - OMOP_DB_USERNAME=omop_admin
      - OMOP_DB_PASSWORD=omop_password
    depends_on:
      - hapi-fhir-server
      - omop-postgres
    restart: unless-stopped

  # Airflow for ETL Orchestration (Future Implementation) 📋
  postgres-airflow:
    image: postgres:13
    environment:
      - POSTGRES_USER=airflow
      - POSTGRES_PASSWORD=airflow
      - POSTGRES_DB=airflow
    volumes:
      - postgres-airflow-volume:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:latest
    restart: unless-stopped

  airflow-webserver:
    image: apache/airflow:2.3.0
    depends_on:
      - postgres-airflow
      - redis
    environment:
      - AIRFLOW__CORE__EXECUTOR=CeleryExecutor
      - AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=postgresql+psycopg2://airflow:airflow@postgres-airflow/airflow
      - AIRFLOW__CELERY__RESULT_BACKEND=db+*****************************************************
      - AIRFLOW__CELERY__BROKER_URL=redis://:@redis:6379/0
    volumes:
      - ./airflow/dags:/opt/airflow/dags
      - ./airflow/plugins:/opt/airflow/plugins
      - airflow-logs-volume:/opt/airflow/logs
    ports:
      - "8081:8080"
    command: webserver
    restart: unless-stopped

  airflow-scheduler:
    image: apache/airflow:2.3.0
    depends_on:
      - postgres-airflow
      - redis
    environment:
      - AIRFLOW__CORE__EXECUTOR=CeleryExecutor
      - AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=postgresql+psycopg2://airflow:airflow@postgres-airflow/airflow
      - AIRFLOW__CELERY__RESULT_BACKEND=db+*****************************************************
      - AIRFLOW__CELERY__BROKER_URL=redis://:@redis:6379/0
    volumes:
      - ./airflow/dags:/opt/airflow/dags
      - ./airflow/plugins:/opt/airflow/plugins
      - airflow-logs-volume:/opt/airflow/logs
    command: schedule
(Content truncated due to size limit. Use line ranges to read in chunks)