// PatientParser.java from NACHC-CAD/fhir-to-omop repository
// Source: https://github.com/NACHC-CAD/fhir-to-omop/blob/main/src/main/java/org/nachc/tools/fhirtoomop/fhir/parser/patient/PatientParser.java

package org.nachc.tools.fhirtoomop.fhir.parser.patient;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.hl7.fhir.dstu3.model.Coding;
import org.hl7.fhir.dstu3.model.Enumerations.AdministrativeGender;
import org.hl7.fhir.dstu3.model.Extension;
import org.hl7.fhir.dstu3.model.HumanName;
import org.hl7.fhir.dstu3.model.Patient;
import org.nachc.tools.fhirtoomop.fhir.parser.coding.CodingParser;
import org.nachc.tools.fhirtoomop.fhir.parser.extension.ExtensionParser;
import org.yaorma.util.time.TimeUtil;

import com.nach.core.util.string.StringUtil;

public class PatientParser {

    private Patient patient;

    public PatientParser(Patient patient) {
        this.patient = patient;
    }

    public Patient getPatient() {
        return this.patient;
    }

    public String getId() {
        try {
            String patientId = patient.getIdElement().getIdPart();
            return patientId;
        } catch (Exception exp) {
            return null;
        }
    }

    public String getFirstName() {
        List<HumanName> nameList = patient.getName();
        if(nameList == null || nameList.size() == 0) {
            return null;
        }
        HumanName name = nameList.get(0);
        if(name == null) {
            return null;
        }
        String rtn = name.getGivenAsSingleString();
        return rtn;
    }

    public String getLastName() {
        List<HumanName> nameList = patient.getName();
        if(nameList == null || nameList.size() == 0) {
            return null;
        }
        HumanName name = nameList.get(0);
        if(name == null) {
            return null;
        }
        String rtn = name.getFamily();
        return rtn;
    }

    public Coding getRace() {
        ExtensionParser ex = getExtension("http://hl7.org/fhir/us/core/StructureDefinition/us-core-race");
        Coding rtn = ex.getCoding();
        return rtn;
    }

    public Coding getEthnicity() {
        ExtensionParser ex = getExtension("http://hl7.org/fhir/us/core/StructureDefinition/us-core-ethnicity");
        Coding rtn = ex.getCoding();
        return rtn;
    }

    public String getGender() {
        AdministrativeGender gender = patient.getGender();
        if(gender == null) {
            return null;
        }
        String rtn = gender.getDisplay();
        return rtn;
    }

    public Date getBirthDate() {
        Date rtn = patient.getBirthDate();
        return rtn;
    }

    public String getBirthDateAsString() {
        Date birthDate = getBirthDate();
        if(birthDate == null) {
            return null;
        }
        String rtn = TimeUtil.getFormattedDate(birthDate);
        return rtn;
    }

    private ExtensionParser getExtension(String url) {
        List<Extension> extList = patient.getExtension();
        if(extList == null) {
            return new ExtensionParser(null);
        }
        for(Extension ext : extList) {
            String extUrl = ext.getUrl();
            if(StringUtil.isEqual(url, extUrl)) {
                return new ExtensionParser(ext);
            }
        }
        return new ExtensionParser(null);
    }

}
