# Postman Setup and Usage

This section covers the installation of Postman and how to use it to interact with the FHIR server.

## Stage 5: <PERSON><PERSON><PERSON> and <PERSON><PERSON> Postman

**Objective**: Install Postman and learn how to use it to interact with the FHIR server.

### Step 1: Install Postman

**For macOS**:
1. Download Postman from [Postman's official website](https://www.postman.com/downloads/)
2. Install the downloaded .dmg file by dragging it to the Applications folder
3. Launch Postman from the Applications folder

**For Windows**:
1. Download Postman from [Postman's official website](https://www.postman.com/downloads/)
2. Run the installer and follow the prompts
3. Launch Postman from the Start menu

**For Linux (Ubuntu)**:
```bash
# Download Postman
wget https://dl.pstmn.io/download/latest/linux64 -O postman.tar.gz

# Extract the archive
sudo tar -xzf postman.tar.gz -C /opt

# Create a symbolic link
sudo ln -s /opt/Postman/Postman /usr/bin/postman

# Create a desktop entry
cat > ~/.local/share/applications/postman.desktop << EOL
[Desktop Entry]
Encoding=UTF-8
Name=Postman
Exec=/opt/Postman/Postman
Icon=/opt/Postman/app/resources/app/assets/icon.png
Terminal=false
Type=Application
Categories=Development;
EOL

# Launch Postman
postman
```

### Step 2: Postman Basics

**Postman Terminology**:
- **Collection**: A group of related API requests
- **Request**: A single HTTP request to an API endpoint
- **Environment**: A set of variables that can be used across requests
- **Variables**: Key-value pairs that can be used in requests
- **Tests**: JavaScript code to validate responses
- **Pre-request Scripts**: JavaScript code to run before a request is sent

**Creating a FHIR Collection**:
1. Open Postman
2. Click "Create a Collection" or the "+" button next to Collections
3. Name the collection "HAPI FHIR Server"
4. Click "Create"

**Setting Up Environment Variables**:
1. Click on "Environments" in the sidebar
2. Click "Create Environment"
3. Name the environment "HAPI FHIR Local"
4. Add the following variables:
   - `fhir_server`: `http://localhost:8080/fhir`
   - `content_type`: `application/fhir+json`
5. Click "Save"
6. Select the "HAPI FHIR Local" environment from the dropdown in the top right

## Stage 6: Create FHIR Resource Examples with Postman

**Objective**: Create documentation and examples for interacting with FHIR resources using Postman.

### Step 1: Create a Patient Resource

**Create a new request**:
1. In the "HAPI FHIR Server" collection, click "Add request"
2. Name it "Create Patient"
3. Set the method to "POST"
4. Set the URL to `{{fhir_server}}/Patient`
5. Go to the "Headers" tab and add:
   - `Content-Type`: `{{content_type}}`
6. Go to the "Body" tab, select "raw", and set the format to "JSON"
7. Add the following JSON:
```json
{
  "resourceType": "Patient",
  "active": true,
  "name": [
    {
      "use": "official",
      "family": "Smith",
      "given": ["John"]
    }
  ],
  "gender": "male",
  "birthDate": "1970-01-01",
  "address": [
    {
      "use": "home",
      "line": ["123 Main St"],
      "city": "Anytown",
      "state": "CA",
      "postalCode": "12345",
      "country": "USA"
    }
  ],
  "telecom": [
    {
      "system": "phone",
      "value": "************",
      "use": "home"
    },
    {
      "system": "email",
      "value": "<EMAIL>"
    }
  ]
}
```
8. Click "Save"
9. Click "Send" to create the patient

**Add a test script**:
1. Go to the "Tests" tab
2. Add the following JavaScript:
```javascript
// Check if the request was successful
pm.test("Status code is 201 Created", function() {
    pm.response.to.have.status(201);
});

// Save the patient ID for later use
if (pm.response.code === 201) {
    var jsonData = pm.response.json();
    pm.environment.set("patient_id", jsonData.id);
    console.log("Patient ID: " + jsonData.id);
}
```
3. Click "Save" and "Send" again

### Step 2: Read a Patient Resource

**Create a new request**:
1. In the "HAPI FHIR Server" collection, click "Add request"
2. Name it "Read Patient"
3. Set the method to "GET"
4. Set the URL to `{{fhir_server}}/Patient/{{patient_id}}`
5. Go to the "Headers" tab and add:
   - `Accept`: `{{content_type}}`
6. Click "Save"
7. Click "Send" to retrieve the patient

### Step 3: Search for Patients

**Create a new request**:
1. In the "HAPI FHIR Server" collection, click "Add request"
2. Name it "Search Patients"
3. Set the method to "GET"
4. Set the URL to `{{fhir_server}}/Patient?family=Smith`
5. Go to the "Headers" tab and add:
   - `Accept`: `{{content_type}}`
6. Click "Save"
7. Click "Send" to search for patients

### Step 4: Update a Patient Resource

**Create a new request**:
1. In the "HAPI FHIR Server" collection, click "Add request"
2. Name it "Update Patient"
3. Set the method to "PUT"
4. Set the URL to `{{fhir_server}}/Patient/{{patient_id}}`
5. Go to the "Headers" tab and add:
   - `Content-Type`: `{{content_type}}`
6. Go to the "Body" tab, select "raw", and set the format to "JSON"
7. Add the following JSON:
```json
{
  "resourceType": "Patient",
  "id": "{{patient_id}}",
  "active": true,
  "name": [
    {
      "use": "official",
      "family": "Smith",
      "given": ["John", "Robert"]
    }
  ],
  "gender": "male",
  "birthDate": "1970-01-01",
  "address": [
    {
      "use": "home",
      "line": ["456 Oak Ave"],
      "city": "Anytown",
      "state": "CA",
      "postalCode": "12345",
      "country": "USA"
    }
  ],
  "telecom": [
    {
      "system": "phone",
      "value": "************",
      "use": "home"
    },
    {
      "system": "email",
      "value": "<EMAIL>"
    }
  ]
}
```
8. Click "Save"
9. Click "Send" to update the patient

### Step 5: Create Other FHIR Resources

**Create an Observation Resource**:
1. In the "HAPI FHIR Server" collection, click "Add request"
2. Name it "Create Observation"
3. Set the method to "POST"
4. Set the URL to `{{fhir_server}}/Observation`
5. Go to the "Headers" tab and add:
   - `Content-Type`: `{{content_type}}`
6. Go to the "Body" tab, select "raw", and set the format to "JSON"
7. Add the following JSON:
```json
{
  "resourceType": "Observation",
  "status": "final",
  "category": [
    {
      "coding": [
        {
          "system": "http://terminology.hl7.org/CodeSystem/observation-category",
          "code": "vital-signs",
          "display": "Vital Signs"
        }
      ]
    }
  ],
  "code": {
    "coding": [
      {
        "system": "http://loinc.org",
        "code": "8867-4",
        "display": "Heart rate"
      }
    ],
    "text": "Heart rate"
  },
  "subject": {
    "reference": "Patient/{{patient_id}}"
  },
  "effectiveDateTime": "2023-01-01T12:00:00Z",
  "valueQuantity": {
    "value": 80,
    "unit": "beats/minute",
    "system": "http://unitsofmeasure.org",
    "code": "/min"
  }
}
```
8. Click "Save"
9. Click "Send" to create the observation

## Additional Data Interaction Methods

While Postman is an excellent tool for interactive testing and development, there are several other methods for interacting with the FHIR server and loading data:

- **Direct REST API calls** using tools like cURL
- **HAPI FHIR CLI tool** for bulk operations
- **Custom scripts** in Python, Java, or other languages
- **Web interface** provided by the HAPI FHIR server

For a comprehensive guide on these methods, including examples and best practices, see the [Data Interaction and Loading](09-data_interaction.md) guide.

## Next Steps

Now that you've learned how to use Postman to interact with the FHIR server, you can proceed to the [Documentation and Testing](05-documentation_testing.md) section to learn how to document and test your FHIR server.
