# GitHub Copilot Instructions for FHIR-OMOP Project

## Core Objective
The primary goal of this project is to develop a modular, multi-source ETL (Extract, Transform, Load) orchestrator to transform data from FHIR format to OMOP CDM. The architecture should be generic and not tied to any specific data source experiment (like the previous Abu Dhabi claims analysis).

## Technology Stack Specifications

We use specific versions for compatibility and stability:

- **OMOP CDM**: Version 5.4.2 for all database schemas and references
- **HAPI FHIR**: R4 with `hapiproject/hapi:latest` Docker image  
- **PostgreSQL**: Version 14 (avoid v15+ due to HAPI FHIR compatibility issues)
- **Python Environment**: Conda environment 'fhir-omop'
- **Docker**: Version 20.10.x+ with Compose v2.x

## Code Standards and Patterns

### Python Development
- Follow NumPy-style docstrings for all functions with type hints
- Use environment variable support with dotenv for all scripts
- Use relative paths to reference the root .env file instead of creating separate ones
- Maximum line length of 88 characters (compatible with Black formatter)
- Organize imports: standard library → third-party → local application imports

### Script Structure Pattern
All scripts should follow this consistent structure:
- Command-line argument parsing with argparse
- Environment variable support with dotenv  
- Configuration precedence: command-line args > environment variables > defaults

### Database Patterns
- Use PostgreSQL COPY method for vocabulary loading following OHDSI standards
- Implement existence detection (check if database exists before creating)
- Use localhost:5432 for local PostgreSQL connections
- Database naming: Use descriptive names based on project purpose (e.g., 'omop_main', 'omop_test')
- Use 'public' schema for OMOP CDM tables

### Security and Quality Standards
- **SQL Injection Prevention**: Always use parameterized queries with user input or external data
- **Credential Security**: Never log passwords, store in environment variables, use parameterized queries for credentials
- **Error Handling**: Use specific exception types, provide meaningful error messages with context
- **Code Quality**: Place comments on separate lines, avoid mixing code and comments on complex statements
- **Configuration Validation**: Validate external inputs, check required parameters, clear error messages
- **PostgreSQL DDL**: When f-strings are necessary for DDL operations, document why parameterization isn't possible and ensure values come from controlled configuration

### Docker Architecture
- Follow Docker architecture patterns from servers/fhir-server implementation
- Use transaction bundle approach with permissive mode for large FHIR datasets
- Maintain modular, reusable components in shared modules

## Development Methodology

Apply pedagogical, academic methodology to ALL processes:
- Include detailed step-by-step explanations for technical implementations
- Provide "why/what/how" breakdowns for complex processes
- Reference official sources with direct links and version information
- Validate against official OMOP CDM v5.4.2 documentation before implementation
- Use Context7 MCP for accessing official documentation when available

## Project-Specific Architecture

- Base OMOP module implementation on HL7 Vulcan FHIR-to-OMOP Implementation Guide
- Follow reference_architecture.md and development standards.md for automation planning
- Prioritize fresh OMOP database deployments following official documentation
- Use dynamic folder detection with prefixes for vocabulary paths (avoid hardcoded dates)
- Follow phase-based development: Phase 1 (HAPI FHIR) → Phase 2 (OMOP database)

## Documentation Requirements

- Write all documentation in English using Markdown format
- Structure as: Title → Brief Description → TOC → Main Content → References
- Use ATX-style headers with one space after # character
- Include Mermaid diagrams for complex processes and architecture
- Use inline hyperlinked citations rather than numbered references
- Include version information when referencing software or standards

## Command Documentation Standards

- Use multi-line format for commands with backslash continuation (\) for better readability
- Indent command parameters with 4 spaces after backslash
- Always specify directory context when commands must be run from specific locations
- Include conda environment activation when required
- Follow command documentation format defined in docs/guides/development/standards.md

## Testing and Quality Assurance

- Write unit tests for all new functionality using pytest
- **ALWAYS consult docs/guides/development/unit_testing_standards.md before creating unit tests**
- Follow the testing pyramid: 80% unit tests, 15% integration tests, 5% E2E tests
- Use pytest markers: @pytest.mark.unit, @pytest.mark.integration, @pytest.mark.slow
- Include data validation tolerances (5% variance) for ETL processes
- Implement comprehensive error handling with academic explanations
- Validate outputs at important steps with interpretation guidance
- Follow security standards from docs/guides/development/standards.md
- Apply common pitfalls prevention: no f-strings with user data in SQL, validate configuration before DB operations

## Version Control Practices

- Use branch format: type/description (e.g., feature/add-patient-mapper)
- Write commit messages in imperative mood with capital first letter
- Include descriptive PR descriptions with testing information
- Reference issues and related work in commit messages

## Current Implementation Status - Clean Functional Repository

### ✅ FUNCTIONAL MODULES (Production-Ready)
**This repository contains ONLY functional, production-ready components:**

1. **OMOP Database Module** (`servers/omop-database/`)
   - Complete PostgreSQL deployment with Docker
   - Official OHDSI vocabulary loading
   - Automated database creation and management
   - Testing framework designed (implementation pending)

2. **Abu Dhabi Claims ETL** (`src/fhir_omop/etl/abu_dhabi_claims_mvp/`)
   - Complete production ETL pipeline
   - 13 comprehensive unit tests
   - Performance validated (8,189 records in ~1.3 seconds)

3. **FHIR Server Module** (`servers/fhir-server/`)
   - Mature HAPI FHIR R4 implementation
   - Docker containerization complete

4. **Testing Framework** (`tests/test_omop_database/`)
   - Functional testing structure for OMOP database module
   - Comprehensive testing standards defined

### 🗂️ TEMPLATE CODE ARCHIVE
**Template/skeleton code has been removed and archived:**

All research-based template code, FHIR mapper designs, and utility module templates have been preserved in the `archive/template-code-complete` branch. This includes:
- Well-documented FHIR mapper templates
- Utility module design patterns
- Main ETL package templates
- Comprehensive implementation analysis

**Access archived templates**: `git checkout archive/template-code-complete`

### Development Priority
Focus on extending the **functional modules** using proven patterns from the Abu Dhabi ETL implementation and OMOP database module architecture.

## Package Management

- Always use package managers (conda, pip) instead of manually editing package files
- Use conda for environment management, pip for package installation within conda env
- Remove unnecessary dependencies when implementation decisions change
- Maintain clean environment.yml file aligned with actual project needs

## Common Pitfalls to Avoid

- Never use f-strings with user-provided data in SQL queries
- Don't place comments at the end of complex Python statements
- Always validate configuration before database operations
- Use specific exception types for better error handling
- When using f-strings for DDL operations, document why parameterization isn't possible and ensure values come from controlled configuration
