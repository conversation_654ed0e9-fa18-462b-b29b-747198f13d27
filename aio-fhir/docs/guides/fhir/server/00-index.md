# FHIR Server Setup and Configuration Guide

This guide provides comprehensive instructions for setting up and configuring a HAPI FHIR server for local development and testing. The guide is divided into several modules to make it easier to navigate and follow.

> **Note**: The actual implementation of the FHIR server can be found in the [servers/fhir-server](../../../../servers/fhir-server) directory, which contains the Docker configuration, scripts, and other files needed to run the server.

## Table of Contents

1. [**Overview and Architecture**](01-overview.md) - Introduction to the FHIR server architecture and design principles
2. [**Prerequisites and Environment Setup**](02-prerequisites.md) - Verifying and setting up the required environment
3. [**FHIR Server Installation**](03-server_setup.md) - Step-by-step guide to install and configure the HAPI FHIR server
4. [**PostgreSQL Configuration**](setup-postgres.md) - Detailed guide for setting up the FHIR server with PostgreSQL
5. [**Postman Setup and Usage**](04-postman_setup.md) - Setting up <PERSON>man and creating FHIR resource examples
6. [**Documentation and Testing**](05-documentation_testing.md) - Creating documentation and testing the FHIR server
7. [**Deployment and Exportability**](06-deployment.md) - Scripts for setup automation and project exportability
8. [**Security Considerations**](07-security.md) - Security best practices for the FHIR server
9. [**Future Work: Phase 2**](08-future_work.md) - Preview of the next phase (OMOP CDM integration)
10. [**Data Interaction and Loading**](09-data_interaction.md) - Methods for interacting with the FHIR server and loading data

## How to Use This Guide

This guide is designed to be followed sequentially, but you can also jump to specific sections if you're only interested in particular aspects of the setup. Each module builds on the previous ones, so if you're starting from scratch, it's recommended to follow the guide in order.

## Quick Start

If you're familiar with Docker and just want to get the FHIR server up and running quickly:

1. Ensure Docker and Docker Compose are installed
2. Navigate to the FHIR server directory:
   ```bash
   cd servers/fhir-server
   ```
3. Start the server with PostgreSQL:
   ```bash
   ./manage-fhir-server.sh start
   ```
4. Access the FHIR server at http://localhost:8080/fhir/metadata
5. Load sample data using one of our methods:
   ```bash
   # Method 1: Direct Transaction Bundles (requires server configuration)
   python scripts/data_loading/ndjson_to_bundle.py \
       --input-file ../../data/sample_fhir/bulk-export/Patient.000.ndjson \
       --output-file ../../data/generated_bundles/Patient_bundle.json

   python scripts/data_loading/send_bundle.py \
       --input-file ../../data/generated_bundles/Patient_bundle.json \
       --server-url http://localhost:8080/fhir

   # Method 2: Selective Loading (works with default server configuration)
   python scripts/data_loading/selective_loader.py \
       --data-dir ../../data/sample_fhir/bulk-export \
       --server-url http://localhost:8080/fhir \
       --verify
   ```

## Next Steps

Start with the [Overview and Architecture](01-overview.md) section to understand the design principles behind this setup.

## Data Loading Documentation

For detailed information on loading data into the FHIR server, see:

- [Quick Reference](../data-loading/quick-reference.md) - Concise overview of both methods
- [Direct Transaction Tutorial](../data-loading/direct-transaction-tutorial.md) - Step-by-step guide for bulk loading
- [Selective Loading Guide](../data-loading/selective-loading.md) - Guide for reference-preserving loading
- [Manual Bundle Tutorial](../data-loading/manual-bundle-tutorial.md) - Detailed explanation of bundle creation
