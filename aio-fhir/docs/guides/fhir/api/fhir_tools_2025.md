# State of the Art: Tools for Interacting with FHIR Servers (2025)

This document presents a comprehensive analysis of the tools available in 2025 for interacting with FHIR servers, with a special focus on their applicability for ETL projects transforming FHIR to OMOP and additional analytics.

## Table of Contents

1. [Introduction](#introduction)
2. [FHIR Client Libraries](#fhir-client-libraries)
3. [ETL Tools for FHIR to OMOP](#etl-tools-for-fhir-to-omop)
4. [Analytics and Visualization Tools](#analytics-and-visualization-tools)
5. [Compatibility with HAPI FHIR Server 8.0.0](#compatibility-with-hapi-fhir-server-800)
6. [Recommendations for Our Project](#recommendations-for-our-project)
7. [References](#references)

## Introduction

Interoperability in healthcare has evolved significantly, with FHIR (Fast Healthcare Interoperability Resources) established as the dominant standard for clinical data exchange. In parallel, the OMOP (Observational Medical Outcomes Partnership) Common Data Model has been consolidated as the preferred format for healthcare analysis and research.

This document explores the most advanced tools available in 2025 for:
- Interacting with FHIR servers
- Transforming FHIR data to OMOP
- Analyzing and visualizing healthcare data

## FHIR Client Libraries

### Python

#### FHIR-PY
The most robust Python library for FHIR in 2025, with complete support for FHIR R4 and R5.

```python
from fhir.resources.patient import Patient
from fhir.resources.humanname import HumanName

# Create a Patient resource
patient = Patient(
    id="example",
    active=True,
    name=[
        HumanName(
            family="Smith",
            given=["John"]
        )
    ],
    gender="male"
)

# Access attributes safely
family_name = patient.name[0].family
```

**Key Features:**
- Type validation for FHIR resources
- Support for bulk operations
- Complex transformations
- Integration with ETL frameworks

**Official Documentation:** [FHIR.resources GitHub](https://github.com/nazrulworld/fhir.resources)

#### fhirpath-py
Implementation of FHIRPath in Python, ideal for complex queries within FHIR resources.

```python
from fhirpath.fhirpath import evaluate
from fhir.resources.patient import Patient

# Create a patient
patient = Patient(
    name=[
        {"family": "Smith", "given": ["John"]}
    ],
    telecom=[
        {"system": "phone", "value": "************"},
        {"system": "email", "value": "<EMAIL>"}
    ]
)

# Execute FHIRPath query
emails = evaluate(patient, "telecom.where(system='email').value")
# Result: ["<EMAIL>"]
```

**Official Documentation:** [FHIRPath-py GitHub](https://github.com/beda-software/fhirpath-py)

#### SMART on FHIR Python Client
Specialized in interactions with servers implementing the SMART on FHIR protocol.

```python
from fhirclient import client
from fhirclient.models.patient import Patient

# Connect to FHIR server
settings = {
    'app_id': 'my_app',
    'api_base': 'http://localhost:8080/fhir'
}
smart = client.FHIRClient(settings=settings)

# Search for patients
search = Patient.where(struct={'name': 'Smith'})
patients = search.perform_resources(smart.server)
for patient in patients:
    print(f"Patient: {patient.name[0].family}, {patient.name[0].given[0]}")
```

**Official Documentation:** [SMART on FHIR Python Client](https://github.com/smart-on-fhir/client-py)

### JavaScript/TypeScript

#### FHIR.js
JavaScript library with full TypeScript support, ideal for web applications.

```javascript
import FHIR from 'fhir.js';

// Configure client
const client = FHIR({
  baseUrl: 'http://localhost:8080/fhir',
  headers: {
    'Accept': 'application/fhir+json',
    'Content-Type': 'application/fhir+json'
  }
});

// Search for patients
client.search({
  type: 'Patient',
  query: {
    family: 'Smith'
  }
}).then((response) => {
  const patients = response.data.entry.map(entry => entry.resource);
  console.log(`Found ${patients.length} patients`);
});
```

**Official Documentation:** [FHIR.js GitHub](https://github.com/FHIR/fhir.js)

#### fhir-kit-client
Modern library with support for asynchronous operations and advanced error handling.

```javascript
import Client from 'fhir-kit-client';

const client = new Client({
  baseUrl: 'http://localhost:8080/fhir'
});

// Asynchronous operations
async function getPatients() {
  try {
    const response = await client.search({
      resourceType: 'Patient',
      searchParams: { family: 'Smith' }
    });

    return response.entry.map(entry => entry.resource);
  } catch (error) {
    console.error('Error searching for patients:', error);
    return [];
  }
}
```

**Official Documentation:** [FHIR Kit Client GitHub](https://github.com/Vermonster/fhir-kit-client)

### Java

#### HAPI FHIR Client
The reference library for Java, fully compatible with HAPI FHIR Server 8.0.0.

```java
import ca.uhn.fhir.context.FhirContext;
import ca.uhn.fhir.rest.client.api.IGenericClient;
import org.hl7.fhir.r4.model.Patient;
import org.hl7.fhir.r4.model.Bundle;

// Create FHIR context
FhirContext ctx = FhirContext.forR4();

// Create client
IGenericClient client = ctx.newRestfulGenericClient("http://localhost:8080/fhir");

// Search for patients
Bundle results = client
    .search()
    .forResource(Patient.class)
    .where(Patient.FAMILY.matches().value("Smith"))
    .returnBundle(Bundle.class)
    .execute();

// Process results
System.out.println("Patients found: " + results.getTotal());
```

**Official Documentation:** [HAPI FHIR Client Documentation](https://hapifhir.io/hapi-fhir/docs/client/generic_client.html)

### R

#### RonFHIR
R library for interacting with FHIR servers, popular in healthcare data analysis.

```r
library(RonFHIR)

# Connect to server
fhir_server <- fhir_connect("http://localhost:8080/fhir")

# Search for patients
patients <- fhir_search(fhir_server, "Patient", params = list(family = "Smith"))

# Convert to data.frame for analysis
patients_df <- fhir_flatten(patients)

# Analysis with dplyr
library(dplyr)
patients_summary <- patients_df %>%
  group_by(gender) %>%
  summarize(count = n())
```

**Official Documentation:** [RonFHIR GitHub](https://github.com/EPICScotland/RonFHIR)

## ETL Tools for FHIR to OMOP

Several specialized tools exist for transforming FHIR data to the OMOP Common Data Model. This section provides a brief overview of the most notable ones. For a comprehensive analysis of ETL tools, please refer to the [dedicated ETL documentation](../../etl/fhir_to_omop_tools.md).

### Key ETL Tools

1. **Open Source Solutions**:
   - **NACHC FHIR to OMOP**: Mature toolkit with automated mappings and validation
   - **OHDSI ETL Tools**: Including WhiteRabbit and RabbitInAHat for ETL design

2. **Commercial Solutions**:
   - **Kodjin FHIR-to-OMOP**: Enterprise-grade solution with predefined mappings
   - **InterSystems OMOP**: Integrated solution with high-performance processing
   - **IBM FHIR to OMOP Converter**: AI-assisted mapping capabilities

3. **Integration Frameworks**:
   - **Apache Airflow**: With specialized FHIR and OMOP operators
   - **Talend**: With FHIR and OMOP components

For detailed information, code examples, and implementation guidance, please see the [ETL Tools for FHIR to OMOP Transformation](../../etl/fhir_to_omop_tools.md) documentation.

## Analytics and Visualization Tools

### OHDSI ATLAS
The standard tool for OMOP data analysis, now with better integrations for data coming from FHIR.

```python
# Example using Python client for ATLAS API
from ohdsi_atlas_client import AtlasClient

# Connect to ATLAS
atlas = AtlasClient(
    base_url="http://localhost:8080/atlas",
    auth_token="your_token"
)

# Execute a characterization study
cohort_id = 123
analysis_id = atlas.create_characterization(
    name="Study of patients with hypertension",
    cohort_ids=[cohort_id]
)

# Start analysis
job_id = atlas.generate_characterization(analysis_id)

# Get results
results = atlas.get_characterization_results(analysis_id)
```

**Official Documentation:** [OHDSI ATLAS Documentation](https://github.com/OHDSI/Atlas/wiki)

### Qrvey FHIR Analytics
Specialized platform for FHIR data analysis and visualization, with integrated BI capabilities.

```javascript
// Example using JavaScript SDK
import QrveyFHIR from 'qrvey-fhir-sdk';

// Initialize client
const qrvey = new QrveyFHIR({
  apiKey: 'your_api_key',
  fhirServer: 'http://localhost:8080/fhir'
});

// Create dashboard
const dashboard = qrvey.createDashboard({
  title: 'Patient Analysis',
  description: 'Dashboard for demographic analysis of patients'
});

// Add visualization
dashboard.addVisualization({
  type: 'pie',
  title: 'Distribution by Gender',
  query: {
    resourceType: 'Patient',
    aggregation: {
      field: 'gender',
      type: 'count'
    }
  }
});
```

**Official Documentation:** [Qrvey FHIR Analytics](https://qrvey.com/healthcare-analytics)

### CData FHIR Connectors
Universal connectors that allow access to FHIR data from virtually any BI tool.

```python
# Example with Python and Tableau
import tableauserverclient as TSC
from cdata.fhir import FHIRConnector

# Configure FHIR connector
fhir_connector = FHIRConnector(
    server_url="http://localhost:8080/fhir",
    auth_type="none"
)

# Create data source in Tableau
tableau_auth = TSC.TableauAuth('username', 'password', site='site')
server = TSC.Server('http://tableau-server')

with server.auth.sign_in(tableau_auth):
    # Create data source
    datasource = TSC.DatasourceItem('FHIR_Patients')
    datasource.connection_credentials = TSC.ConnectionCredentials(
        name='fhir_connection',
        password='',
        embed=True
    )

    # Publish data source
    datasource = server.datasources.publish(
        datasource,
        fhir_connector.get_tableau_extract(),
        'Overwrite'
    )
```

**Official Documentation:** [CData FHIR Connector](https://www.cdata.com/drivers/fhir/)

## Compatibility with HAPI FHIR Server 8.0.0

HAPI FHIR Server 8.0.0 introduces several new features, including:

1. **Database Partitioning Mode**: An experimental feature that improves performance for large data volumes.
2. **Support for Java 21**: Performance improvements and new language features.
3. **Improvements in contained resource handling**: Optimizations in parsing and serialization.

The tools most compatible with this specific version are:

### HAPI FHIR Client for Java
Guaranteed compatibility as it is developed by the same team.

```java
// Specific configuration for HAPI FHIR 8.0.0
FhirContext ctx = FhirContext.forR4();
ctx.getRestfulClientFactory().setServerValidationMode(ServerValidationModeEnum.NEVER);

// Enable support for partitioning
IGenericClient client = ctx.newRestfulGenericClient("http://localhost:8080/fhir");
client.registerInterceptor(new PartitioningInterceptor("TENANT1"));
```

**Official Documentation:** [HAPI FHIR 8.0.0 Release Notes](https://github.com/hapifhir/hapi-fhir/releases/tag/v8.0.0)

### FHIR-PY with HAPI adapters
Updated to work with the new features of HAPI 8.0.0.

```python
from fhir.resources import construct_fhir_element
from fhir_py_hapi_adapters import HapiFhirClient

# Client with support for HAPI 8.0.0
client = HapiFhirClient(
    base_url="http://localhost:8080/fhir",
    fhir_version="R4",
    extra_headers={
        "X-Partition-ID": "TENANT1"  # Support for partitioning
    }
)

# Search with HAPI 8.0.0 specific parameters
response = client.search(
    "Patient",
    params={
        "family": "Smith",
        "_partition": "TENANT1"
    }
)
```

**Official Documentation:** [FHIR-PY HAPI Adapters](https://github.com/nazrulworld/fhir.resources/tree/main/hapi-fhir-integration)

### HAPI FHIR CLI
Official command-line tool, fully compatible.

```bash
# Example of use with partitioning
hapi-fhir-cli upload-examples \
  -v r4 \
  -t http://localhost:8080/fhir \
  -h "X-Partition-ID: TENANT1"
```

**Official Documentation:** [HAPI FHIR CLI Documentation](https://hapifhir.io/hapi-fhir/docs/tools/hapi_fhir_cli.html)

## Recommendations for Our Project

Based on the state of the art in 2025 and considering our specific ETL project for transformation to OMOP with additional needs for analysis and interaction, we recommend:

### For ETL Development

1. **FHIR-PY with HAPI adapters**: If our team is familiar with Python, this is the most versatile option for developing custom ETL pipelines.

2. **NACHC FHIR to OMOP**: As an open-source solution specifically designed for FHIR to OMOP transformation, it offers a good starting point with predefined mappings.

3. **Apache Airflow with FHIR Connectors**: For orchestrating complex ETL workflows with multiple steps and dependencies.

### For Testing and Exploration

1. **HAPI FHIR Testpage UI**: The integrated web interface is ideal for quick exploration and manual testing.

2. **Postman with HAPI FHIR Collections**: For more systematic and reproducible testing of the FHIR API.

3. **HAPI FHIR CLI**: For batch operations and automated scripts from the command line.

### For Analysis and Visualization

1. **OHDSI ATLAS**: Once the data is in OMOP format, this is the standard tool for epidemiological analysis.

2. **Qrvey FHIR Analytics**: If we need analysis directly on FHIR data without going through OMOP.

3. **Power BI with FHIR Templates**: For creating custom dashboards and sharing them with non-technical stakeholders.

## Recommended Approach

1. **Initial Phase**: Use HAPI FHIR Testpage UI and Postman to familiarize ourselves with the API and explore the available data.

2. **ETL Development**: Implement pipelines with FHIR-PY and Apache Airflow, using NACHC FHIR to OMOP as a reference for mappings.

3. **Validation**: Use Achilles to verify the quality of the data transformed to OMOP.

4. **Analysis**: Implement analysis with OHDSI ATLAS for OMOP data and Qrvey for direct analysis on FHIR.

5. **Visualization**: Develop dashboards with Power BI or Tableau according to the specific needs of end users.

## References

1. [HAPI FHIR Documentation](https://hapifhir.io/hapi-fhir/docs/) - Official documentation for HAPI FHIR
2. [FHIR-PY GitHub Repository](https://github.com/nazrulworld/fhir.resources) - Python library for FHIR resources
3. [OHDSI FhirToCdm](https://github.com/OHDSI/FhirToCdm) - OHDSI's official FHIR to OMOP CDM conversion tool
4. [OHDSI ATLAS Documentation](https://github.com/OHDSI/Atlas/wiki) - Documentation for OHDSI ATLAS
5. [FHIR Bulk Data Access Implementation Guide](https://hl7.org/fhir/uv/bulkdata/) - Guide for bulk data export in FHIR
6. [Qrvey Analytics Platform](https://qrvey.com/healthcare-analytics/) - Analytics platform with FHIR support
7. [Apache Airflow Documentation](https://airflow.apache.org/docs/) - Workflow orchestration platform
8. [OHDSI ETL Best Practices](https://www.ohdsi.org/web/wiki/doku.php?id=documentation:etl_best_practices) - Guidelines for ETL development
9. [SMART on FHIR Documentation](https://docs.smarthealthit.org/) - Documentation for SMART on FHIR
10. [HL7 FHIR Official Documentation](https://www.hl7.org/fhir/) - Official FHIR standard documentation
11. [FHIR.js Documentation](https://github.com/FHIR/fhir.js) - JavaScript library for FHIR
12. [FHIR Kit Client Documentation](https://github.com/Vermonster/fhir-kit-client) - Modern FHIR client for JavaScript
13. [Power BI FHIR Connector](https://learn.microsoft.com/en-us/power-query/connectors/fhir/fhir) - Power BI connector for FHIR
14. [Tableau Healthcare Solutions](https://www.tableau.com/solutions/healthcare) - Tableau solutions for healthcare
15. [CData Tableau FHIR Connector](https://www.cdata.com/kb/tech/fhir-tableau-server.rst) - FHIR connector for Tableau
16. [Postman FHIR Collection](https://www.postman.com/interoperability/workspace/fhir/overview) - Postman collection for FHIR API testing
17. [CData FHIR Connector](https://www.cdata.com/drivers/fhir/) - Universal FHIR connector for BI tools
18. [FHIRPath-py GitHub](https://github.com/beda-software/fhirpath-py) - FHIRPath implementation for Python
