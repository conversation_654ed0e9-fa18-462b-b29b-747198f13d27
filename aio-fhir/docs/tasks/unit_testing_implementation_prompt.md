# Unit Testing Implementation Prompt - OMOP Database Module

## Context and Objective

You are tasked with implementing the **FIRST** unit tests for the FHIR-OMOP project, specifically targeting the **OMOP Database module** (`servers/omop-database/`). This is a foundational task that will set the testing pattern for all future modules in the project.

## Project Overview

The FHIR-OMOP project is a healthcare data transformation pipeline that converts HL7 FHIR resources to OMOP Common Data Model format. The project is currently in Phase 1 (FHIR server setup) and moving toward Phase 2 (OMOP database implementation).

### Technology Stack
- **Python**: 3.11 with conda environment 'fhir-omop'
- **Testing Framework**: pytest with coverage reporting
- **Database**: PostgreSQL 14 for OMOP CDM v5.4.2
- **FHIR**: HAPI FHIR R4 server
- **Development Approach**: Pedagogical, academic methodology with step-by-step explanations

## ⚠️ IMPORTANT: Project Structure Context

### Target Module (COMPLETE AND READY FOR TESTING)
**ONLY focus on this module for this implementation:**

```
servers/omop-database/                    # ⭐ TARGET MODULE - COMPLETE
├── manage-omop-database.sh              # Main management script
├── docker-compose.yml                   # PostgreSQL container config
├── scripts/setup/
│   ├── __init__.py                      # Package initialization
│   ├── config.py                        # Configuration management
│   ├── database_checker.py              # Database state verification
│   ├── create_database.py               # Database creation automation
│   └── load_vocabularies.py             # OMOP vocabulary loading
└── README.md                            # Complete documentation
```

### Placeholder/Incomplete Modules (IGNORE THESE)
**DO NOT attempt to test these - they are templates/placeholders:**

```
src/                                      # 🚫 PLACEHOLDER - Empty skeleton
├── fhir_omop/                           # 🚫 Future main package (empty)
└── __init__.py                          # 🚫 Placeholder file

tests/                                    # 🚫 MOSTLY EMPTY - Only basic structure
├── conftest.py                          # 🚫 Currently empty
├── test_etl/                            # 🚫 Placeholder directory
├── test_mappers/                        # 🚫 Placeholder directory
├── test_omop_database/                  # 🚫 Empty except __init__.py
└── test_fhir_server/                    # 🚫 Placeholder directory

temp/                                     # 🚫 IGNORE - Temporary files
data/                                     # 🚫 IGNORE - Data storage only
docs/examples/                            # 🚫 IGNORE - Documentation examples
servers/fhir-server/                      # 🚫 IGNORE - Different phase
```

### What This Means for You
- **Focus EXCLUSIVELY** on `servers/omop-database/` module
- **Ignore** any empty `__init__.py` files or placeholder directories
- **Don't be confused** by empty test directories - you'll be creating the first real tests
- **The target module is complete** and ready for comprehensive testing

## Required Reading

Before starting implementation, review these documents:

1. **Primary Standards**: `docs/guides/development/unit_testing_standards.md`
   - Complete testing philosophy and architecture
   - Framework configuration and fixture strategies
   - Mocking patterns and test data management

2. **Code Standards**: `docs/guides/development/standards.md`
   - Python coding conventions
   - Documentation requirements
   - File organization patterns

3. **Target Module Documentation**: `servers/omop-database/README.md`
   - Complete module functionality overview
   - Configuration and usage patterns
   - Integration points and dependencies

## Implementation Tasks

### Task 1: Setup Testing Infrastructure for OMOP Database Module

1. **Create the test directory structure**:
   ```
   tests/test_omop_database/
   ├── __init__.py                      # Already exists
   ├── conftest.py                      # CREATE - Module-specific fixtures
   ├── test_config.py                   # CREATE - Configuration management tests
   ├── test_database_checker.py         # CREATE - Database verification tests
   ├── test_create_database.py          # CREATE - Database creation tests
   ├── test_load_vocabularies.py        # CREATE - Vocabulary loading tests
   ├── test_integration.py              # CREATE - Integration tests
   └── fixtures/
       ├── __init__.py                  # CREATE
       ├── sample_data.py               # CREATE - Test data constants
       └── mock_responses.py            # CREATE - Mock database responses
   ```

2. **Verify pytest configuration** exists and add OMOP-specific markers:
   - Check `pytest.ini` configuration
   - Add markers for OMOP database tests
   - Configure test paths to include new test directory

3. **Create module-specific fixtures** in `tests/test_omop_database/conftest.py`:
   - Mock database connections
   - Sample OMOP configuration
   - Temporary directory fixtures for DDL scripts
   - Mock vocabulary data

### Task 2: Analyze Target Module Components

**Focus on these specific files in `servers/omop-database/scripts/setup/`:**

1. **`config.py`** - Configuration management:
   - Environment variable loading
   - Database connection parameters
   - File path resolution
   - Vocabulary file definitions

2. **`database_checker.py`** - Database state verification:
   - Connection testing
   - Table existence checking
   - Setup status verification
   - Health check functions

3. **`create_database.py`** - Database creation automation:
   - DDL script downloading
   - Schema placeholder replacement
   - SQL execution
   - Installation verification

4. **`load_vocabularies.py`** - OMOP vocabulary loading:
   - CSV file processing
   - Batch loading operations
   - Progress tracking
   - Error handling

### Task 3: Implement Comprehensive Unit Test Suite

#### 3.1 Test Structure for OMOP Database Module
```
tests/test_omop_database/
├── conftest.py                          # OMOP-specific fixtures
├── test_config.py                       # Configuration tests (15+ tests)
├── test_database_checker.py             # Database verification tests (10+ tests)
├── test_create_database.py              # Database creation tests (20+ tests)
├── test_load_vocabularies.py            # Vocabulary loading tests (15+ tests)
├── test_integration.py                  # Integration tests (5+ tests)
└── fixtures/
    ├── sample_data.py                   # Test constants and sample data
    └── mock_responses.py                # Mock database responses
```

#### 3.2 Specific Test Categories for Each Component

**For `config.py`:**
- Environment variable loading and defaults
- Database connection string construction
- File path resolution and validation
- Configuration validation and error handling

**For `database_checker.py`:**
- Database connection testing (success/failure scenarios)
- Table existence verification
- Setup status checking
- Health check functionality

**For `create_database.py`:**
- DDL script downloading and caching
- Schema placeholder replacement
- SQL script execution (mocked)
- Installation verification
- Error handling for each step

**For `load_vocabularies.py`:**
- CSV file reading and validation
- Batch processing logic
- Progress tracking
- Database insertion (mocked)
- Error handling and rollback scenarios

#### 3.3 Required Test Patterns for OMOP Module

1. **Configuration Tests**:
   ```python
   @pytest.mark.unit
   def test_config_loads_environment_variables():
       """Test that configuration correctly loads from environment variables."""
       pass

   @pytest.mark.unit
   def test_config_uses_defaults_when_env_missing():
       """Test that configuration uses appropriate defaults."""
       pass
   ```

2. **Database Interaction Tests (Mocked)**:
   ```python
   @pytest.mark.unit
   @patch('psycopg2.connect')
   def test_database_connection_success(mock_connect):
       """Test successful database connection."""
       pass

   @pytest.mark.unit
   @patch('psycopg2.connect')
   def test_database_connection_failure_handling(mock_connect):
       """Test proper handling of database connection failures."""
       pass
   ```

3. **File Processing Tests**:
   ```python
   @pytest.mark.unit
   def test_vocabulary_file_validation(tmp_path):
       """Test vocabulary file validation with sample data."""
       pass
   ```

4. **Integration Tests**:
   ```python
   @pytest.mark.integration
   @pytest.mark.slow
   def test_complete_database_setup_workflow():
       """Test complete database setup workflow with mocked dependencies."""
       pass
   ```

### Task 4: Create OMOP-Specific Test Fixtures

In `tests/test_omop_database/conftest.py`, create:

1. **Configuration Fixtures**:
   - Mock environment variables
   - Sample database configurations
   - Test file paths

2. **Database Mock Fixtures**:
   - Mock PostgreSQL connections
   - Mock cursor objects
   - Sample query results

3. **File System Fixtures**:
   - Temporary directories for DDL scripts
   - Sample vocabulary CSV files
   - Mock downloaded files

4. **Data Fixtures**:
   - Sample OMOP vocabulary data
   - Expected database schemas
   - Test configuration objects

### Task 5: Documentation and Examples

1. **Create comprehensive test documentation** in `tests/test_omop_database/README.md`:
   - How to run OMOP database tests specifically
   - Test organization for this module
   - Mock strategy explanation
   - Troubleshooting OMOP-specific issues

2. **Document test patterns** specific to database operations:
   - How to mock PostgreSQL connections
   - Testing file download operations
   - Validating SQL script execution
   - Testing batch processing operations

### Task 6: Validation and Quality Assurance

1. **Verify test execution**:
   - All tests pass consistently
   - Coverage ≥ 85% for OMOP database module
   - Tests run in < 45 seconds for full OMOP suite
   - No actual database connections in unit tests

2. **Validate OMOP-specific test quality**:
   - Database operations are properly mocked
   - File operations use temporary directories
   - Configuration tests cover all environment variables
   - Error scenarios match real-world OMOP setup issues

## Deliverables

### Primary Deliverables

1. **Complete test suite for OMOP Database module** with:
   - **60+ unit tests** covering all four main components
   - **5+ integration tests** for key OMOP workflows
   - **15+ error handling tests** for database and file operations
   - **5+ performance/edge case tests**

2. **OMOP-specific test infrastructure** including:
   - Module-specific conftest.py with database mocks
   - Test data fixtures for OMOP vocabulary samples
   - Mock objects for PostgreSQL operations

3. **OMOP module test documentation** including:
   - Test suite README with OMOP-specific instructions
   - Examples of database mocking patterns
   - Troubleshooting guide for OMOP test issues

### Secondary Deliverables

1. **Test execution report** showing:
   - All OMOP database tests passing
   - Coverage percentage for each component
   - Performance analysis for test suite

2. **Implementation notes** documenting:
   - OMOP-specific testing challenges and solutions
   - Database mocking strategy decisions
   - Recommendations for testing other database modules

## Success Criteria

### Functional Criteria
- [ ] All OMOP database tests pass consistently
- [ ] Test coverage ≥ 85% for `servers/omop-database/scripts/setup/` module
- [ ] Tests execute in < 45 seconds total
- [ ] No actual database connections in unit tests
- [ ] Integration tests properly mock external dependencies

### OMOP-Specific Quality Criteria
- [ ] Database operations are comprehensively mocked
- [ ] Configuration tests cover all OMOP environment variables
- [ ] File processing tests use realistic OMOP vocabulary samples
- [ ] Error scenarios match real OMOP setup challenges
- [ ] Tests validate OMOP CDM v5.4.2 compliance

### Documentation Criteria
- [ ] Complete OMOP module test documentation
- [ ] Clear instructions for running OMOP-specific tests
- [ ] Examples of database mocking patterns
- [ ] Troubleshooting guide for OMOP test scenarios

## Implementation Guidelines

### OMOP-Specific Considerations

1. **Database Mocking Strategy**:
   - Mock all PostgreSQL connections
   - Use realistic OMOP schema responses
   - Test both successful and failed database operations

2. **File System Testing**:
   - Use temporary directories for DDL script downloads
   - Create sample vocabulary CSV files for testing
   - Test file validation and error handling

3. **Configuration Testing**:
   - Test all OMOP-specific environment variables
   - Validate database connection string construction
   - Test configuration validation and defaults

4. **Integration Testing**:
   - Test complete OMOP setup workflows
   - Mock external dependencies (downloads, database)
   - Validate proper error propagation

## Target Module File Analysis

### Files to Test (in `servers/omop-database/scripts/setup/`):

1. **`config.py`** (~150 lines):
   - Environment variable management
   - Database configuration
   - File path utilities
   - Vocabulary file definitions

2. **`database_checker.py`** (~100 lines):
   - Database connection testing
   - Table existence verification
   - Setup status checking

3. **`create_database.py`** (~300 lines):
   - DDL script management
   - Database creation workflow
   - SQL execution and verification

4. **`load_vocabularies.py`** (~250 lines):
   - CSV file processing
   - Batch loading operations
   - Progress tracking and error handling

### Expected Test Distribution:
- **config.py**: 15 tests (environment, paths, validation)
- **database_checker.py**: 12 tests (connections, checks, status)
- **create_database.py**: 25 tests (download, creation, verification)
- **load_vocabularies.py**: 20 tests (loading, batching, errors)
- **Integration**: 8 tests (workflows, end-to-end scenarios)

**Total: ~80 tests for comprehensive coverage**

## Final Notes

This implementation focuses **exclusively** on the OMOP Database module, which is the most complete and ready-for-testing component in the current project. The testing patterns you establish here will serve as the foundation for testing other modules as they are developed.

Remember:
- **Ignore** all placeholder/empty directories
- **Focus only** on `servers/omop-database/` module
- **Create the first real tests** for this project
- **Establish patterns** that future developers can follow
- **Document everything** for the pedagogical approach

This foundational work is critical to the project's quality and will demonstrate proper testing practices for healthcare data processing systems.
