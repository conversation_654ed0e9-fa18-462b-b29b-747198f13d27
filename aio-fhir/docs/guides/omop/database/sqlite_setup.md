# SQLite OMOP CDM Database Setup Guide

This guide explains how to set up a SQLite database for the OMOP Common Data Model (CDM) v5.4. While not officially supported by OHDSI, SQLite provides a lightweight, file-based database option that is excellent for development and testing purposes.

## Overview

SQLite is a self-contained, serverless, zero-configuration, transactional SQL database engine. Unlike PostgreSQL or other enterprise database systems, SQLite doesn't require a separate server process and writes directly to ordinary disk files.

## Advantages and Limitations

### Advantages of SQLite for OMOP CDM

- **No installation required**: SQLite is a file-based database that doesn't need server setup
- **Portability**: The entire database is contained in a single file that can be easily shared
- **Simplicity**: Minimal configuration and administration overhead
- **Resource efficiency**: Uses minimal system resources
- **Perfect for development**: Ideal for local development and testing
- **Easy reset**: Simply delete the file and recreate to start fresh

### Limitations of SQLite for OMOP CDM

- **Not officially supported** by OHDSI (official support is for PostgreSQL, SQL Server, Oracle, etc.)
- **Performance limitations** with large datasets (not recommended for full vocabulary sets)
- **Concurrency limitations**: Not suitable for multi-user environments
- **Feature limitations**: Lacks some advanced database features used in production
- **Migration required**: Eventually need to migrate to a production database system

## When to Use SQLite for OMOP CDM

SQLite is recommended when:
- You're in early development stages
- You're learning the OMOP CDM structure
- You're testing ETL logic with small datasets
- You need a portable development environment
- You want to avoid complex database server setup

## Prerequisites

- Python 3.6+ installed
- SQLite3 command-line tools (usually included with Python)
- At least 1GB of free disk space for a minimal setup

## Step 1: Create a Python Script to Generate the SQLite Database

Since OHDSI doesn't provide official SQLite DDL scripts, we'll create a Python script to generate the database structure based on the OMOP CDM v5.4 specifications.

Create a file named `create_omop_tables_sqlite.py`:

```python
#!/usr/bin/env python
"""
Script to create OMOP CDM v5.4 tables in SQLite.
This script creates a SQLite database with the main tables of the OMOP CDM v5.4.
"""
import os
import sqlite3
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get database path from .env or use default
DB_PATH = os.getenv("OMOP_DB_CONNECTION_STRING", "sqlite:///omop_test.db").replace("sqlite:///", "")

def create_omop_tables():
    """Create the main OMOP CDM tables in SQLite."""
    print(f"Creating OMOP CDM database at: {DB_PATH}")
    
    # Ensure directory exists
    db_dir = os.path.dirname(DB_PATH)
    if db_dir and not os.path.exists(db_dir):
        os.makedirs(db_dir)
    
    # Connect to SQLite database
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # Create vocabulary tables
    print("Creating vocabulary tables...")
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS concept (
        concept_id INTEGER PRIMARY KEY,
        concept_name TEXT NOT NULL,
        domain_id TEXT NOT NULL,
        vocabulary_id TEXT NOT NULL,
        concept_class_id TEXT NOT NULL,
        standard_concept TEXT,
        concept_code TEXT NOT NULL,
        valid_start_date TEXT NOT NULL,
        valid_end_date TEXT NOT NULL,
        invalid_reason TEXT
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS vocabulary (
        vocabulary_id TEXT PRIMARY KEY,
        vocabulary_name TEXT NOT NULL,
        vocabulary_reference TEXT,
        vocabulary_version TEXT,
        vocabulary_concept_id INTEGER NOT NULL
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS domain (
        domain_id TEXT PRIMARY KEY,
        domain_name TEXT NOT NULL,
        domain_concept_id INTEGER NOT NULL
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS concept_class (
        concept_class_id TEXT PRIMARY KEY,
        concept_class_name TEXT NOT NULL,
        concept_class_concept_id INTEGER NOT NULL
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS concept_relationship (
        concept_id_1 INTEGER NOT NULL,
        concept_id_2 INTEGER NOT NULL,
        relationship_id TEXT NOT NULL,
        valid_start_date TEXT NOT NULL,
        valid_end_date TEXT NOT NULL,
        invalid_reason TEXT,
        PRIMARY KEY (concept_id_1, concept_id_2, relationship_id)
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS relationship (
        relationship_id TEXT PRIMARY KEY,
        relationship_name TEXT NOT NULL,
        is_hierarchical TEXT NOT NULL,
        defines_ancestry TEXT NOT NULL,
        reverse_relationship_id TEXT NOT NULL,
        relationship_concept_id INTEGER NOT NULL
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS concept_synonym (
        concept_id INTEGER NOT NULL,
        concept_synonym_name TEXT NOT NULL,
        language_concept_id INTEGER NOT NULL
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS concept_ancestor (
        ancestor_concept_id INTEGER NOT NULL,
        descendant_concept_id INTEGER NOT NULL,
        min_levels_of_separation INTEGER NOT NULL,
        max_levels_of_separation INTEGER NOT NULL,
        PRIMARY KEY (ancestor_concept_id, descendant_concept_id)
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS source_to_concept_map (
        source_code TEXT NOT NULL,
        source_concept_id INTEGER NOT NULL,
        source_vocabulary_id TEXT NOT NULL,
        source_code_description TEXT,
        target_concept_id INTEGER NOT NULL,
        target_vocabulary_id TEXT NOT NULL,
        valid_start_date TEXT NOT NULL,
        valid_end_date TEXT NOT NULL,
        invalid_reason TEXT
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS drug_strength (
        drug_concept_id INTEGER NOT NULL,
        ingredient_concept_id INTEGER NOT NULL,
        amount_value NUMERIC NULL,
        amount_unit_concept_id INTEGER NULL,
        numerator_value NUMERIC NULL,
        numerator_unit_concept_id INTEGER NULL,
        denominator_value NUMERIC NULL,
        denominator_unit_concept_id INTEGER NULL,
        box_size INTEGER NULL,
        valid_start_date TEXT NOT NULL,
        valid_end_date TEXT NOT NULL,
        invalid_reason TEXT
    )
    ''')
    
    # Create clinical tables
    print("Creating clinical tables...")
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS person (
        person_id INTEGER PRIMARY KEY,
        gender_concept_id INTEGER NOT NULL,
        year_of_birth INTEGER NOT NULL,
        month_of_birth INTEGER,
        day_of_birth INTEGER,
        birth_datetime TEXT,
        race_concept_id INTEGER NOT NULL,
        ethnicity_concept_id INTEGER NOT NULL,
        location_id INTEGER,
        provider_id INTEGER,
        care_site_id INTEGER,
        person_source_value TEXT,
        gender_source_value TEXT,
        gender_source_concept_id INTEGER,
        race_source_value TEXT,
        race_source_concept_id INTEGER,
        ethnicity_source_value TEXT,
        ethnicity_source_concept_id INTEGER
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS observation_period (
        observation_period_id INTEGER PRIMARY KEY,
        person_id INTEGER NOT NULL,
        observation_period_start_date TEXT NOT NULL,
        observation_period_end_date TEXT NOT NULL,
        period_type_concept_id INTEGER NOT NULL
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS visit_occurrence (
        visit_occurrence_id INTEGER PRIMARY KEY,
        person_id INTEGER NOT NULL,
        visit_concept_id INTEGER NOT NULL,
        visit_start_date TEXT NOT NULL,
        visit_start_datetime TEXT,
        visit_end_date TEXT NOT NULL,
        visit_end_datetime TEXT,
        visit_type_concept_id INTEGER NOT NULL,
        provider_id INTEGER,
        care_site_id INTEGER,
        visit_source_value TEXT,
        visit_source_concept_id INTEGER,
        admitted_from_concept_id INTEGER,
        admitted_from_source_value TEXT,
        discharge_to_concept_id INTEGER,
        discharge_to_source_value TEXT,
        preceding_visit_occurrence_id INTEGER
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS visit_detail (
        visit_detail_id INTEGER PRIMARY KEY,
        person_id INTEGER NOT NULL,
        visit_detail_concept_id INTEGER NOT NULL,
        visit_detail_start_date TEXT NOT NULL,
        visit_detail_start_datetime TEXT,
        visit_detail_end_date TEXT NOT NULL,
        visit_detail_end_datetime TEXT,
        visit_detail_type_concept_id INTEGER NOT NULL,
        provider_id INTEGER,
        care_site_id INTEGER,
        visit_detail_source_value TEXT,
        visit_detail_source_concept_id INTEGER,
        admitted_from_concept_id INTEGER,
        admitted_from_source_value TEXT,
        discharged_to_source_value TEXT,
        discharged_to_concept_id INTEGER,
        preceding_visit_detail_id INTEGER,
        parent_visit_detail_id INTEGER,
        visit_occurrence_id INTEGER NOT NULL
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS condition_occurrence (
        condition_occurrence_id INTEGER PRIMARY KEY,
        person_id INTEGER NOT NULL,
        condition_concept_id INTEGER NOT NULL,
        condition_start_date TEXT NOT NULL,
        condition_start_datetime TEXT,
        condition_end_date TEXT,
        condition_end_datetime TEXT,
        condition_type_concept_id INTEGER NOT NULL,
        condition_status_concept_id INTEGER,
        stop_reason TEXT,
        provider_id INTEGER,
        visit_occurrence_id INTEGER,
        visit_detail_id INTEGER,
        condition_source_value TEXT,
        condition_source_concept_id INTEGER,
        condition_status_source_value TEXT
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS drug_exposure (
        drug_exposure_id INTEGER PRIMARY KEY,
        person_id INTEGER NOT NULL,
        drug_concept_id INTEGER NOT NULL,
        drug_exposure_start_date TEXT NOT NULL,
        drug_exposure_start_datetime TEXT,
        drug_exposure_end_date TEXT NOT NULL,
        drug_exposure_end_datetime TEXT,
        verbatim_end_date TEXT,
        drug_type_concept_id INTEGER NOT NULL,
        stop_reason TEXT,
        refills INTEGER,
        quantity NUMERIC,
        days_supply INTEGER,
        sig TEXT,
        route_concept_id INTEGER,
        lot_number TEXT,
        provider_id INTEGER,
        visit_occurrence_id INTEGER,
        visit_detail_id INTEGER,
        drug_source_value TEXT,
        drug_source_concept_id INTEGER,
        route_source_value TEXT,
        dose_unit_source_value TEXT
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS procedure_occurrence (
        procedure_occurrence_id INTEGER PRIMARY KEY,
        person_id INTEGER NOT NULL,
        procedure_concept_id INTEGER NOT NULL,
        procedure_date TEXT NOT NULL,
        procedure_datetime TEXT,
        procedure_end_date TEXT,
        procedure_end_datetime TEXT,
        procedure_type_concept_id INTEGER NOT NULL,
        modifier_concept_id INTEGER,
        quantity INTEGER,
        provider_id INTEGER,
        visit_occurrence_id INTEGER,
        visit_detail_id INTEGER,
        procedure_source_value TEXT,
        procedure_source_concept_id INTEGER,
        modifier_source_value TEXT
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS measurement (
        measurement_id INTEGER PRIMARY KEY,
        person_id INTEGER NOT NULL,
        measurement_concept_id INTEGER NOT NULL,
        measurement_date TEXT NOT NULL,
        measurement_datetime TEXT,
        measurement_time TEXT,
        measurement_type_concept_id INTEGER NOT NULL,
        operator_concept_id INTEGER,
        value_as_number NUMERIC,
        value_as_concept_id INTEGER,
        unit_concept_id INTEGER,
        range_low NUMERIC,
        range_high NUMERIC,
        provider_id INTEGER,
        visit_occurrence_id INTEGER,
        visit_detail_id INTEGER,
        measurement_source_value TEXT,
        measurement_source_concept_id INTEGER,
        unit_source_value TEXT,
        unit_source_concept_id INTEGER,
        value_source_value TEXT,
        measurement_event_id INTEGER,
        meas_event_field_concept_id INTEGER
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS observation (
        observation_id INTEGER PRIMARY KEY,
        person_id INTEGER NOT NULL,
        observation_concept_id INTEGER NOT NULL,
        observation_date TEXT NOT NULL,
        observation_datetime TEXT,
        observation_type_concept_id INTEGER NOT NULL,
        value_as_number NUMERIC,
        value_as_string TEXT,
        value_as_concept_id INTEGER,
        qualifier_concept_id INTEGER,
        unit_concept_id INTEGER,
        provider_id INTEGER,
        visit_occurrence_id INTEGER,
        visit_detail_id INTEGER,
        observation_source_value TEXT,
        observation_source_concept_id INTEGER,
        unit_source_value TEXT,
        qualifier_source_value TEXT,
        value_source_value TEXT,
        observation_event_id INTEGER,
        obs_event_field_concept_id INTEGER
    )
    ''')
    
    # Create derived tables
    print("Creating derived tables...")
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS drug_era (
        drug_era_id INTEGER PRIMARY KEY,
        person_id INTEGER NOT NULL,
        drug_concept_id INTEGER NOT NULL,
        drug_era_start_date TEXT NOT NULL,
        drug_era_end_date TEXT NOT NULL,
        drug_exposure_count INTEGER,
        gap_days INTEGER
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS dose_era (
        dose_era_id INTEGER PRIMARY KEY,
        person_id INTEGER NOT NULL,
        drug_concept_id INTEGER NOT NULL,
        unit_concept_id INTEGER NOT NULL,
        dose_value NUMERIC NOT NULL,
        dose_era_start_date TEXT NOT NULL,
        dose_era_end_date TEXT NOT NULL
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS condition_era (
        condition_era_id INTEGER PRIMARY KEY,
        person_id INTEGER NOT NULL,
        condition_concept_id INTEGER NOT NULL,
        condition_era_start_date TEXT NOT NULL,
        condition_era_end_date TEXT NOT NULL,
        condition_occurrence_count INTEGER
    )
    ''')
    
    # Create indices for better performance
    print("Creating indices...")
    
    # Indices for vocabulary tables
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_concept_concept_id ON concept(concept_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_concept_code ON concept(concept_code)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_concept_vocab ON concept(vocabulary_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_concept_domain ON concept(domain_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_concept_class ON concept(concept_class_id)')
    
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_relationship_c1 ON concept_relationship(concept_id_1)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_relationship_c2 ON concept_relationship(concept_id_2)')
    
    # Indices for clinical tables
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_person_id ON person(person_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_observation_period_person ON observation_period(person_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_visit_person ON visit_occurrence(person_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_condition_person ON condition_occurrence(person_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_drug_person ON drug_exposure(person_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_procedure_person ON procedure_occurrence(person_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_measurement_person ON measurement(person_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_observation_person ON observation(person_id)')
    
    # Commit changes and close connection
    conn.commit()
    conn.close()
    
    print(f"OMOP CDM database successfully created at: {DB_PATH}")
    print("Tables created:")
    print("- Vocabulary: concept, vocabulary, domain, concept_class, concept_relationship, relationship, concept_synonym, concept_ancestor, source_to_concept_map, drug_strength")
    print("- Clinical: person, observation_period, visit_occurrence, visit_detail, condition_occurrence, drug_exposure, procedure_occurrence, measurement, observation")
    print("- Derived: drug_era, dose_era, condition_era")

if __name__ == "__main__":
    create_omop_tables()
```

## Step 2: Update Environment Configuration

Update your `.env` file to use SQLite:

```
# OMOP Database Configuration
OMOP_DB_CONNECTION_STRING=sqlite:///omop_test.db
```

## Step 3: Create the Database

Run the Python script to create the SQLite database:

```bash
python scripts/create_omop_tables_sqlite.py
```

This will create a file named `omop_test.db` with all the OMOP CDM tables.

## Step 4: Verify the Database Structure

You can verify the database structure using the SQLite command-line tool:

```bash
# List all tables
sqlite3 omop_test.db ".tables"

# Show schema for a specific table
sqlite3 omop_test.db ".schema person"
```

## Step 5: Load Minimal Vocabulary Data

For testing purposes, you can load a minimal set of vocabulary data. Create a script named `load_vocabularies_sqlite.py`:

```python
#!/usr/bin/env python
"""
Script to load minimal vocabulary data into SQLite OMOP CDM database.
This loads just enough vocabulary data for basic testing.
"""
import os
import sqlite3
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get database path from .env or use default
DB_PATH = os.getenv("OMOP_DB_CONNECTION_STRING", "sqlite:///omop_test.db").replace("sqlite:///", "")

def load_minimal_vocabulary():
    """Load minimal vocabulary data for testing."""
    print(f"Loading minimal vocabulary data into: {DB_PATH}")
    
    # Connect to SQLite database
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # Insert minimal vocabulary data
    
    # 1. Add vocabulary records
    print("Adding vocabulary records...")
    vocabularies = [
        ('SNOMED', 'Systematic Nomenclature of Medicine - Clinical Terms', 'http://www.snomed.org', 'OMOP generated', 44819096),
        ('RxNorm', 'RxNorm', 'http://www.nlm.nih.gov/research/umls/rxnorm', 'OMOP generated', 44819104),
        ('LOINC', 'Logical Observation Identifiers Names and Codes', 'http://loinc.org', 'OMOP generated', 44819102)
    ]
    cursor.executemany(
        'INSERT OR IGNORE INTO vocabulary (vocabulary_id, vocabulary_name, vocabulary_reference, vocabulary_version, vocabulary_concept_id) VALUES (?, ?, ?, ?, ?)',
        vocabularies
    )
    
    # 2. Add domain records
    print("Adding domain records...")
    domains = [
        ('Condition', 'Condition', 19),
        ('Device', 'Device', 13),
        ('Drug', 'Drug', 13),
        ('Measurement', 'Measurement', 21),
        ('Observation', 'Observation', 27),
        ('Procedure', 'Procedure', 10),
        ('Visit', 'Visit', 8)
    ]
    cursor.executemany(
        'INSERT OR IGNORE INTO domain (domain_id, domain_name, domain_concept_id) VALUES (?, ?, ?)',
        domains
    )
    
    # 3. Add concept class records
    print("Adding concept class records...")
    concept_classes = [
        ('Clinical Finding', 'Clinical Finding', 44819123),
        ('Procedure', 'Procedure', 44819124),
        ('Drug Product', 'Drug Product', 44819127),
        ('Lab Test', 'Lab Test', 44819131)
    ]
    cursor.executemany(
        'INSERT OR IGNORE INTO concept_class (concept_class_id, concept_class_name, concept_class_concept_id) VALUES (?, ?, ?)',
        concept_classes
    )
    
    # 4. Add minimal concepts
    print("Adding minimal concepts...")
    concepts = [
        # Gender concepts
        (8507, 'Male', 'Gender', 'SNOMED', 'Administrative concept', 'S', '248153007', '1970-01-01', '2099-12-31', None),
        (8532, 'Female', 'Gender', 'SNOMED', 'Administrative concept', 'S', '248152002', '1970-01-01', '2099-12-31', None),
        (8551, 'Unknown', 'Gender', 'SNOMED', 'Administrative concept', 'S', '261665006', '1970-01-01', '2099-12-31', None),
        
        # Race concepts
        (8516, 'Black', 'Race', 'SNOMED', 'Administrative concept', 'S', '415229000', '1970-01-01', '2099-12-31', None),
        (8515, 'Asian', 'Race', 'SNOMED', 'Administrative concept', 'S', '413582008', '1970-01-01', '2099-12-31', None),
        (8527, 'White', 'Race', 'SNOMED', 'Administrative concept', 'S', '413100009', '1970-01-01', '2099-12-31', None),
        
        # Ethnicity concepts
        (38003563, 'Hispanic', 'Ethnicity', 'Ethnicity', 'Ethnicity', 'S', 'Hispanic', '1970-01-01', '2099-12-31', None),
        (38003564, 'Not Hispanic', 'Ethnicity', 'Ethnicity', 'Ethnicity', 'S', 'Not Hispanic', '1970-01-01', '2099-12-31', None),
        
        # Visit concepts
        (9201, 'Inpatient Visit', 'Visit', 'Visit', 'Visit', 'S', 'IP', '1970-01-01', '2099-12-31', None),
        (9202, 'Outpatient Visit', 'Visit', 'Visit', 'Visit', 'S', 'OP', '1970-01-01', '2099-12-31', None),
        (9203, 'Emergency Room Visit', 'Visit', 'Visit', 'Visit', 'S', 'ER', '1970-01-01', '2099-12-31', None),
        
        # Type concepts
        (32020, 'EHR problem list entry', 'Type Concept', 'Condition Type', 'Type Concept', 'S', 'EHR problem list entry', '1970-01-01', '2099-12-31', None),
        (32817, 'EHR observation', 'Type Concept', 'Observation Type', 'Type Concept', 'S', 'EHR observation', '1970-01-01', '2099-12-31', None),
        (32856, 'EHR measurement', 'Type Concept', 'Measurement Type', 'Type Concept', 'S', 'EHR measurement', '1970-01-01', '2099-12-31', None)
    ]
    cursor.executemany(
        'INSERT OR IGNORE INTO concept (concept_id, concept_name, domain_id, vocabulary_id, concept_class_id, standard_concept, concept_code, valid_start_date, valid_end_date, invalid_reason) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        concepts
    )
    
    # Commit changes and close connection
    conn.commit()
    conn.close()
    
    print(f"Minimal vocabulary data loaded successfully into: {DB_PATH}")
    print("Added:")
    print("- Basic vocabularies (SNOMED, RxNorm, LOINC)")
    print("- Essential domains")
    print("- Common concept classes")
    print("- Minimal concepts for testing (gender, race, ethnicity, visit types)")

if __name__ == "__main__":
    load_minimal_vocabulary()
```

Run the script to load minimal vocabulary data:

```bash
python scripts/load_vocabularies_sqlite.py
```

## Step 6: Verify the Vocabulary Data

Check that the vocabulary data was loaded correctly:

```bash
# Count concepts
sqlite3 omop_test.db "SELECT COUNT(*) FROM concept;"

# Check available vocabularies
sqlite3 omop_test.db "SELECT vocabulary_id, vocabulary_name FROM vocabulary;"
```

## Step 7: Create a Script to Verify the Database

Create a verification script named `verify_sqlite_database.py`:

```python
#!/usr/bin/env python
"""
Script to verify the SQLite OMOP CDM database setup.
"""
import os
import sqlite3
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get database path from .env or use default
DB_PATH = os.getenv("OMOP_DB_CONNECTION_STRING", "sqlite:///omop_test.db").replace("sqlite:///", "")

def verify_database():
    """Verify the SQLite OMOP CDM database setup."""
    print(f"Verifying OMOP CDM database at: {DB_PATH}")
    
    # Check if database file exists
    if not os.path.exists(DB_PATH):
        print(f"❌ Database file not found: {DB_PATH}")
        return False
    
    try:
        # Connect to SQLite database
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # 1. Check tables
        print("\nChecking tables...")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]
        
        expected_tables = [
            'concept', 'vocabulary', 'domain', 'concept_class', 'concept_relationship',
            'relationship', 'concept_synonym', 'concept_ancestor', 'source_to_concept_map',
            'drug_strength', 'person', 'observation_period', 'visit_occurrence',
            'visit_detail', 'condition_occurrence', 'drug_exposure', 'procedure_occurrence',
            'measurement', 'observation', 'drug_era', 'dose_era', 'condition_era'
        ]
        
        for table in expected_tables:
            if table in tables:
                print(f"✅ Table exists: {table}")
            else:
                print(f"❌ Table missing: {table}")
        
        # 2. Check vocabulary data
        print("\nChecking vocabulary data...")
        cursor.execute("SELECT COUNT(*) FROM vocabulary;")
        vocab_count = cursor.fetchone()[0]
        print(f"Vocabulary count: {vocab_count}")
        
        cursor.execute("SELECT COUNT(*) FROM concept;")
        concept_count = cursor.fetchone()[0]
        print(f"Concept count: {concept_count}")
        
        # 3. Check indices
        print("\nChecking indices...")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index';")
        indices = [row[0] for row in cursor.fetchall()]
        print(f"Total indices: {len(indices)}")
        
        # Close connection
        conn.close()
        
        print("\n✅ Database verification complete!")
        return True
        
    except sqlite3.Error as e:
        print(f"❌ SQLite error: {e}")
        return False

if __name__ == "__main__":
    verify_database()
```

Run the verification script:

```bash
python scripts/verify_sqlite_database.py
```

## Migrating to a Production Database

When you're ready to move to a production environment, you should migrate your data to a fully supported database system like PostgreSQL. See our [PostgreSQL Setup Guide](database_postgresql_setup.md) for instructions.

## Troubleshooting

### Database File Permission Issues
- Check file permissions: `ls -l omop_test.db`
- Ensure your user has read/write access

### SQLite Version Issues
- Check your SQLite version: `sqlite3 --version`
- SQLite 3.8.0 or higher is recommended

### Memory Issues with Large Operations
- SQLite has memory limitations for large operations
- Consider using smaller batches for data loading

## References

1. [OHDSI Common Data Model](https://ohdsi.github.io/CommonDataModel/)
2. [SQLite Documentation](https://www.sqlite.org/docs.html)
3. [OMOP CDM v5.4.2 Specifications](https://github.com/OHDSI/CommonDataModel/tree/v5.4.2)
4. [Python SQLite3 Documentation](https://docs.python.org/3/library/sqlite3.html)
