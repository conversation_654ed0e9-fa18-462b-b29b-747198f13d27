# FHIR Terminology Server API Examples

## Overview

FHIR Terminology Services provide a standardized way to access and manage healthcare terminologies through a set of RESTful APIs. These services are essential for implementing vocabulary mapping strategies in FHIR to OMOP transformation pipelines, as they allow for the validation, lookup, and translation of codes between different terminology systems.

This document provides examples of using FHIR Terminology Server APIs, focusing on HAPI FHIR as the implementation platform, and demonstrates how these APIs can be integrated into a FHIR to OMOP transformation pipeline.

## FHIR Terminology Resources

FHIR defines several resources for representing and working with terminologies:

1. **CodeSystem**: Defines a set of codes with meanings (also known as enumeration, terminology, classification, or ontology)
2. **ValueSet**: Specifies a set of codes drawn from one or more code systems
3. **ConceptMap**: Defines mappings between concepts in different code systems
4. **NamingSystem**: Identifies a system of unique identification
5. **TerminologyCapabilities**: Documents the terminology capabilities of a server

## Setting Up a FHIR Terminology Server

### Using HAPI FHIR JPA Server

HAPI FHIR JPA Server provides a robust implementation of FHIR terminology services. Here's how to set it up:

1. **Clone the repository**:
   ```bash
   git clone https://github.com/hapifhir/hapi-fhir-jpaserver-starter.git
   ```

2. **Run HAPI FHIR JPA**:
   ```bash
   mvn -Djetty.port=9999 jetty:run
   ```

3. **Install HAPI FHIR CLI**:
   For macOS:
   ```bash
   brew install hapi-fhir-cli
   ```
   For other operating systems, follow the [official documentation](https://hapifhir.io/hapi-fhir/docs/tools/hapi_fhir_cli.html).

4. **Upload terminology**:
   ```bash
   hapi-fhir-cli upload-terminology -d ./SnomedCT_InternationalRF2_PRODUCTION_20210131T120000Z.zip -t http://localhost:9999/fhir -u http://snomed.info/sct -v r4
   ```

## FHIR Terminology Service API Operations

### 1. CodeSystem Operations

#### $lookup Operation

The `$lookup` operation is used to retrieve information about a specific code in a code system.

**Example Request**:
```http
GET [base]/CodeSystem/$lookup?system=http://snomed.info/sct&code=73211009
```

**Example Response**:
```json
{
  "resourceType": "Parameters",
  "parameter": [
    {
      "name": "name",
      "valueString": "Diabetes mellitus"
    },
    {
      "name": "display",
      "valueString": "Diabetes mellitus"
    },
    {
      "name": "abstract",
      "valueBoolean": false
    }
  ]
}
```

#### $validate-code Operation

The `$validate-code` operation checks if a code is valid in a code system.

**Example Request**:
```http
GET [base]/CodeSystem/$validate-code?system=http://snomed.info/sct&code=73211009
```

**Example Response**:
```json
{
  "resourceType": "Parameters",
  "parameter": [
    {
      "name": "result",
      "valueBoolean": true
    },
    {
      "name": "display",
      "valueString": "Diabetes mellitus"
    }
  ]
}
```

#### $subsumes Operation

The `$subsumes` operation tests if a code is subsumed by another code in a code system.

**Example Request**:
```http
GET [base]/CodeSystem/$subsumes?system=http://snomed.info/sct&codeA=73211009&codeB=44054006
```

**Example Response**:
```json
{
  "resourceType": "Parameters",
  "parameter": [
    {
      "name": "outcome",
      "valueCode": "subsumed-by"
    }
  ]
}
```

### 2. ValueSet Operations

#### $expand Operation

The `$expand` operation expands a value set into a list of codes.

**Example Request**:
```http
GET [base]/ValueSet/$expand?url=http://hl7.org/fhir/ValueSet/condition-code
```

**Example Response**:
```json
{
  "resourceType": "ValueSet",
  "expansion": {
    "identifier": "urn:uuid:8230a24e-3181-4c2d-9d0a-2c3bf9d5d9f6",
    "timestamp": "2023-04-11T12:00:00Z",
    "total": 3,
    "contains": [
      {
        "system": "http://snomed.info/sct",
        "code": "73211009",
        "display": "Diabetes mellitus"
      },
      {
        "system": "http://snomed.info/sct",
        "code": "44054006",
        "display": "Type 2 diabetes mellitus"
      },
      {
        "system": "http://snomed.info/sct",
        "code": "46635009",
        "display": "Type 1 diabetes mellitus"
      }
    ]
  }
}
```

#### $validate-code Operation

The `$validate-code` operation checks if a code is in a value set.

**Example Request**:
```http
GET [base]/ValueSet/$validate-code?url=http://hl7.org/fhir/ValueSet/condition-code&system=http://snomed.info/sct&code=73211009
```

**Example Response**:
```json
{
  "resourceType": "Parameters",
  "parameter": [
    {
      "name": "result",
      "valueBoolean": true
    },
    {
      "name": "display",
      "valueString": "Diabetes mellitus"
    }
  ]
}
```

### 3. ConceptMap Operations

#### $translate Operation

The `$translate` operation translates a code from one system to another using a concept map.

**Example Request**:
```http
GET [base]/ConceptMap/$translate?system=http://snomed.info/sct&code=73211009&target=http://hl7.org/fhir/sid/icd-10
```

**Example Response**:
```json
{
  "resourceType": "Parameters",
  "parameter": [
    {
      "name": "result",
      "valueBoolean": true
    },
    {
      "name": "match",
      "part": [
        {
          "name": "equivalence",
          "valueCode": "equivalent"
        },
        {
          "name": "concept",
          "valueCoding": {
            "system": "http://hl7.org/fhir/sid/icd-10",
            "code": "E11",
            "display": "Type 2 diabetes mellitus"
          }
        }
      ]
    }
  ]
}
```

## Python Examples for FHIR Terminology Service API

### Example 1: Code Lookup

```python
import requests
import json

def lookup_code(base_url, system, code):
    """
    Look up a code in a code system
    
    Args:
        base_url (str): The base URL of the FHIR server
        system (str): The code system URL
        code (str): The code to look up
        
    Returns:
        dict: The response from the server
    """
    url = f"{base_url}/CodeSystem/$lookup"
    params = {
        "system": system,
        "code": code
    }
    
    response = requests.get(url, params=params)
    
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Error looking up code: {response.status_code} - {response.text}")

# Example usage
base_url = "http://localhost:9999/fhir"
system = "http://snomed.info/sct"
code = "73211009"  # Diabetes mellitus

result = lookup_code(base_url, system, code)
print(json.dumps(result, indent=2))
```

### Example 2: Value Set Expansion

```python
import requests
import json

def expand_valueset(base_url, valueset_url):
    """
    Expand a value set
    
    Args:
        base_url (str): The base URL of the FHIR server
        valueset_url (str): The URL of the value set to expand
        
    Returns:
        dict: The expanded value set
    """
    url = f"{base_url}/ValueSet/$expand"
    params = {
        "url": valueset_url
    }
    
    response = requests.get(url, params=params)
    
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Error expanding value set: {response.status_code} - {response.text}")

# Example usage
base_url = "http://localhost:9999/fhir"
valueset_url = "http://hl7.org/fhir/ValueSet/condition-code"

result = expand_valueset(base_url, valueset_url)
print(json.dumps(result, indent=2))
```

### Example 3: Code Translation

```python
import requests
import json

def translate_code(base_url, source_system, source_code, target_system):
    """
    Translate a code from one system to another
    
    Args:
        base_url (str): The base URL of the FHIR server
        source_system (str): The source code system URL
        source_code (str): The code to translate
        target_system (str): The target code system URL
        
    Returns:
        dict: The translation result
    """
    url = f"{base_url}/ConceptMap/$translate"
    params = {
        "system": source_system,
        "code": source_code,
        "target": target_system
    }
    
    response = requests.get(url, params=params)
    
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Error translating code: {response.status_code} - {response.text}")

# Example usage
base_url = "http://localhost:9999/fhir"
source_system = "http://snomed.info/sct"
source_code = "73211009"  # Diabetes mellitus
target_system = "http://hl7.org/fhir/sid/icd-10"

result = translate_code(base_url, source_system, source_code, target_system)
print(json.dumps(result, indent=2))
```

## Integration with FHIR to OMOP Transformation

When building a FHIR to OMOP transformation pipeline, FHIR Terminology Services can be used to:

1. **Validate Codes**: Ensure that codes in FHIR resources are valid before mapping them to OMOP concepts
2. **Look Up Code Information**: Retrieve additional information about codes that may be needed for mapping
3. **Translate Codes**: Convert codes from one terminology system to another as part of the mapping process

### Example: Using Terminology Services in a FHIR to OMOP Mapper

```python
import requests
import json
import sqlalchemy as sa

class FhirToOmopMapper:
    def __init__(self, fhir_server_url, omop_db_connection_string):
        """
        Initialize the mapper
        
        Args:
            fhir_server_url (str): The URL of the FHIR server
            omop_db_connection_string (str): The connection string for the OMOP database
        """
        self.fhir_server_url = fhir_server_url
        self.engine = sa.create_engine(omop_db_connection_string)
    
    def validate_code(self, system, code):
        """
        Validate a code using the FHIR terminology service
        
        Args:
            system (str): The code system URL
            code (str): The code to validate
            
        Returns:
            bool: True if the code is valid, False otherwise
        """
        url = f"{self.fhir_server_url}/CodeSystem/$validate-code"
        params = {
            "system": system,
            "code": code
        }
        
        response = requests.get(url, params=params)
        
        if response.status_code == 200:
            result = response.json()
            return result.get("parameter", [{}])[0].get("valueBoolean", False)
        else:
            return False
    
    def translate_to_standard_concept(self, system, code):
        """
        Translate a code to an OMOP standard concept
        
        Args:
            system (str): The code system URL
            code (str): The code to translate
            
        Returns:
            int: The OMOP concept_id, or None if not found
        """
        # First, try to find a direct mapping in the OMOP database
        with self.engine.connect() as conn:
            query = """
            SELECT 
                c.concept_id
            FROM 
                concept c
            WHERE 
                c.vocabulary_id = :vocabulary_id
                AND c.concept_code = :concept_code
                AND c.standard_concept = 'S'
            """
            
            # Map FHIR system URL to OMOP vocabulary_id
            vocabulary_id = self._map_system_to_vocabulary(system)
            
            result = conn.execute(sa.text(query), {
                "vocabulary_id": vocabulary_id,
                "concept_code": code
            }).fetchone()
            
            if result:
                return result[0]
        
        # If not found, try using the FHIR terminology service to translate
        url = f"{self.fhir_server_url}/ConceptMap/$translate"
        params = {
            "system": system,
            "code": code,
            "target": "http://terminology.hl7.org/CodeSystem/v3-snomed"  # Target SNOMED CT
        }
        
        response = requests.get(url, params=params)
        
        if response.status_code == 200:
            result = response.json()
            for param in result.get("parameter", []):
                if param.get("name") == "match":
                    for part in param.get("part", []):
                        if part.get("name") == "concept":
                            target_code = part.get("valueCoding", {}).get("code")
                            if target_code:
                                # Look up the SNOMED CT code in OMOP
                                with self.engine.connect() as conn:
                                    query = """
                                    SELECT 
                                        c.concept_id
                                    FROM 
                                        concept c
                                    WHERE 
                                        c.vocabulary_id = 'SNOMED'
                                        AND c.concept_code = :concept_code
                                        AND c.standard_concept = 'S'
                                    """
                                    
                                    result = conn.execute(sa.text(query), {
                                        "concept_code": target_code
                                    }).fetchone()
                                    
                                    if result:
                                        return result[0]
        
        return None
    
    def _map_system_to_vocabulary(self, system):
        """
        Map a FHIR system URL to an OMOP vocabulary_id
        
        Args:
            system (str): The FHIR system URL
            
        Returns:
            str: The OMOP vocabulary_id
        """
        system_to_vocabulary = {
            "http://snomed.info/sct": "SNOMED",
            "http://loinc.org": "LOINC",
            "http://www.nlm.nih.gov/research/umls/rxnorm": "RxNorm",
            "http://hl7.org/fhir/sid/icd-10": "ICD10",
            "http://hl7.org/fhir/sid/icd-9-cm": "ICD9CM"
        }
        
        return system_to_vocabulary.get(system)
    
    def map_condition(self, fhir_condition):
        """
        Map a FHIR Condition resource to OMOP CONDITION_OCCURRENCE
        
        Args:
            fhir_condition (dict): The FHIR Condition resource
            
        Returns:
            dict: The OMOP CONDITION_OCCURRENCE record
        """
        # Extract the code from the FHIR Condition
        coding = None
        for c in fhir_condition.get("code", {}).get("coding", []):
            if self.validate_code(c.get("system"), c.get("code")):
                coding = c
                break
        
        if not coding:
            return None
        
        # Translate to OMOP standard concept
        concept_id = self.translate_to_standard_concept(coding.get("system"), coding.get("code"))
        
        if not concept_id:
            return None
        
        # Map other fields
        # ... (implementation details)
        
        return {
            "condition_concept_id": concept_id,
            # ... other mapped fields
        }

# Example usage
mapper = FhirToOmopMapper(
    fhir_server_url="http://localhost:9999/fhir",
    omop_db_connection_string="postgresql://username:password@localhost:5432/omop_cdm"
)

fhir_condition = {
    "resourceType": "Condition",
    "id": "example",
    "code": {
        "coding": [
            {
                "system": "http://snomed.info/sct",
                "code": "73211009",
                "display": "Diabetes mellitus"
            }
        ]
    }
    # ... other fields
}

omop_condition = mapper.map_condition(fhir_condition)
print(json.dumps(omop_condition, indent=2))
```

## Best Practices for Using FHIR Terminology Services

1. **Cache Terminology Data**: FHIR terminology operations can be computationally expensive. Implement caching to improve performance.

2. **Use Batch Operations**: When processing multiple codes, use batch operations to reduce the number of API calls.

3. **Handle Versioning**: Be aware of terminology versioning. Specify the version when necessary to ensure consistent results.

4. **Error Handling**: Implement robust error handling for terminology service calls, as they may fail due to network issues or server limitations.

5. **Fallback Mechanisms**: Have fallback mechanisms in place when terminology services are unavailable, such as local copies of commonly used terminologies.

6. **Monitor Performance**: Monitor the performance of terminology service calls and optimize as needed.

7. **Validate Before Mapping**: Always validate codes before attempting to map them to OMOP concepts to ensure data quality.

## References

1. HAPI FHIR Terminology Documentation: https://hapifhir.io/hapi-fhir/docs/server_jpa/terminology.html
2. FHIR Terminology Service: https://medblocks.com/blog/terminologies-in-fhir
3. HL7 FHIR Terminology Module: https://www.hl7.org/fhir/terminology-module.html
4. FHIR Terminology Services: https://confluence.hl7.org/display/FHIR/Terminology+Services
