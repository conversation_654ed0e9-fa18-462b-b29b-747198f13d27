# Prerequisites and Environment Setup

This section covers the verification of your current technology stack and the installation of any missing components required for the FHIR server deployment.

## Phase 1: FHIR Server Setup and Basic Interaction

### Stage 1: Verify Current Tech Stack

**Objective**: Assess the current technology environment to identify what's already installed and what needs to be installed.

#### Step 1: Verify Environment

Run the following commands to check your current environment:

```bash
# Check OS version
uname -a

# For macOS, also run:
sw_vers

# Check if Docker is installed and its version (need 20.10.x+)
docker --version

# Check Docker Compose (modern Docker Desktop uses 'docker compose')
docker compose version

# Check if Python is installed and its version (need 3.8+)
python --version

# Check if Git is installed and its version
git --version

# Check if ports 8080 (HAPI FHIR) and 5432 (PostgreSQL) are available
lsof -i :8080
lsof -i :5432
```

### Docker Path Configuration

If Docker is installed via Docker Desktop but not available in your PATH, you can add it with:

```bash
# For Bash
echo 'export PATH=$PATH:/Applications/Docker.app/Contents/Resources/bin' >> ~/.bashrc
source ~/.bashrc

# For Zsh
echo 'export PATH=$PATH:/Applications/Docker.app/Contents/Resources/bin' >> ~/.zshrc
source ~/.zshrc
```

### Stage 2: Install Missing Components

**Objective**: Install any missing components required for the development environment.

### Installing Docker

#### For macOS:
1. Download Docker Desktop from [Docker's official website](https://www.docker.com/products/docker-desktop)
2. Install the downloaded .dmg file
3. Start Docker Desktop from the Applications folder

#### For Windows:
1. Download Docker Desktop from [Docker's official website](https://www.docker.com/products/docker-desktop)
2. Run the installer and follow the prompts
3. Start Docker Desktop from the Start menu

#### For Linux (Ubuntu):
```bash
# Update package index
sudo apt-get update

# Install prerequisites
sudo apt-get install -y apt-transport-https ca-certificates curl software-properties-common

# Add Docker's official GPG key
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -

# Add Docker repository
sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"

# Update package index again
sudo apt-get update

# Install Docker CE
sudo apt-get install -y docker-ce

# Add your user to the docker group to run Docker without sudo
sudo usermod -aG docker $USER

# Apply group changes (or log out and back in)
newgrp docker
```

### Installing Python

#### For macOS:
```bash
# Using Homebrew
brew install python

# Or download from Python's official website
# https://www.python.org/downloads/
```

#### For Windows:
1. Download Python from [Python's official website](https://www.python.org/downloads/)
2. Run the installer and make sure to check "Add Python to PATH"

#### For Linux (Ubuntu):
```bash
sudo apt-get update
sudo apt-get install -y python3 python3-pip
```

### Stage 3: Learn Docker Fundamentals

**Objective**: Understand basic Docker concepts to effectively use containerized applications.

#### Step 1: Docker Terminology and Concepts

**Docker Basics**:
- **Container**: A lightweight, standalone, executable package that includes everything needed to run an application: code, runtime, system tools, libraries, and settings.
- **Image**: A read-only template used to create containers. Images are built from a set of instructions written in a Dockerfile.
- **Dockerfile**: A text file containing instructions to build a Docker image.
- **Docker Hub**: A cloud-based registry service for finding and sharing container images.
- **Docker Compose**: A tool for defining and running multi-container Docker applications using a YAML file.

**Key Docker Commands**:

```bash
# Check Docker version
docker --version

# Check Docker Compose version
docker compose version

# List running containers
docker ps

# List all containers (including stopped ones)
docker ps -a

# List images
docker images

# Pull an image from Docker Hub
docker pull [image_name]

# Run a container
docker run [image_name]

# Stop a container
docker stop [container_id]

# Remove a container
docker rm [container_id]

# Remove an image
docker rmi [image_id]

# View logs for a container
docker logs [container_id]

# Execute a command in a running container
docker exec -it [container_id] [command]

# Docker Compose commands
# Start services defined in docker-compose.yml
docker compose up -d

# Stop services
docker compose down

# View logs
docker compose logs -f

# View logs for a specific service
docker compose logs [service_name]
```

#### Step 2: Create a Simple Docker Example

```bash
# Create a directory for your Docker example
mkdir docker-hello-world
cd docker-hello-world

# Create a simple Python application
cat > app.py << EOL
print("Hello, Docker World!")
EOL

# Create a Dockerfile
cat > Dockerfile << EOL
# Use an official Python runtime as a parent image
FROM python:3.11-slim

# Set the working directory in the container
WORKDIR /app

# Copy the current directory contents into the container at /app
COPY . /app

# Run app.py when the container launches
CMD ["python", "app.py"]
EOL

# Build the image
docker build -t hello-docker .

# Run the container
docker run hello-docker
```

You should see the output: "Hello, Docker World!"

## Next Steps

Now that you've verified and set up your environment, you're ready to proceed with the [FHIR Server Installation](03-server_setup.md).
