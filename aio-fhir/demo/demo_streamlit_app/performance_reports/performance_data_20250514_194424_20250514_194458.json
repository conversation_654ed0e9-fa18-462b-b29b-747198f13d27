{"test_id": "20250514_194424", "timestamp": "20250514_194458", "system_info": {"timestamp": "2025-05-14T19:44:24.078191", "test_id": "20250514_194424", "platform": "<PERSON>", "platform_release": "24.5.0", "platform_version": "Darwin Kernel Version 24.5.0: <PERSON><PERSON> Apr 22 19:52:00 PDT 2025; root:xnu-11417.121.6~2/RELEASE_ARM64_T6031", "architecture": "arm64", "processor": "arm", "python_version": "3.9.22", "hostname": "laptop-944a4son.home", "cpu_count_logical": 14, "cpu_count_physical": 14, "total_memory_gb": 36.0, "available_memory_gb": 8.38}, "overall_stats": {"total_bundles": 46, "total_bundles_succeeded": 45, "total_bundles_failed": 1, "total_resources": 18966, "total_resources_succeeded": 18466, "total_resources_failed": 500, "total_processing_time": 10.977776765823364, "elapsed_time": 34.4035222530365, "resources_per_second": 551.2807630714624, "bundles_per_second": 1.3370723980431969, "peak_cpu_percent": 32.9, "peak_memory_percent": 76.7, "peak_memory_usage_gb": 13.77}, "resource_type_stats": {"AllergyIntolerance": {"bundles_processed": 1, "bundles_succeeded": 1, "bundles_failed": 0, "resources_processed": 11, "resources_succeeded": 11, "resources_failed": 0, "processing_time": 0.0511631965637207, "failed_bundles": []}, "Condition": {"bundles_processed": 2, "bundles_succeeded": 2, "bundles_failed": 0, "resources_processed": 555, "resources_succeeded": 555, "resources_failed": 0, "processing_time": 0.43442749977111816, "failed_bundles": []}, "Device": {"bundles_processed": 1, "bundles_succeeded": 1, "bundles_failed": 0, "resources_processed": 16, "resources_succeeded": 16, "resources_failed": 0, "processing_time": 0.026526927947998047, "failed_bundles": []}, "DiagnosticReport": {"bundles_processed": 5, "bundles_succeeded": 5, "bundles_failed": 0, "resources_processed": 2101, "resources_succeeded": 2101, "resources_failed": 0, "processing_time": 1.8231596946716309, "failed_bundles": []}, "DocumentReference": {"bundles_processed": 3, "bundles_succeeded": 3, "bundles_failed": 0, "resources_processed": 1215, "resources_succeeded": 1215, "resources_failed": 0, "processing_time": 0.869103193283081, "failed_bundles": []}, "Encounter": {"bundles_processed": 3, "bundles_succeeded": 2, "bundles_failed": 1, "resources_processed": 1215, "resources_succeeded": 715, "resources_failed": 500, "processing_time": 0.8660502433776855, "failed_bundles": [{"file_path": "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Work/AIO/fhir-omop/data/generated_bundles/bulk-export-bundles/Encounter/Encounter_bundle_2.json", "resource_type": "Encounter", "resource_count": 500, "error_message": "HAPI-0550: could not execute batch [Batch entry 8 insert into hfj_spidx_token (hash_identity,hash_sys,hash_sys_and_value,hash_value,sp_missing,sp_name,partition_date,partition_id,res_id,res_type,sp_system,sp_updated,sp_value,sp_id) values (('2962015495993695466'::int8),('4048630544910848998'::int8),('7089306791856775142'::int8),('-4876611660480196639'::int8),('FALSE'::boolean),('identifier'),(NULL),(NULL),('757918'::int8),('Location'),('https://github.com/synthetichealth/synthea'),('2025-05-14 17:44:34.439+00'),('bb1ad573-19b8-9cd8-68fb-0e6f684df992'),('3583746'::int8)) was aborted: ERROR: insert or update on table \"hfj_spidx_token\" violates foreign key constraint \"fk_sp_token_res\"\n  Detail: Key (res_id)=(757918) is not present in table \"hfj_resource\".  Call getNextException to see other errors in the batch.] [insert into hfj_spidx_token (hash_identity,hash_sys,hash_sys_and_value,hash_value,sp_missing,sp_name,partition_date,partition_id,res_id,res_type,sp_system,sp_updated,sp_value,sp_id) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?)]"}]}, "Immunization": {"bundles_processed": 1, "bundles_succeeded": 1, "bundles_failed": 0, "resources_processed": 161, "resources_succeeded": 161, "resources_failed": 0, "processing_time": 0.06698107719421387, "failed_bundles": []}, "MedicationRequest": {"bundles_processed": 4, "bundles_succeeded": 4, "bundles_failed": 0, "resources_processed": 1745, "resources_succeeded": 1745, "resources_failed": 0, "processing_time": 0.8040421009063721, "failed_bundles": []}, "Observation": {"bundles_processed": 20, "bundles_succeeded": 20, "bundles_failed": 0, "resources_processed": 9878, "resources_succeeded": 9878, "resources_failed": 0, "processing_time": 5.120328664779663, "failed_bundles": []}, "Patient": {"bundles_processed": 1, "bundles_succeeded": 1, "bundles_failed": 0, "resources_processed": 13, "resources_succeeded": 13, "resources_failed": 0, "processing_time": 0.03279685974121094, "failed_bundles": []}, "Procedure": {"bundles_processed": 5, "bundles_succeeded": 5, "bundles_failed": 0, "resources_processed": 2056, "resources_succeeded": 2056, "resources_failed": 0, "processing_time": 0.8831973075866699, "failed_bundles": []}}, "resource_usage_samples": [{"timestamp": **********.082803, "elapsed_time": 1.1920928955078125e-06, "bundles_processed": 0, "resources_processed": 0, "cpu_percent": 21.5, "memory_percent": 76.7, "memory_used_gb": 13.32, "net_sent_mb": 0.05, "net_recv_mb": 0.0, "disk_read_mb": 0.05, "disk_write_mb": 0.0}, {"timestamp": **********.9512892, "elapsed_time": 5.868487358093262, "bundles_processed": 8, "resources_processed": 2582, "cpu_percent": 19.0, "memory_percent": 75.7, "memory_used_gb": 13.48, "net_sent_mb": 8.08, "net_recv_mb": 5.71, "disk_read_mb": 14.29, "disk_write_mb": 3.58}, {"timestamp": 1747244675.4182148, "elapsed_time": 11.335412979125977, "bundles_processed": 15, "resources_processed": 5113, "cpu_percent": 24.1, "memory_percent": 74.9, "memory_used_gb": 13.21, "net_sent_mb": 15.83, "net_recv_mb": 12.31, "disk_read_mb": 32.85, "disk_write_mb": 8.95}, {"timestamp": 1747244680.433541, "elapsed_time": 16.350739240646362, "bundles_processed": 22, "resources_processed": 8019, "cpu_percent": 0.0, "memory_percent": 75.3, "memory_used_gb": 13.48, "net_sent_mb": 22.6, "net_recv_mb": 15.93, "disk_read_mb": 45.07, "disk_write_mb": 11.83}, {"timestamp": 1747244685.833479, "elapsed_time": 21.75067710876465, "bundles_processed": 29, "resources_processed": 11519, "cpu_percent": 32.9, "memory_percent": 74.7, "memory_used_gb": 13.29, "net_sent_mb": 29.19, "net_recv_mb": 19.92, "disk_read_mb": 54.95, "disk_write_mb": 13.02}, {"timestamp": 1747244691.261448, "elapsed_time": 27.178646087646484, "bundles_processed": 36, "resources_processed": 14897, "cpu_percent": 19.4, "memory_percent": 75.5, "memory_used_gb": 13.77, "net_sent_mb": 34.44, "net_recv_mb": 23.72, "disk_read_mb": 64.9, "disk_write_mb": 19.32}, {"timestamp": 1747244696.3797069, "elapsed_time": 32.29690504074097, "bundles_processed": 43, "resources_processed": 17910, "cpu_percent": 25.0, "memory_percent": 75.3, "memory_used_gb": 13.73, "net_sent_mb": 38.61, "net_recv_mb": 27.0, "disk_read_mb": 76.38, "disk_write_mb": 19.41}]}