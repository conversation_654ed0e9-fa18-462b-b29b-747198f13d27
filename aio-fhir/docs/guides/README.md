# FHIR to OMOP Transformation Project Documentation

Welcome to the documentation for the FHIR to OMOP Transformation Project. This guide provides comprehensive information on setting up, configuring, and using the various components of the project.

## Technology Stack

This project uses specific versions for compatibility and stability:

- **OMOP CDM**: Version 5.4.2 for all database schemas and references
- **HAPI FHIR**: R4 with `hapiproject/hapi:latest` Docker image
- **PostgreSQL**: Version 14 (recommended for HAPI FHIR compatibility)
- **Python Environment**: Conda environment 'fhir-omop'

## Table of Contents

### FHIR Server Documentation

- **Server Setup and Configuration**
  - [FHIR Server Setup Guide](fhir/server/setup.md) - Complete guide to setting up the HAPI FHIR server

- **API Usage**
  - [Terminology Services](fhir/api/terminology_services.md) - Examples of using FHIR terminology services

### OMOP CDM Documentation

- **Database Setup**
  - [Database Overview](omop/database/overview.md) - Overview of database options for OMOP CDM
  - [PostgreSQL Setup](omop/database/postgresql_setup.md) - Setting up PostgreSQL for OMOP CDM
  - [SQLite Setup](omop/database/sqlite_setup.md) - Setting up SQLite for OMOP CDM (development)

- **Vocabulary Management**
  - [Athena Setup](omop/vocabulary/athena_setup.md) - Configuring and using OHDSI Athena for vocabularies

### Development and General Documentation

- **Development Environment**
  - [Environment Setup](development/environment_setup.md) - Setting up the development environment

- **General Information**
  - [Project Setup](general/setup.md) - Overall project setup guide
  - [Troubleshooting](general/troubleshooting.md) - Common issues and solutions
  - [User Guide](general/user_guide.md) - General user guide for the project

## Documentation Structure

The documentation is organized into the following sections:

```
docs/guides/
├── fhir/                           # FHIR-related documentation
│   ├── server/                     # FHIR server setup and configuration
│   ├── api/                        # FHIR API usage examples
│   └── resources/                  # FHIR resource documentation
├── omop/                           # OMOP CDM documentation
│   ├── database/                   # Database setup and configuration
│   ├── vocabulary/                 # Vocabulary management
│   └── model/                      # OMOP data model documentation
├── etl/                            # ETL process documentation
├── development/                    # Development guides
└── general/                        # General documentation
```

## Contributing to Documentation

When adding new documentation, follow our established guidelines:

### Documentation Standards

1. **Structure**: Title → Brief Description → TOC → Main Content → References
2. **Format**: Use Markdown with ATX-style headers (one space after #)
3. **Language**: Write all documentation in English
4. **Citations**: Use inline hyperlinked citations rather than numbered references
5. **Versions**: Include version information when referencing software or standards

### File Organization

1. Place the file in the appropriate directory based on the structure above
2. Use descriptive, lowercase names with underscores for spaces
3. Include links to related documentation
4. Update this index file to include the new document
5. Place new documentation within existing directories to avoid over-complication

## Future Documentation

As the project progresses, additional documentation will be added for:

- ETL processes for transforming FHIR to OMOP
- OMOP data model details
- Additional FHIR resources and their mappings
- Validation and quality assurance processes
