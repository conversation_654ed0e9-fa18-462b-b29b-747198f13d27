# RxNorm to OMOP Standard Concepts Mapping

## Overview

RxNorm is a standardized nomenclature for clinical drugs produced by the U.S. National Library of Medicine (NLM). In the OMOP Common Data Model, RxNorm is the primary standard vocabulary for the Drug domain. This document outlines the strategies and approaches for mapping RxNorm codes from FHIR resources to OMOP standard concepts, including handling international drugs through RxNorm Extension.

## RxNorm in OMOP CDM

In the OMOP Common Data Model:
- RxNorm is the preferred standard vocabulary for drugs
- RxNorm concepts are stored in the CONCEPT table with vocabulary_id = 'RxNorm'
- Most standard concepts in the Drug domain are sourced from RxNorm
- RxNorm Extension is used for drugs not available in the U.S. market

## RxNorm Concept Structure

RxNorm organizes drug concepts in a hierarchical structure with different term types:

1. **Ingredient (IN)**: The active ingredient of a drug (e.g., "Acetaminophen")
2. **Precise Ingredient (PIN)**: A specific form of an ingredient (e.g., "Acetaminophen, oral")
3. **Brand Name (BN)**: The proprietary name of a drug product (e.g., "Tylenol")
4. **Semantic Clinical Drug (SCD)**: Ingredient + Strength + Dose Form (e.g., "Acetaminophen 500 MG Oral Tablet")
5. **Semantic Branded Drug (SBD)**: Brand Name + Ingredient + Strength + Dose Form (e.g., "Tylenol 500 MG Oral Tablet")
6. **Clinical Drug Component (SCDC)**: Ingredient + Strength (e.g., "Acetaminophen 500 MG")
7. **Clinical Drug Form (SCDF)**: Ingredient + Dose Form (e.g., "Acetaminophen Oral Tablet")
8. **Multiple Ingredients (MIN)**: Combination of multiple ingredients (e.g., "Acetaminophen / Codeine")

In OMOP, the preferred level for standard concepts is typically the SCD or SBD level, which provides the most complete clinical information.

## Mapping Process

The process of mapping RxNorm codes from FHIR resources to OMOP standard concepts involves several steps:

1. **Extract RxNorm Code from FHIR Resource**: 
   - In FHIR, RxNorm codes are typically found in Medication resources in the `code.coding` element where `system = "http://www.nlm.nih.gov/research/umls/rxnorm"`.

2. **Look Up RxNorm Code in OMOP Vocabulary**:
   - Use the CONCEPT table to find the corresponding concept_id for the RxNorm code.
   - Query the CONCEPT table where concept_code = [RxNorm code] and vocabulary_id = 'RxNorm'.

3. **Determine Standard Concept Status**:
   - Check if the found concept is a standard concept (standard_concept = 'S').
   - If it is not a standard concept, use the CONCEPT_RELATIONSHIP table to find the standard concept it maps to.

4. **Handle Drug Components**:
   - For drug mapping, consider the multiple components: ingredient, dose form, and strength.
   - Ensure all components are properly mapped to maintain clinical accuracy.

5. **Populate OMOP Fields**:
   - Set drug_concept_id to the standard concept_id.
   - Set drug_source_value to the original RxNorm code.
   - Set drug_source_concept_id to the source concept_id (if different from the standard concept_id).

## SQL Code Examples

### Example 1: Finding OMOP Concept ID for a RxNorm Code

```sql
-- Find the OMOP concept_id for RxNorm code '1049640' (Acetaminophen 325 MG Oral Tablet)
SELECT 
    concept_id,
    concept_name,
    domain_id,
    vocabulary_id,
    concept_code,
    standard_concept
FROM 
    concept
WHERE 
    concept_code = '1049640'
    AND vocabulary_id = 'RxNorm';
```

### Example 2: Finding Standard Concept for Non-Standard RxNorm Concept

```sql
-- If the RxNorm concept is not standard, find its standard mapping
SELECT 
    c1.concept_id AS source_concept_id,
    c1.concept_name AS source_concept_name,
    c2.concept_id AS standard_concept_id,
    c2.concept_name AS standard_concept_name,
    c2.domain_id,
    c2.vocabulary_id
FROM 
    concept c1
JOIN 
    concept_relationship cr ON c1.concept_id = cr.concept_id_1
JOIN 
    concept c2 ON cr.concept_id_2 = c2.concept_id
WHERE 
    c1.concept_code = '1049640'
    AND c1.vocabulary_id = 'RxNorm'
    AND cr.relationship_id = 'Maps to'
    AND c2.standard_concept = 'S';
```

### Example 3: Complete FHIR Medication to OMOP DRUG_EXPOSURE Mapping

```sql
-- Pseudocode for mapping FHIR Medication with RxNorm code to OMOP DRUG_EXPOSURE
INSERT INTO drug_exposure (
    person_id,
    drug_concept_id,
    drug_exposure_start_date,
    drug_exposure_start_datetime,
    drug_type_concept_id,
    quantity,
    days_supply,
    drug_source_value,
    drug_source_concept_id
)
SELECT
    person_id, -- Mapped from FHIR MedicationRequest.subject reference
    standard_concept_id, -- From the mapping query above
    exposure_date, -- From FHIR MedicationRequest.authoredOn
    exposure_datetime, -- From FHIR MedicationRequest.authoredOn
    type_concept_id, -- Based on provenance of the medication
    quantity, -- From FHIR MedicationRequest.dispenseRequest.quantity
    days_supply, -- Calculated from FHIR MedicationRequest.dispenseRequest
    rxnorm_code, -- Original RxNorm code from FHIR Medication.code.coding.code
    source_concept_id -- From the initial concept lookup
FROM
    fhir_medication_staging;
```

## RxNorm Extension

RxNorm Extension is an OHDSI-developed vocabulary that extends RxNorm to include drugs that are not available in the U.S. market. This is necessary because RxNorm primarily focuses on drugs approved for use in the United States.

### Key Features of RxNorm Extension:

1. **International Drug Coverage**: Includes drugs used outside the United States
2. **Historical Drug Information**: Contains data on discontinued drugs
3. **Standardized Structure**: Follows the same hierarchical structure as RxNorm
4. **OMOP Integration**: Fully integrated with the OMOP CDM and vocabulary system

### Mapping to RxNorm Extension:

When a drug cannot be found in the standard RxNorm vocabulary, the following approach can be used to map to RxNorm Extension:

1. **Extract Drug Components**:
   - Identify the ingredient, dose form, and strength from the source data
   - For international drugs, also identify the country-specific code or name

2. **Search in RxNorm Extension**:
   - Query the CONCEPT table with vocabulary_id = 'RxNorm Extension'
   - Use the extracted components to find the most appropriate match

3. **Create Mappings**:
   - If a direct match is found, use it as the standard concept
   - If no direct match is found, map to the closest concept (typically at the ingredient level)

### SQL Example for RxNorm Extension Mapping:

```sql
-- Find a drug in RxNorm Extension by components
SELECT 
    concept_id,
    concept_name,
    concept_code,
    standard_concept
FROM 
    concept
WHERE 
    vocabulary_id = 'RxNorm Extension'
    AND concept_class_id = 'Ingredient'
    AND concept_name LIKE '%Paracetamol%' -- International name for Acetaminophen
    AND standard_concept = 'S';
```

## Python Code Example for FHIR to OMOP RxNorm Mapping

```python
import pandas as pd
import sqlalchemy as sa

# Connect to the OMOP database
engine = sa.create_engine('postgresql://username:password@localhost:5432/omop_cdm')

def map_rxnorm_to_omop(rxnorm_code):
    """
    Map a RxNorm code to its corresponding OMOP standard concept
    
    Args:
        rxnorm_code (str): The RxNorm code to map
        
    Returns:
        dict: A dictionary containing the mapping information
    """
    # Query to find the concept and its standard mapping if needed
    query = """
    WITH source_concept AS (
        SELECT 
            concept_id,
            concept_name,
            domain_id,
            standard_concept,
            concept_class_id
        FROM 
            concept
        WHERE 
            concept_code = :rxnorm_code
            AND vocabulary_id = 'RxNorm'
    )
    SELECT 
        sc.concept_id AS source_concept_id,
        sc.concept_name AS source_concept_name,
        sc.concept_class_id AS source_concept_class_id,
        CASE 
            WHEN sc.standard_concept = 'S' THEN sc.concept_id
            ELSE c2.concept_id 
        END AS standard_concept_id,
        CASE 
            WHEN sc.standard_concept = 'S' THEN sc.concept_name
            ELSE c2.concept_name 
        END AS standard_concept_name,
        CASE 
            WHEN sc.standard_concept = 'S' THEN sc.domain_id
            ELSE c2.domain_id 
        END AS domain_id
    FROM 
        source_concept sc
    LEFT JOIN 
        concept_relationship cr ON sc.concept_id = cr.concept_id_1 AND cr.relationship_id = 'Maps to'
    LEFT JOIN 
        concept c2 ON cr.concept_id_2 = c2.concept_id AND c2.standard_concept = 'S'
    """
    
    with engine.connect() as conn:
        result = conn.execute(sa.text(query), {"rxnorm_code": rxnorm_code}).fetchone()
    
    if result is None:
        # Try RxNorm Extension if not found in RxNorm
        ext_query = """
        SELECT 
            concept_id AS standard_concept_id,
            concept_name AS standard_concept_name,
            domain_id,
            concept_class_id
        FROM 
            concept
        WHERE 
            concept_code = :rxnorm_code
            AND vocabulary_id = 'RxNorm Extension'
            AND standard_concept = 'S'
        """
        with engine.connect() as conn:
            ext_result = conn.execute(sa.text(ext_query), {"rxnorm_code": rxnorm_code}).fetchone()
        
        if ext_result is None:
            return {"error": f"RxNorm code {rxnorm_code} not found in OMOP vocabulary"}
        
        return {
            "source_concept_id": None,
            "source_concept_name": None,
            "standard_concept_id": ext_result.standard_concept_id,
            "standard_concept_name": ext_result.standard_concept_name,
            "domain_id": ext_result.domain_id,
            "concept_class_id": ext_result.concept_class_id,
            "vocabulary": "RxNorm Extension"
        }
    
    return {
        "source_concept_id": result.source_concept_id,
        "source_concept_name": result.source_concept_name,
        "standard_concept_id": result.standard_concept_id,
        "standard_concept_name": result.standard_concept_name,
        "domain_id": result.domain_id,
        "concept_class_id": result.source_concept_class_id,
        "vocabulary": "RxNorm"
    }

def process_fhir_medication(fhir_medication_json):
    """
    Process a FHIR Medication resource and map its RxNorm code to OMOP
    
    Args:
        fhir_medication_json (dict): The FHIR Medication resource as a JSON object
        
    Returns:
        dict: The data ready to be inserted into the DRUG_EXPOSURE table
    """
    # Extract the RxNorm code from the FHIR Medication
    rxnorm_code = None
    for coding in fhir_medication_json.get("code", {}).get("coding", []):
        if coding.get("system") == "http://www.nlm.nih.gov/research/umls/rxnorm":
            rxnorm_code = coding.get("code")
            break
    
    if not rxnorm_code:
        return {"error": "No RxNorm code found in the FHIR Medication"}
    
    # Map the RxNorm code to OMOP
    mapping = map_rxnorm_to_omop(rxnorm_code)
    if "error" in mapping:
        return mapping
    
    # Extract other relevant data from the FHIR Medication
    # (This is simplified and would need to be expanded for a real implementation)
    person_id = extract_person_id(fhir_medication_json.get("subject", {}).get("reference"))
    exposure_datetime = fhir_medication_json.get("authoredOn")
    
    # Extract quantity and days supply
    quantity = None
    days_supply = None
    if "dispenseRequest" in fhir_medication_json:
        if "quantity" in fhir_medication_json["dispenseRequest"]:
            quantity = fhir_medication_json["dispenseRequest"]["quantity"].get("value")
        if "expectedSupplyDuration" in fhir_medication_json["dispenseRequest"]:
            days_supply = fhir_medication_json["dispenseRequest"]["expectedSupplyDuration"].get("value")
    
    # Prepare the data for insertion into the DRUG_EXPOSURE table
    omop_data = {
        "person_id": person_id,
        "drug_concept_id": mapping["standard_concept_id"],
        "drug_exposure_start_date": exposure_datetime.split("T")[0] if exposure_datetime else None,
        "drug_exposure_start_datetime": exposure_datetime,
        "drug_type_concept_id": 32839,  # Prescription dispensed in pharmacy
        "quantity": quantity,
        "days_supply": days_supply,
        "drug_source_value": rxnorm_code,
        "drug_source_concept_id": mapping["source_concept_id"]
    }
    
    return omop_data

# Helper function (would need to be implemented)
def extract_person_id(subject_reference):
    # Extract person_id from FHIR reference (e.g., "Patient/123")
    if subject_reference and subject_reference.startswith("Patient/"):
        return subject_reference.split("/")[1]
    return None
```

## Mapping Challenges and Solutions

### 1. Multiple Ingredients (MIN)

RxNorm's Multiple Ingredients (MIN) term type represents combination drugs. In OMOP, these can be challenging to map because:

- Some MIN concepts may not be standard concepts in OMOP
- The RxNorm Extension may not include all international combination drugs

**Solution**: 
- For MIN concepts not found in RxNorm, check if they map to ATC codes that are in the standard vocabulary
- If no direct mapping exists, map to the individual ingredients and record the relationship

### 2. International Drugs

Drugs used outside the United States may not be present in RxNorm.

**Solution**:
- Use RxNorm Extension, which is specifically designed to handle international drugs
- Map based on the drug components (ingredient, dose form, strength)
- If no exact match is found, map to the closest concept at the ingredient level

### 3. Drug Components Mapping

Drug mapping requires matching multiple components (ingredient, dose form, strength).

**Solution**:
- Extract each component from the source data
- Map each component separately
- Combine the mappings to find the most appropriate standard concept
- Use a hierarchical approach: try to map at the most specific level (SCD/SBD) first, then fall back to less specific levels (SCDF, IN) if needed

## Automatic Mapping Approach

For large-scale mapping of drug concepts to RxNorm, an automated approach can be used:

1. **Extract Key Attributes**:
   - Parse the drug name to identify ingredient, dose form, and strength
   - Use natural language processing techniques for unstructured drug names
   - Leverage existing codes (e.g., ATC) if available

2. **Build Mappings with RxNorm**:
   - Compare attribute combinations against existing RxNorm concepts
   - Use fuzzy matching for ingredient names to handle spelling variations
   - Consider synonyms and alternative names

3. **Integration with RxNorm Extension**:
   - If no match is found in RxNorm, search in RxNorm Extension
   - Generate new concepts in RxNorm Extension if needed (through the OHDSI vocabulary update process)

4. **Validation and Quality Control**:
   - Implement validation rules to ensure clinical accuracy
   - Review mappings, especially for high-impact or frequently used drugs
   - Track mapping coverage and success rates

## Best Practices for RxNorm to OMOP Mapping

1. **Use Athena for Vocabulary Lookup**:
   - Athena (https://athena.ohdsi.org) is the official OHDSI tool for browsing and downloading the standardized vocabularies
   - Use it to look up RxNorm codes and understand their mappings

2. **Preserve Source Values**:
   - Always preserve the original RxNorm code in the drug_source_value field for traceability

3. **Handle Drug Hierarchies Appropriately**:
   - Map at the most specific level possible (SCD/SBD)
   - If specific mapping is not possible, fall back to more general concepts (IN)
   - Document the level of specificity in your mapping

4. **Consider Drug Exposure Context**:
   - The drug_type_concept_id field in DRUG_EXPOSURE indicates the provenance of the drug information
   - Set this appropriately based on the FHIR resource type (MedicationRequest, MedicationDispense, MedicationAdministration)

5. **Stay Updated with Vocabulary Releases**:
   - OMOP vocabularies are updated regularly. Ensure your system uses the latest version to have the most comprehensive RxNorm coverage
   - RxNorm Extension is particularly active with frequent updates for international drugs

## References

1. OHDSI. "The Book of OHDSI: Chapter 5 Standardized Vocabularies." https://ohdsi.github.io/TheBookOfOhdsi/StandardizedVocabularies.html
2. The Hyve. "OMOP data curation: Automatic Mapping of Drug Concepts to RxNorm." https://www.thehyve.nl/articles/omop-mapping-drug-concepts-to-rxnorm
3. Sciforce. "RxNorm Extension: tool to standardize source drug data using OMOP CDM." https://medium.com/sciforce/rxnorm-extension-tool-to-standardize-source-drug-data-using-omop-cdm-71fd87eddaa2
4. OHDSI Forums. "Mapping OMOP concept id from RxNorm Extension." https://forums.ohdsi.org/t/mapping-omop-concept-id-from-rxnorm-extension/7105
