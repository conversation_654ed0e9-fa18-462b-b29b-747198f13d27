# Configuration template for FHIR to OMOP ETL Pipeline
# Copy this file to .env and configure with your specific values

# ===========================================
# GENERAL CONFIGURATION
# ===========================================

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=./etl.log

# Processing Configuration
BATCH_SIZE=100
MAX_WORKERS=4

# ===========================================
# PATHS AND DIRECTORIES
# ===========================================

# Vocabulary and Data Paths
VOCABULARY_PATH=./data/vocabulary/omop_v5_YYYYMMDD

# ===========================================
# EXTERNAL APIS
# ===========================================

# UMLS API Configuration for CPT4 reconstitution
UMLS_API_KEY=your-umls-api-key-here

# ===========================================
# FHIR SERVER CONFIGURATION
# ===========================================

# FHIR Server Configuration
FHIR_SERVER_BASE_URL=http://localhost:8080/fhir
FHIR_SERVER_USERNAME=
FHIR_SERVER_PASSWORD=

# ===========================================
# OMOP DATABASE CONFIGURATION
# ===========================================

# Production OMOP CDM v5.4 database with dedicated user
OMOP_DB_HOST=localhost
OMOP_DB_PORT=5432
OMOP_DB_NAME=omop_cdm
OMOP_DB_SCHEMA=public
OMOP_DB_USERNAME=omop
OMOP_DB_PASSWORD=your-secure-password-here

# ===========================================
# POSTGRESQL ADMIN CONFIGURATION
# ===========================================

# PostgreSQL Admin Configuration (for database creation)
POSTGRES_ADMIN_USER=your-admin-user
POSTGRES_ADMIN_DB=postgres
