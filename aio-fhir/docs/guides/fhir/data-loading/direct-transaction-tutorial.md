# Direct Transaction Bundle Loading Tutorial

A concise guide for loading FHIR data using transaction bundles with permissive server configuration.

## Prerequisites

- HAPI FHIR Server running
- FHIR data in NDJSON format
- Python 3.6+
- Required Python packages: `requests`, `json`

## Overview

The direct transaction bundle method:
1. Configures the server to accept resources with unresolved references
2. Converts NDJSON files to transaction bundles
3. Sends bundles directly to the server
4. Works with complex reference relationships

## Step 1: Configure Server for Import Mode

This step is **critical** for successful bulk loading.

1. Navigate to the fhir-server directory:
   ```bash
   # Navigate to the fhir-server directory
   cd servers/fhir-server
   ```

2. Edit the server configuration file:
   ```bash
   nano docker-compose-postgres.yml
   ```

3. Uncomment these lines in the configuration:
   ```yaml
   - hapi.fhir.validation.enabled=false                        # Disables validation
   - hapi.fhir.validation.request_validator.mode=NONE          # Disables request validation
   - hapi.fhir.auto_create_placeholder_reference_targets=true  # Creates placeholder resources for missing references
   - hapi.fhir.enforce_referential_integrity_on_write=false    # Disables referential integrity checks on write
   - hapi.fhir.enforce_referential_integrity_on_delete=false   # Disables referential integrity checks on delete
   ```

4. Restart the server:
   ```bash
   ./manage-fhir-server.sh restart
   ```

5. Verify the server is running:
   ```bash
   curl -s "http://localhost:8080/fhir/metadata" | grep -A 5 "resourceType"
   ```

## Step 2: Convert NDJSON to Transaction Bundles

First, make sure you're in the fhir-server directory:

```bash
# Navigate to the fhir-server directory
cd servers/fhir-server
```

You can convert NDJSON files to transaction bundles using one of two approaches:

### Option 1: Process the entire directory at once

```bash
# Convert all NDJSON files in the directory
python scripts/data_loading/ndjson_to_bundle.py \
  --input-dir ../../data/sample_fhir/bulk-export \
  --output-dir ../../data/generated_bundles/bulk-export-bundles
```

This will:
- Process all NDJSON files in the directory
- Automatically create the output directory and subdirectories for each resource type
- Generate transaction bundles with appropriate batch sizes
- Skip log files automatically

### Option 2: Process individual files

If you prefer to process files individually:

```bash
# Convert specific resources
python scripts/data_loading/ndjson_to_bundle.py \
  --input-file ../../data/sample_fhir/bulk-export/Patient.ndjson \
  --output-file ../../data/generated_bundles/bulk-export-bundles/Patient/Patient_bundle.json

python scripts/data_loading/ndjson_to_bundle.py \
  --input-file ../../data/sample_fhir/bulk-export/Encounter.ndjson \
  --output-file ../../data/generated_bundles/bulk-export-bundles/Encounter/Encounter_bundle.json
```

Note: The script will automatically create any necessary directories in the output path.

For large files, the script will automatically create multiple bundles with numeric suffixes (e.g., `Patient_bundle_1.json`, `Patient_bundle_2.json`).

## Step 3: Load Bundles to FHIR Server

Continuing from the fhir-server directory, use the dedicated Python script for loading all bundles:

```bash
# Load all bundles from the generated directory
python scripts/data_loading/load_all_bundles.py --bundle-dir ../../data/generated_bundles/bulk-export-bundles
```

The script will:
1. Find all JSON files in the specified directory (including subdirectories)
2. Load each file as a FHIR transaction bundle
3. Send each bundle to the FHIR server
4. Report on the success or failure of each operation
5. Track performance metrics during the loading process

### Command-line Options

The script provides several options for customization:

```bash
# Get help and see all available options
python scripts/data_loading/load_all_bundles.py --help

# Specify a custom server URL
python scripts/data_loading/load_all_bundles.py \
  --bundle-dir ../../data/generated_bundles/bulk-export-bundles \
  --server-url http://custom-server:8080/fhir

# Specify a custom file pattern
python scripts/data_loading/load_all_bundles.py \
  --bundle-dir ../../data/generated_bundles/bulk-export-bundles \
  --pattern "Patient*.json"

# Export performance metrics
python scripts/data_loading/load_all_bundles.py \
  --bundle-dir ../../data/generated_bundles/bulk-export-bundles \
  --export-performance
```

### Environment Variables

You can also use environment variables for configuration:

```bash
# Set environment variables and run the script
FHIR_SERVER_URL="http://localhost:8080/fhir" \
BUNDLE_DIR="../../data/generated_bundles/bulk-export-bundles" \
python scripts/data_loading/load_all_bundles.py
```

Note: This approach works well because we've configured the server with `auto_create_placeholder_reference_targets=true` in Step 1, which allows resources to be loaded regardless of their dependency order.

## Step 4: Verify Loaded Resources

Still in the fhir-server directory, check that resources were loaded correctly:

```bash
# Check Patient resources
curl -s "http://localhost:8080/fhir/Patient?_summary=count" | python -m json.tool

# Check Encounter resources
curl -s "http://localhost:8080/fhir/Encounter?_summary=count" | python -m json.tool

# Check Condition resources
curl -s "http://localhost:8080/fhir/Condition?_summary=count" | python -m json.tool

# Check Observation resources
curl -s "http://localhost:8080/fhir/Observation?_summary=count" | python -m json.tool

# Check AllergyIntolerance resources
curl -s "http://localhost:8080/fhir/AllergyIntolerance?_summary=count" | python -m json.tool

# Check Device resources
curl -s "http://localhost:8080/fhir/Device?_summary=count" | python -m json.tool
```

## Step 5: Restore Default Server Configuration

After loading is complete, restore the default server configuration:

1. Edit the server configuration file:
   ```bash
   nano docker-compose-postgres.yml
   ```

2. Comment out these lines:
   ```yaml
   # - hapi.fhir.validation.enabled=false
   # - hapi.fhir.validation.request_validator.mode=NONE
   # - hapi.fhir.auto_create_placeholder_reference_targets=true
   # - hapi.fhir.enforce_referential_integrity_on_write=false
   # - hapi.fhir.enforce_referential_integrity_on_delete=false
   ```

3. Restart the server:
   ```bash
   ./manage-fhir-server.sh restart
   ```

## Troubleshooting

| Issue | Solution |
|-------|----------|
| Bundle processing errors | Check server logs with `./manage-fhir-server.sh logs` |
| Missing bundle files | Check generated files with `find ../../data/generated_bundles/bulk-export-bundles -name "*.json"` |
| Multiple bundle files | Use the `load_all_bundles.py` script which handles multiple files automatically |
| Script not finding files | Use the `--bundle-dir` option with the full path to your bundle directory |
| Server not responding | Verify server is running with `./manage-fhir-server.sh status` |
| Performance issues | Use the `--export-performance` flag to analyze loading performance |
| 409 Conflict errors | Resources with the same ID already exist; clean the server before loading |
| Reference integrity errors | Configure server for import mode as described in Step 1 |

## References

- [FHIR Transaction Bundle Documentation](https://www.hl7.org/fhir/bundle.html#transaction)
- [HAPI FHIR Server Configuration](https://hapifhir.io/hapi-fhir/docs/server_jpa/configuration.html)
- [Performance Metrics Guide](performance-metrics.md)
