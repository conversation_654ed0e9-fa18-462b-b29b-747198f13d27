# Development Documentation

This section contains documentation related to the development environment, processes, and standards for the FHIR to OMOP transformation project.

## Documentation Structure

### Core Standards
- [Coding and Documentation Standards](standards.md) - Core coding style, documentation format, and general development practices
- [Unit Testing Standards](unit_testing_standards.md) - Comprehensive testing strategy, architecture, and implementation guidelines

### Environment and Setup
- [Environment Setup](environment_setup.md) - Setting up the development environment

### Specialized Guides
- [Unit Testing Strategy Brief](unit_testing_strategy_brief.md) - Strategic overview for testing implementation

## Quick Reference

### Technology Stack
- **OMOP CDM**: Version 5.4.2
- **HAPI FHIR**: R4 with Docker
- **PostgreSQL**: Version 14
- **Python**: Version 3.11 with conda environment 'fhir-omop'
- **Testing**: pytest with coverage reporting

### Key Principles
- **Pedagogical Approach**: Step-by-step explanations for technical implementations
- **Official Documentation**: Reference to OMOP CDM v5.4.2 and HAPI FHIR R4 documentation
- **Flexibility**: Standards that adapt to project evolution
- **Quality**: Comprehensive testing and validation

## Directory Structure

```
development/
├── README.md                           # This file - overview and navigation
├── standards.md                        # Core coding and documentation standards
├── unit_testing_standards.md           # Comprehensive testing guidelines
├── environment_setup.md                # Development environment setup
└── unit_testing_strategy_brief.md      # Testing strategy overview
```

## Related Documentation

- [OMOP Implementation Guide](../omop/) - OMOP-specific implementation details
- [FHIR Server Guide](../fhir/) - FHIR server setup and configuration
- [Architecture Documentation](../../architecture/) - System architecture and design
