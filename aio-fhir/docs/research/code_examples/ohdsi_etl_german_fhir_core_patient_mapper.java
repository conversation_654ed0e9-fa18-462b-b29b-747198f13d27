// Extract from PatientMapper.java in OHDSI/ETL-German-FHIR-Core repository
// Source: https://github.com/OHDSI/ETL-German-FHIR-Core/blob/main/src/main/java/org/miracum/etl/fhirtoomop/mapper/PatientMapper.java

package org.miracum.etl.fhirtoomop.mapper;

import static org.miracum.etl.fhirtoomop.Constants.CONCEPT_EHR_RECORD_STATUS_DECEASED;
import static org.miracum.etl.fhirtoomop.Constants.CONCEPT_GENDER_UNKNOWN;
import static org.miracum.etl.fhirtoomop.Constants.CONCEPT_HISPANIC_OR_LATINO;
import static org.miracum.etl.fhirtoomop.Constants.CONCEPT_NO_MATCHING_CONCEPT;
import static org.miracum.etl.fhirtoomop.Constants.CONCEPT_UNKNOWN_RACIAL_GROUP;
import static org.miracum.etl.fhirtoomop.Constants.ETHNICITY_SOURCE_HISPANIC_OR_LATINO;
import static org.miracum.etl.fhirtoomop.Constants.ETHNICITY_SOURCE_MIXED;
import static org.miracum.etl.fhirtoomop.Constants.MAX_LOCATION_CITY_LENGTH;
import static org.miracum.etl.fhirtoomop.Constants.MAX_LOCATION_COUNTRY_LENGTH;
import static org.miracum.etl.fhirtoomop.Constants.MAX_LOCATION_STATE_LENGTH;
import static org.miracum.etl.fhirtoomop.Constants.MAX_LOCATION_ZIP_LENGTH;
import static org.miracum.etl.fhirtoomop.Constants.MAX_SOURCE_VALUE_LENGTH;
import static org.miracum.etl.fhirtoomop.Constants.SOURCE_VOCABULARY_ID_GENDER;

import ca.uhn.fhir.fhirpath.IFhirPath;
import com.google.common.base.Strings;
import io.micrometer.core.instrument.Counter;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hl7.fhir.r4.model.Address;
import org.hl7.fhir.r4.model.Age;
import org.hl7.fhir.r4.model.Coding;
import org.hl7.fhir.r4.model.DateTimeType;
import org.hl7.fhir.r4.model.DateType;
import org.hl7.fhir.r4.model.Enumerations.ResourceType;
import org.hl7.fhir.r4.model.Extension;
import org.hl7.fhir.r4.model.Patient;
import org.miracum.etl.fhirtoomop.DbMappings;
import org.miracum.etl.fhirtoomop.config.FhirSystems;
import org.miracum.etl.fhirtoomop.mapper.helpers.FindOmopConcepts;
import org.miracum.etl.fhirtoomop.mapper.helpers.MapperMetrics;
import org.miracum.etl.fhirtoomop.mapper.helpers.ResourceCheckDataAbsentReason;
import org.miracum.etl.fhirtoomop.mapper.helpers.ResourceFhirReferenceUtils;
import org.miracum.etl.fhirtoomop.mapper.helpers.ResourceOmopReferenceUtils;
import org.miracum.etl.fhirtoomop.model.OmopModelWrapper;
import org.miracum.etl.fhirtoomop.model.PostProcessMap;
import org.miracum.etl.fhirtoomop.model.omop.Person;
import org.miracum.etl.fhirtoomop.repository.service.PatientMapperServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * The PatientMapper class describes the business logic of transforming a FHIR Patient resource to
 * OMOP CDM.
 *
 * <AUTHOR> Henke
 * <AUTHOR> Peng
 */
@Slf4j
@Component
public class PatientMapper implements FhirMapper<Patient> {

  private static final FhirSystems fhirSystems = new FhirSystems();
  private final IFhirPath fhirPath;
  private final Boolean bulkload;
  private final DbMappings dbMappings;

  @Autowired ResourceOmopReferenceUtils omopReferenceUtils;

  @Autowired ResourceFhirReferenceUtils fhirReferenceUtils;

  @Autowired PatientMapperServiceImpl patientService;

  @Autowired ResourceCheckDataAbsentReason checkDataAbsentReason;

  @Autowired FindOmopConcepts findOmopConcepts;

  private static final Counter noFhirReferenceCounter =
      MapperMetrics.setNoFhirReferenceCounter("stepProcessPatients");
  private static final Counter deletedFhirReferenceCounter =
      MapperMetrics.setDeletedFhirRessourceCounter("stepProcessPatients");

  /**
   * Constructor for objects of the class PatientMapper.
   *
   * @param fhirPath FhirPath engine to evaluate path expressions over FHIR resources
   * @param bulkload parameter which indicates whether the Job should be run as bulk load or
   *     incremental load
   * @param dbMappings collections for the intermediate storage of data from OMOP CDM in RAM
   */
  @Autowired
  public PatientMapper(IFhirPath fhirPath, Boolean bulkload, DbMappings dbMappings) {
    this.fhirPath = fhirPath;
    this.bulkload = bulkload;
    this.dbMappings = dbMappings;
  }

  /**
   * Maps a FHIR Patient resource to several OMOP CDM tables.
   *
   * @param srcPatient FHIR Patient resource
   * @param isDeleted a flag, whether the FHIR resource is deleted in the source
   * @return OmopModelWrapper cache of newly created OMOP CDM records from the FHIR Patient resource
   */
  @Override
  public OmopModelWrapper map(Patient srcPatient, boolean isDeleted) {
    var wrapper = new OmopModelWrapper();

    var patientSourceIdentifier = fhirReferenceUtils.extractIdentifier(srcPatient, "MR");
    var patientLogicId = fhirReferenceUtils.extractId(srcPatient);

    if (Strings.isNullOrEmpty(patientLogicId) && Strings.isNullOrEmpty(patientSourceIdentifier)) {
      log.warn("No [Identifier] or [Id] found. [Patient] resource is invalid. Skip resource");
      noFhirReferenceCounter.increment();
      return null;
    }

    String patientId = "";
    if (!Strings.isNullOrEmpty(patientLogicId)) {
      patientId = srcPatient.getId();
    }

    var ageExtensionMap = extractAgeExtension(srcPatient);
    var ageAtDiagnosis = setAgeAtDiagnosis(patientLogicId, patientSourceIdentifier);
    var realBirthDate = extractBirthDate(srcPatient);
    var calculatedBirthDate = extractCalculatedBirthDate(ageExtensionMap, ageAtDiagnosis, patientId);

    if (realBirthDate == null && calculatedBirthDate == null) {
      log.info("No [Birthdate] found for [Patient]: {}. Skip Resource.", patientId);
      if (bulkload.equals(Boolean.FALSE)) {
        deleteExistingPatients(patientLogicId, patientSourceIdentifier);
      }
      return null;
    }

    if (bulkload.equals(Boolean.FALSE) && isDeleted) {
      log.info("Found a deleted [Patient] resource {}. Deleting from OMOP DB.", patientId);
      deleteExistingPatients(patientLogicId, patientSourceIdentifier);
      deletedFhirReferenceCounter.increment();
      return null;
    }

    if (bulkload.equals(Boolean.FALSE)) {
      deleteExistingDeath(patientLogicId, patientSourceIdentifier);
      deleteExistingCalculatedBirthYear(patientLogicId, patientSourceIdentifier);
    }

    var newPerson = createNewPerson(srcPatient, patientLogicId, patientSourceIdentifier, patientId);
    setBirthDate(realBirthDate, calculatedBirthDate, newPerson);

    var ethnicGroupCoding = extractEthnicGroup(srcPatient);
    setRaceConcept(ethnicGroupCoding, newPerson, patientLogicId);
    setEthnicityConcept(ethnicGroupCoding, newPerson);

    var death = setDeath(srcPatient, patientLogicId, patientSourceIdentifier);
    if (death != null) {
      wrapper.getPostProcessMap().add(death);
    }

    var location = setLocation(srcPatient, patientLogicId, patientSourceIdentifier);
    if (location != null) {
      wrapper.getPostProcessMap().add(location);
    }

    wrapper.setPerson(newPerson);

    if (ageAtDiagnosis.getDataOne() != null) {
      wrapper.getPostProcessMap().add(ageAtDiagnosis);
    }

    return wrapper;
  }

  // Additional methods omitted for brevity
  // The PatientMapper handles mapping FHIR Patient resources to OMOP CDM Person table
  // It extracts demographic information, handles birth dates, ethnicity, race, death information, and location
  // It supports both incremental and bulk loading modes
}
