# Shell and PATH: Pedagogical Troubleshooting Guide

## 📚 Purpose of This Document

This document originates from a real issue encountered during PostgreSQL setup in the FHIR-OMOP project. It serves as:

1. **Documentation of the specific problem** we experienced
2. **Pedagogical guide** to understand fundamental shell and PATH concepts
3. **Future reference** to prevent and resolve similar issues
4. **Learning task** to deepen understanding of these critical concepts

## 🚨 Documented Specific Problem

### Error Context
- **Project**: FHIR-OMOP, PostgreSQL database setup
- **Date**: July 1, 2025
- **Symptom**: `psql: command not found` despite PostgreSQL being installed
- **Environment**: macOS with Apple Silicon, VS Code, Homebrew

### Error Manifestation
```bash
# Command executed
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT COUNT(*) FROM concept;"

# Error received
bash: psql: command not found

# But the command existed
ls -la /opt/homebrew/bin/psql
# -rwxr-xr-x  1 <USER>  <GROUP>  [...] /opt/homebrew/bin/psql
```

### Diagnosis Performed
```bash
# 1. Check current shell
echo $SHELL                    # Output: /bin/zsh
ps -p $$ -o comm=             # Output: /opt/homebrew/bin/bash

# 2. Check PATH
echo $PATH                     # Did not include /opt/homebrew/bin

# 3. Check installations
find /opt -name psql 2>/dev/null
# /opt/homebrew/bin/psql (existed)
```

### Root Cause Identified
1. **Shell Mismatch**: VS Code running bash while the system uses zsh
2. **Incomplete PATH**: `/opt/homebrew/bin` was missing from PATH
3. **Missing Configuration**: `.zshrc` lacked Homebrew setup

## 🎯 Instructions for Pedagogical Agent

> **For the agent using this document**: Apply the academic pedagogical methodology that characterizes our work. This means:
>
> - **Step-by-step explanations** with theoretical foundations
> - **Why/what/how approach** for each concept
> - **References to official documentation** where relevant
> - **Practical examples** based on this real case
> - **Reflection questions** to consolidate learning
> - **Incremental progression** from basic to advanced concepts
> - **Connections to the FHIR-OMOP project context**

## 📖 Required Learning Topics

### 1. Shell Fundamentals in Unix/Linux/macOS
**Learning objectives:**
- Understand what a shell is and its role in the OS
- Differences between bash, zsh, fish, and other shells
- How the system determines which shell to use
- Environment variables related to shells

**Key questions:**
- Why do different shells exist?
- How does the system decide which shell to run?
- What does `$SHELL` mean vs the current shell?
- Why might VS Code use a different shell than the system?

### 2. PATH System: Function and Configuration
**Learning objectives:**
- Understand what the PATH variable is and how it works
- Command search order in PATH
- Differences between system, user, and session PATH
- Best practices for modifying PATH

**Key questions:**
- How does the system find a command when you type it?
- Why is PATH order important?
- What’s the difference between temporary and permanent PATH?
- How do different shells affect PATH?

### 3. Shell Configuration Files
**Learning objectives:**
- Differences between `.bashrc`, `.bash_profile`, `.zshrc`, `.zprofile`
- Load order of configuration files
- When to use each file type
- Best practices for organizing configurations

**Key questions:**
- When is each configuration file executed?
- Why are there so many different files?
- How to avoid duplication in configurations?
- What settings belong in which file?

### 4. Package Managers and PATH
**Learning objectives:**
- How Homebrew, apt, yum, etc. affect PATH
- Strategies for handling multiple package managers
- Resolving version conflicts
- Best practices for development environments

**Key questions:**
- Why does Homebrew install to `/opt/homebrew` on Apple Silicon?
- How to avoid conflicts between system and Homebrew versions?
- What strategies exist to isolate environments?

### 5. Systematic Troubleshooting
**Learning objectives:**
- Methodology for diagnosing PATH issues
- Diagnostic tools (`which`, `whereis`, `type`, etc.)
- Step-by-step resolution strategies
- Preventing future issues

**Key questions:**
- What commands help diagnose PATH problems?
- How to check the current shell configuration?
- What to do when a command “should” be available but isn’t?

## 🔧 Solution Implemented (Reference)

### Resolution Steps
```bash
# 1. Switch to correct shell
exec zsh

# 2. Set PATH in .zshrc
echo 'export PATH="/opt/homebrew/bin:/opt/homebrew/sbin:$PATH"' >> ~/.zshrc

# 3. Reload configuration
source ~/.zshrc

# 4. Verification
which psql  # /opt/homebrew/bin/psql
which brew  # /opt/homebrew/bin/brew
```

### Solution Verification
```bash
# Check current shell
ps -p $$ -o comm=  # Should show: zsh

# Check PATH
echo $PATH | grep -o '/opt/homebrew/bin'  # Should appear

# Check commands
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT 1;"
# Should connect successfully
```

## 📝 Proposed Learning Tasks

### Task 1: Theoretical Research
- Read official bash and zsh documentation
- Research the history and evolution of Unix shells
- Understand the shell initialization process

### Task 2: Practical Experimentation
- Create a test environment with different shells
- Experiment with different PATH configurations
- Simulate and resolve similar issues

### Task 3: Best Practices
- Develop a personal shell configuration strategy
- Create reusable configuration scripts
- Document standard configurations for projects

## 🔗 Recommended Learning Resources

### Official Documentation
- [Bash Manual](https://www.gnu.org/software/bash/manual/)
- [Zsh Documentation](https://zsh.sourceforge.io/Doc/)
- [macOS Terminal User Guide](https://support.apple.com/guide/terminal/)

### Specific Tutorials
- PATH configuration best practices
- Shell scripting fundamentals
- Environment management strategies

## 💡 Final Reflections

This issue highlights the importance of understanding OS fundamentals in software development. Seemingly simple problems like “command not found” can have deep causes related to environment configuration.

**Lessons learned:**
1. Always check the current shell before assuming configurations
2. Understand the difference between system and user configuration
3. Document issues for future learning
4. Use a systematic methodology for troubleshooting

## 🧪 Proposed Practical Exercises

### Exercise 1: Shell Diagnosis
```bash
# Create a diagnostic script that reports:
# - Current vs configured shell
# - Full PATH and analysis
# - Existing configuration files
# - Available vs expected commands
```

### Exercise 2: Problem Simulation
```bash
# Simulate the original problem:
# 1. Temporarily switch to bash
# 2. Modify PATH to exclude Homebrew
# 3. Try running commands
# 4. Apply the solution step by step
```

### Exercise 3: Robust Configuration
```bash
# Create a configuration that works on:
# - Different shells (bash, zsh)
# - Different systems (macOS, Linux)
# - Different environments (VS Code, Terminal, SSH)
```

## 🔍 Useful Diagnostic Commands

### Shell Verification
```bash
# Currently running shell
ps -p $$ -o comm=

# Default configured shell
echo $SHELL

# Shells available on the system
cat /etc/shells

# Change default shell
chsh -s /bin/zsh
```

### PATH Analysis
```bash
# Display PATH in readable format
echo $PATH | tr ':' '\n' | nl

# Check if a directory is in PATH
echo $PATH | grep -q "/opt/homebrew/bin" && echo "Found" || echo "Not found"

# Find all versions of a command
which -a python
type -a python
```

### Configuration Files
```bash
# List shell configuration files
ls -la ~/.*rc ~/.*profile

# Check which files are loaded
# For zsh:
zsh -x -c 'exit' 2>&1 | grep -E '\.(zshrc|zprofile|zshenv)'

# For bash:
bash -x -c 'exit' 2>&1 | grep -E '\.(bashrc|bash_profile|profile)'
```

## 🎓 Recommended Learning Methodology

### Phase 1: Conceptual Understanding (1-2 hours)
1. **Guided reading** on shells and PATH
2. **Mind maps** of command search flow
3. **Reflection questions** for each concept

### Phase 2: Controlled Experimentation (2-3 hours)
1. **Test environment** in VM or container
2. **Practical exercises** simulating common issues
3. **Documentation** of each experiment and result

### Phase 3: Practical Application (1-2 hours)
1. **Optimized personal configuration**
2. **Automation scripts** for setup
3. **Troubleshooting** real problems

### Phase 4: Consolidation (30 minutes)
1. **Summary** of learned concepts
2. **Best practices checklist**
3. **Plan** to keep configurations up to date

## 📋 Robust Configuration Checklist

### ✅ Initial Setup
- [ ] Check system default shell
- [ ] Set PATH in the correct file (.zshrc vs .bash_profile)
- [ ] Include essential directories (/opt/homebrew/bin, ~/.local/bin)
- [ ] Avoid PATH duplication

### ✅ Tool Management
- [ ] Configure package managers (Homebrew, pip, npm)
- [ ] Configure version managers (nvm, pyenv, rbenv)
- [ ] Isolate development environments (conda, venv, docker)

### ✅ Troubleshooting Preparedness
- [ ] Create aliases for diagnostic commands
- [ ] Document custom configurations
- [ ] Keep backups of configuration files
- [ ] Test configurations in clean environments

## 🚀 Advanced Use Cases

### Multi-Environment Configuration
```bash
# .zshrc that works in development, CI/CD, and production
if [[ -d "/opt/homebrew/bin" ]]; then
    export PATH="/opt/homebrew/bin:/opt/homebrew/sbin:$PATH"
fi

if [[ -d "$HOME/.local/bin" ]]; then
    export PATH="$HOME/.local/bin:$PATH"
fi

# Detect environment and configure accordingly
if [[ -n "$CI" ]]; then
    # CI/CD configuration
elif [[ -n "$SSH_CONNECTION" ]]; then
    # SSH configuration
else
    # Local development configuration
fi
```

### Advanced Debugging
```bash
# Complete diagnostic script
#!/bin/bash
echo "=== SHELL ENVIRONMENT DIAGNOSTIC ==="
echo "Current shell: $(ps -p $$ -o comm=)"
echo "SHELL variable: $SHELL"
echo "PATH directories:"
echo $PATH | tr ':' '\n' | nl
echo "Missing common tools:"
for tool in psql brew python pip git; do
    which $tool >/dev/null || echo "  - $tool not found"
done
```

---

> **Note for future use**: This document should be used with an agent applying academic pedagogical methodology, providing detailed explanations, practical examples, and reflection opportunities for each concept. The agent should adapt the level of detail to the user's prior knowledge and provide practical exercises to consolidate learning.

